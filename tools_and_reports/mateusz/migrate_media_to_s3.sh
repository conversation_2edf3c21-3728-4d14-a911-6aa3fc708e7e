#!/bin/bash


# set credential

if [[ -z "${AWS_ACCESS_KEY_ID}" ]]; then
  echo "Set AWS_ACCESS_KEY_ID"
  exit
fi
if [[ -z "${AWS_SECRET_ACCESS_KEY}" ]]; then
  echo "Set AWS_SECRET_ACCESS_KEY"
  exit
fi
if [[ -z "${AWS_S3_REGION_NAME}" ]]; then
  echo "Set AWS_S3_REGION_NAME"
  exit
fi

aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
aws configure set region $AWS_S3_REGION_NAME

# sync catalogs

if [[ -z "${AWS_S3_MEDIA_BUCKET_NAME}" ]]; then
  echo "Set AWS_S3_MEDIA_BUCKET_NAME"
  exit
fi

if [[ -z "${MEDIA_DIR}" ]]; then
  echo "Set MEDIA_DIR, example: /home/<USER>/app/media  (without slash at the end)"
  exit
fi


#340K	checkout
aws s3 sync $MEDIA_DIR/checkout s3://$AWS_S3_MEDIA_BUCKET_NAME/media/checkout

#569M	custom_audiences
aws s3 sync $MEDIA_DIR/custom_audiences s3://$AWS_S3_MEDIA_BUCKET_NAME/media/custom_audiences

#312G	gallery
aws s3 sync $MEDIA_DIR/gallery s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery

#16G	invoice
aws s3 sync $MEDIA_DIR/invoice s3://$AWS_S3_MEDIA_BUCKET_NAME/media/invoice

#139M	items_for_render
aws s3 sync $MEDIA_DIR/items_for_render s3://$AWS_S3_MEDIA_BUCKET_NAME/media/items_for_render

#5.8G	logistic
aws s3 sync $MEDIA_DIR/logistic s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic

#165G	producers
aws s3 sync $MEDIA_DIR/producers s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers

#107G	product_feeds
aws s3 sync $MEDIA_DIR/product_feeds s3://$AWS_S3_MEDIA_BUCKET_NAME/media/product_feeds

#182M	production_margins
aws s3 sync $MEDIA_DIR/production_margins s3://$AWS_S3_MEDIA_BUCKET_NAME/media/production_margins

#40M	promotions
aws s3 sync $MEDIA_DIR/promotions s3://$AWS_S3_MEDIA_BUCKET_NAME/media/promotions

#7.7G	reviews
aws s3 sync $MEDIA_DIR/reviews s3://$AWS_S3_MEDIA_BUCKET_NAME/media/reviews

#9.7M	warehouse
aws s3 sync $MEDIA_DIR/warehouse s3://$AWS_S3_MEDIA_BUCKET_NAME/media/warehouse

# strange things - reviews
aws s3 sync $MEDIA_DIR/cache s3://$AWS_S3_MEDIA_BUCKET_NAME/media/cache
