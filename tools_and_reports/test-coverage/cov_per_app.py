import json
from collections import defaultdict

with open('../src/.tests_reports/coverage.json') as file:
    data = json.load(file)

cov_per_package = defaultdict(lambda: defaultdict(int))
for key, value in data.get('files', {}).items():
    package = key.split('/')
    if len(package) == 1:
        # omit out of packages files
        continue
    cov_per_package[package[0]]['num_statements'] += value['summary']['num_statements']
    cov_per_package[package[0]]['covered_lines'] += value['summary']['covered_lines']
    cov_per_package[package[0]]['missing_lines'] += value['summary']['missing_lines']
    cov_per_package[package[0]]['excluded_lines'] += value['summary']['excluded_lines']


for key, value in cov_per_package.items():
    percent = value['covered_lines'] / value['num_statements'] * 100
    print(f'{key} - {percent:.2f}% - missing lines: {value["missing_lines"]}')
