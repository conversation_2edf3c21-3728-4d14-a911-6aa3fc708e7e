import pathlib

import pandas as pd
import numpy as np

CODENAME_MATERIAL_PATH = pathlib.Path(
    pathlib.Path(__file__).parent.absolute(),
    'codename_materials.csv',
)


def get_conversion_coefficients(pricing_factors: pd.DataFrame) -> pd.Series:
    """
    Function extracts weight per unit coefficient for all codenames.

    Weights per unit are loaded from pricing factors given as argument. Values are
    cleaned and parsed. Empty cells are filled with value 0.0.
    """

    weight_per_unit = pricing_factors['weight_per_unit']
    conversion_coefficients = (
        weight_per_unit
        .fillna('0.0')
        .str.replace('[^0-9,.]', '')
        .str.replace(',', '.')
        .str.replace('^\s*$', '0.0')  # Fill empty cells
        .apply(pd.to_numeric)
    )
    if conversion_coefficients.isnull().values.any():
        raise ValueError(
            'Could not parse some values in weight_per_unit column in pricing factors.',
        )
    return conversion_coefficients


def calculate_ecotax(
    sales_report: pd.DataFrame,
    pricing_factors: pd.DataFrame,
):
    codename_materials = pd.read_csv(
        CODENAME_MATERIAL_PATH,
        index_col=False,
    )

    codenames_to_sum = set(codename_materials['codename'].unique())
    # Drop complaints
    sales_report = sales_report[~sales_report.paid_at.isnull()]
    # Filter rows only to the rows with codenames on the basis of which
    # we want to count ecotax
    sales_report = sales_report[sales_report['codename'].isin(codenames_to_sum)]

    # get only useful cols
    ecotax_data = sales_report[
        ['country', 'codename', 'product_id', 'sold_price', 'summary_quantity']
    ]

    conversion_coefficients = get_conversion_coefficients(pricing_factors)

    # multiply by correct coefficient to get correct weight
    ecotax_data['result_quantity'] = ecotax_data.apply(
        lambda row: (
            row['summary_quantity'] * conversion_coefficients[row['codename']]
        ),
        axis=1,
    )

    # Add information about the codename category (plastic, paper)
    ecotax_data = pd.merge(ecotax_data, codename_materials, how='left')

    # Drop unused columns
    ecotax_data = ecotax_data.drop(columns=['codename', 'summary_quantity'])

    # Sum quantity for same category in given furniture
    result_df = ecotax_data.groupby(
        ['country', 'material', 'sold_price', 'product_id'],
        as_index=False,
    )['result_quantity'].sum()

    # Aggregate information about used materials per country
    result_df = result_df.groupby(
        ['country', 'material'],
        as_index=False,
    ).agg({
            'product_id': np.size,
            'sold_price': np.sum,
            'result_quantity': np.sum,
    })

    # Pivot to move material information from row to column
    result_df = result_df.pivot(
        index=['country', 'product_id', 'sold_price'],
        columns='material',
        values='result_quantity',
    )
    result_df.reset_index(inplace=True)
    result_df.rename(
        columns={'product_id': 'count', 'sold_price': 'sum_price'},
        inplace=True,
    )

    return result_df
