import random
from collections import defaultdict
import requests
from requests.auth import HTTPBasicAuth

token = 'ATATT3xFfGF0FGuVurxRvGDoSA8ld7-3qQg9ec8c_vYgzByy6O6cWNnyJKpIum1P-zlCo8MD9WG0Am0ritDElzSEsAW7usLRrTZAKFH2DuadsIJ7BFguXBhEl2v8KKQcqxaLMDzBmv92lWfYlBlb2RlDXqHMzGYoVydsRvHNmi9Oq4tTBtYfWTM=C61C706A'
url = 'https://cstm-tasks.atlassian.net/rest/api/3/search/jql'
auth = HTTPBasicAuth('<EMAIL>', token)
headers = {
  'Accept': 'application/json'
}

payload = {
    'jql': 'project = "OP4" AND updated >= "2024-01-01" ORDER BY created DESC',
    'fieldsByKeys': True,
    'fields': ['*all'],
}

tasks = []
while True:
    response = requests.post(
       url,
       headers=headers,
       json=payload,
       auth=auth
    )

    for issue in response.json()['issues']:
        assignee = issue['fields'].get('assignee', {}) or {}
        tasks.append({
            'key': issue['key'],
            'summary': issue['fields']['summary'],
            'assignee': assignee.get('displayName', ''),
            'status': issue['fields']['status']['name'],
            'issuetype': issue['fields']['issuetype']['name'],
        })

    next_page_token = response.json().get('nextPageToken')
    if not next_page_token:
        break
    payload['nextPageToken'] = next_page_token


# filter only tickets with key > OP4-1000
tasks = [task for task in tasks if int(task['key'].split('-')[1]) > 1000]

# get the word between brackets
print([task['summary'].split('(', 1)[1].split(')')[0] for task in tasks if '(' in task['summary']])



from itertools import groupby
tasks.sort(key=lambda x: x['assignee'])
# count number of tasks done by each assignee
Counter(task['assignee'] for task in tasks if task['status'] == 'Done')

tasks_by_assignee = defaultdict(lambda: defaultdict(list))
for key, group in groupby(tasks, key=lambda x: x['assignee']):
    for task in group:
        if task['status'] == 'Done':
            # print(task['key'], task['summary'], task['status'], task['issuetype'])
            # print(task['summary'])
            tasks_by_assignee[key][task['issuetype']].append(task['summary'])

for assignee, person_tasks in tasks_by_assignee.items():
    print(assignee)
    report = []
    for person_epic in person_tasks.get('Epic', []):
        report.append(person_epic.split(' ', 1)[1])
    if len(report) < 4:
        additional_count = min(4 - len(report), len(person_tasks.get('Task', [])))
        report.extend(random.sample(person_tasks.get('Task', []), additional_count))
    print('\n'.join(report))
    print()

