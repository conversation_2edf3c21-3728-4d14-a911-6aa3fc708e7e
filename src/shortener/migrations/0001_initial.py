# -*- coding: utf-8 -*-
# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RedirectedUserInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('actual_url', models.Char<PERSON>ield(max_length=1024)),
                (
                    'param_network',
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    'param_campaign',
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    'param_adgroup',
                    models.Char<PERSON>ield(blank=True, max_length=512, null=True),
                ),
                ('param_term', models.Char<PERSON>ield(blank=True, max_length=512, null=True)),
                ('time', models.DateTimeField()),
                ('ip', models.<PERSON>r<PERSON><PERSON>(max_length=45)),
                ('referer', models.CharField(blank=True, max_length=1024, null=True)),
                ('language', models.CharField(blank=True, max_length=4, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('installed_app', models.BooleanField(db_index=True, default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ShortUrl',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(db_index=True, max_length=64, unique=True)),
                ('description', models.CharField(max_length=128)),
                ('url', models.CharField(max_length=1024)),
                (
                    'redirect_type',
                    models.PositiveSmallIntegerField(
                        choices=[(301, 'Permanent'), (302, 'Temporary')]
                    ),
                ),
                (
                    'url_mobile',
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    'url_android',
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                ('url_ios', models.CharField(blank=True, max_length=1024, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='redirecteduserinfo',
            name='short_url',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='shortener.ShortUrl'
            ),
        ),
        migrations.AddField(
            model_name='redirecteduserinfo',
            name='user',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
