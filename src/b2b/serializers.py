from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from rest_framework import serializers

from b2b.models import B2bApplicant
from custom.utils.parameter_mappers import map_keys_in_dict
from vouchers.utils import (
    create_b2b_vouchers,
    create_free_sample_b2b_voucher,
)


class B2bApplicantSerializer(serializers.ModelSerializer):
    class Meta:
        model = B2bApplicant
        fields = (
            'marketing_permission',
            'language',
            'job_other',
            'why_tylko',
            'first_name',
            'last_name',
            'email',
            'departament',
            'company_name',
            'country',
            'vat_number',
            'website',
            'free_sample_code',
            'promo_code',
        )
        read_only_fields = (
            'free_sample_code',
            'promo_code',
        )

    @transaction.atomic
    def create(self, validated_data: dict) -> B2bApplicant:
        validated_data['language'] = getattr(
            self.context['request'],
            'LANGUAGE_CODE',
            settings.LANGUAGE_CODE,
        )
        b2b_code = create_b2b_vouchers(quantity=1, for_email=validated_data['email'])[0]
        sample_voucher = create_free_sample_b2b_voucher(validated_data['email'])
        validated_data['promo_code'] = b2b_code
        validated_data['free_sample_code'] = sample_voucher.code
        return super().create(validated_data)

    def validate_marketing_permission(self, marketing_permission: bool) -> bool:
        if not marketing_permission:
            raise serializers.ValidationError(_('Accept marketing permissions.'))
        return marketing_permission


class PipedriveOrganisationSerializer(serializers.ModelSerializer):
    class Meta:
        model = B2bApplicant
        fields = (
            'company_name',
            'vat_number',
            'website',
        )

    def to_representation(self, instance):
        result = map_keys_in_dict(
            data=super().to_representation(instance),
            fields_mapping=self.keys_to_representation,
        )
        result['label'] = 'Form'
        return result

    @property
    def keys_to_representation(self):
        return {
            'name': 'company_name',
            '47ecb293dc1c956558db809ac1fc3f3f097905b3': 'vat_number',
            '3d823ddd026370bcaf372f70a0be079aa8732436': 'website',
        }


class PipedrivePersonSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    marketing_status = serializers.SerializerMethodField()
    language = serializers.SerializerMethodField()

    class Meta:
        model = B2bApplicant
        fields = (
            'name',
            'email',
            'marketing_status',
            'departament',
            'job_other',
            'why_tylko',
            'country',
            'language',
            'free_sample_code',
            'promo_code',
        )

    def get_name(self, instance: B2bApplicant) -> str:
        return f'{instance.first_name} {instance.last_name}'

    def get_language(self, instance: B2bApplicant) -> str:
        return instance.language.upper()

    def get_marketing_status(self, instance: B2bApplicant) -> str:
        return 'subscribed' if instance.marketing_permission else 'no_consent'

    def to_representation(self, instance: B2bApplicant):
        result = map_keys_in_dict(
            data=super().to_representation(instance),
            fields_mapping=self.keys_to_representation,
        )
        result['6fec656cc211182ee3c44c304ce656eb326d4a47'] = 'T4B Form'
        result['label'] = 'Form'
        return result

    @property
    def keys_to_representation(self) -> dict[str, str]:
        return {
            '5c41dc0f45f39c3371f54b3a0a29229479318354': 'departament',
            '9d8b93b9cec6cf6ccc4df2fcbbb20bf78bc58c6a': 'job_other',
            '64b2530ccc690843ba99fc01de56de5292788895': 'why_tylko',
            '9229cc86c38039081ac479fc4ca811c147f89aab': 'country',
            'd94a545c1589960510752ba78fa59c2678eb2c20': 'language',
            'e3f04c39eb8c4ff48d2496145ec2025186ac11a4': 'promo_code',
            '56f3ef9d6512729755dcc9eb4c88d8d04b4fe388': 'free_sample_code',
        }
