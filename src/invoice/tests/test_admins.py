from django.urls import reverse

from invoice.choices import InvoiceStatus


def test_invoice_admin_num_queries(
    django_assert_max_num_queries,
    admin_client,
    order,
    invoice_factory,
):
    invoice_factory.create_batch(5, order=order, status=InvoiceStatus.PROFORMA)

    with django_assert_max_num_queries(42):
        url = reverse('admin:invoice_invoice_changelist')
        response = admin_client.get(url)

        assert response.status_code == 200
