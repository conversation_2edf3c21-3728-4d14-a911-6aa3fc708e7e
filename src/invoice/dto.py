from datetime import datetime
from decimal import Decimal
from typing import (
    List,
    Optional,
)

from pydantic.dataclasses import dataclass as pydantic_dataclass


@pydantic_dataclass
class InvoiceItemCachedDTO:
    item_name: str
    quantity: int
    net_price: Decimal
    discount_value: Decimal
    vat_status: int
    net_value: Decimal
    vat_rate: Decimal
    vat_amount: Decimal
    net_weight: Decimal
    gross_weight: Decimal
    gross_price: Decimal
    item_type: int
    correcting_invoice_item_ids: List[int]
    net_price_in_pln: Decimal
    discount_value_in_pln: Decimal
    gross_price_in_pln: Decimal
    vat_amount_in_pln: Decimal
    net_value_in_pln: Decimal
    exchange_rate: Decimal
    recycle_tax_value: Decimal = Decimal('0.00')
    exchange_date: Optional[datetime] = None
    hts_code: Optional[str] = ''
    item_material: Optional[str] = ''
    item_dimensions: Optional[str] = ''
    corrected_invoice_item: Optional['InvoiceItemCachedDTO'] = None

    def correction_quantity(self) -> int:
        if self.corrected_invoice_item is None:
            return self.quantity
        return self.quantity - self.corrected_invoice_item.quantity

    def correction_net_price(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.net_price
        return self.net_price - self.corrected_invoice_item.net_price

    def correction_discount_value(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.discount_value
        return self.discount_value - self.corrected_invoice_item.discount_value

    def correction_net_value(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.net_value
        return self.net_value - self.corrected_invoice_item.net_value

    def correction_vat_amount(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.vat_amount
        return self.vat_amount - self.corrected_invoice_item.vat_amount

    def correction_gross_price(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.gross_price
        return self.gross_price - self.corrected_invoice_item.gross_price

    def correction_recycle_tax_value(self) -> Decimal:
        if self.corrected_invoice_item is None:
            return self.recycle_tax_value
        return self.recycle_tax_value - self.corrected_invoice_item.recycle_tax_value
