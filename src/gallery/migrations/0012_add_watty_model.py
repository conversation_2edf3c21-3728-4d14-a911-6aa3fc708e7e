# Generated by Django 1.11.24 on 2020-10-06 11:37
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import sorl.thumbnail.fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('gallery', '0011_new_sample_box_sets'),
    ]

    operations = [
        migrations.CreateModel(
            name='Watty',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'furniture_status',
                    models.IntegerField(
                        choices=[
                            (0, 'draft'),
                            (1, 'saved'),
                            (2, 'ordered'),
                            (3, 'shared'),
                            (4, 'special'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'placeholder_item',
                    models.BooleanField(
                        default=False,
                        help_text='Item used as placeholder for older orders',
                    ),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False,
                        help_text='Item has been customized manually',
                    ),
                ),
                (
                    'walls',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'backs',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'slabs',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'frame',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'bars',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'drawers',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'doors_exterior',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'hinges',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                (
                    'masking_bars',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=list
                    ),
                ),
                ('dna_preset', models.IntegerField(blank=True, null=True)),
                ('dna_object_id', models.IntegerField(blank=True, null=True)),
                (
                    'configurator_params',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=dict
                    ),
                ),
                ('material', models.IntegerField(default=0)),
                (
                    'pattern',
                    models.IntegerField(default=0, help_text='0 - Typical column one'),
                ),
                (
                    'dna_name',
                    models.CharField(
                        blank=True,
                        default='',
                        max_length=100,
                    ),
                ),
                (
                    'item_category',
                    models.CharField(blank=True, default='', max_length=150),
                ),
                (
                    'item_type',
                    models.IntegerField(default=0, help_text='0 - Wardrobe MLP'),
                ),
                ('width', models.IntegerField()),
                ('height', models.IntegerField()),
                ('depth', models.IntegerField()),
                (
                    'grid_all_colors',
                    models.FileField(blank=True, null=True, upload_to='grid_images'),
                ),
                (
                    'grid_all_colors_webp',
                    models.FileField(blank=True, null=True, upload_to='grid_images'),
                ),
                (
                    'configurator_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'ROW'),
                            (2, 'COLUMN'),
                        ],
                        default=2,
                    ),
                ),
                (
                    'physical_product_version',
                    models.PositiveSmallIntegerField(
                        choices=[(1, 'TREX'), (2, 'RAPTOR')], default=1
                    ),
                ),
                (
                    'preview',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file',
                    ),
                ),
                (
                    'price',
                    models.FloatField(default=0, verbose_name='Furniture price'),
                ),
                (
                    'title',
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                ('description', models.TextField(blank=True, null=True)),
                (
                    'preview_updated_at',
                    models.DateTimeField(blank=True, null=True),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                ('preset', models.BooleanField(db_index=True, default=False)),
                (
                    'preset_initial_state',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_mobile',
                    models.BooleanField(db_index=True, default=False),
                ),
                (
                    'preset_visible_web',
                    models.BooleanField(db_index=True, default=False),
                ),
                ('preset_order', models.IntegerField(default=0)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('grid_preset', models.IntegerField(blank=True, null=True)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                        ],
                        default=15,
                    ),
                ),
                ('category', models.IntegerField(default=0)),
                (
                    'price_custom',
                    models.DecimalField(decimal_places=2, default=0, max_digits=7),
                ),
                (
                    'preview_cover',
                    sorl.thumbnail.fields.ImageField(
                        blank=True,
                        null=True,
                        upload_to='gallery_images',
                        verbose_name='Preview file for ideas in app',
                    ),
                ),
                ('is_bestseller', models.BooleanField(default=False)),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='owner of shelf',
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ('shelf_type', models.PositiveSmallIntegerField(default=3)),
            ],
            options={
                'verbose_name': 'Type03 configuration',
                'verbose_name_plural': 'Type03 configurations',
            },
        ),
    ]
