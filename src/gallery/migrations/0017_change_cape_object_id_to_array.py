import django.contrib.postgres.fields

from django.db import (
    migrations,
    models,
)


def fill_temp_cape_id(apps, schema_editor):
    CustomDna = apps.get_model('gallery', 'CustomDna')
    for custom_dna in CustomDna.objects.all():
        custom_dna.cape_object_id_temp = [custom_dna.cape_object_id]
        custom_dna.save()


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0016_added_interal_source_for_items'),
    ]

    operations = [
        migrations.AddField(
            model_name='customdna',
            name='cape_object_id_temp',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.IntegerField(), blank=True, default=list, size=None
            ),
        ),
        migrations.RunPython(
            fill_temp_cape_id,
            migrations.RunPython.noop,
            elidable=True,
        ),
        migrations.RemoveField(
            model_name='customdna',
            name='cape_object_id',
        ),
        migrations.RenameField(
            model_name='customdna',
            old_name='cape_object_id_temp',
            new_name='cape_object_id',
        ),
        migrations.AddField(
            model_name='customdna',
            name='dna_version',
            field=models.SmallIntegerField(
                choices=[(0, 'BOTH'), (1, 'VERSION1'), (2, 'VERSION2')],
                default=0,
                blank=True,
            ),
        ),
    ]
