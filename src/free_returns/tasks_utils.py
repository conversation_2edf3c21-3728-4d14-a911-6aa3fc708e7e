import logging

from free_returns.internal_api.clients import (
    CheckTNTDeliveredDateByTrackingNumberAPIClient,
)

logger = logging.getLogger('cstm')


def check_tnt_orders_and_set_date(tnt_orders):
    tnt_tracking_service = CheckTNTDeliveredDateByTrackingNumberAPIClient()

    for tnt_order in tnt_orders:
        delivered_date = None

        try:  # don't block task if something happen with one item
            delivered_date = tnt_tracking_service.get_delivery_date(
                tnt_order.tracking_number
            )
        except Exception as e:
            logger.error(e)
        if delivered_date:
            tnt_order.finished_at = delivered_date
            tnt_order.save(update_fields=['finished_at'])
