import itertools

from collections import deque

from django.db.models import Q

from catalogue.constants.merchandising_config import (
    BEDSIDE_TABLE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    BOOKCASE_ATTRIBUTES_NOT_SHARED_BY_<PERSON>IGHBOURS,
    CHEST_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    DESK_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    SHOERACK_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    SIDEBOARD_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    SOFA_CATEGORY_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    TVSTAND_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    VINYL_STORAGE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
    WALLSTORAGE_ATTRIBUTES_NOT_SHARED_BY_<PERSON>IGHBOURS,
    WARDROBE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS,
)
from catalogue.enums import Color<PERSON>roup
from catalogue.models import CatalogueEntry
from catalogue.services.automatic_merchandising.catalogue_stats import (
    EdgeWardrobeCategoryStats,
)
from catalogue.services.automatic_merchandising.colors_service import (
    ColorService,
    ColorsGetter,
)
from catalogue.services.automatic_merchandising.filters_getters import (
    CategoryFiltersGetter,
)
from custom.enums import ShelfType
from gallery.enums import FurnitureCategory


class BedsideTableFiltersGetter(CategoryFiltersGetter):
    def get_height_filter(self) -> Q:
        return Q()

    @property
    def not_shared_features(self) -> set:
        return BEDSIDE_TABLE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=2),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class BookcaseFiltersGetter(CategoryFiltersGetter):
    def get_width_filter(self) -> Q:
        return Q()

    @property
    def not_shared_features(self) -> set:
        return BOOKCASE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=2),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class ChestFiltersGetter(CategoryFiltersGetter):
    def get_height_filter(self) -> Q:
        return Q()

    @property
    def not_shared_features(self) -> set:
        return CHEST_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=1),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class DeskFiltersGetter(CategoryFiltersGetter):
    @staticmethod
    def _get_new_colors_mode() -> bool:
        return False

    def _should_different_shelf_type_be_used(self) -> bool:
        # Desk category does not have full TYPE02 representation
        return False

    @property
    def not_shared_features(self) -> set:
        return DESK_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            color_group: deque(maxlen=0)
            for color_group in ColorGroup.__members__.values()
        }

    def get_height_filter(self) -> Q:
        return Q()

    def get_width_filter(self) -> Q:
        return Q()

    def _get_second_stage_filters(self) -> Q:
        return Q(
            self.get_colors_filter(self._get_same_colors)
            & self.get_excluded_duplicated_variants_filter()
        )


class ShoerackFiltersGetter(CategoryFiltersGetter):
    def get_width_filter(self) -> Q:
        return Q()

    @property
    def not_shared_features(self) -> set:
        return SHOERACK_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=1),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class SideboardFiltersGetter(CategoryFiltersGetter):
    @property
    def not_shared_features(self) -> set:
        return SIDEBOARD_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    def get_width_filter(self) -> Q:
        return Q()

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=2),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class TvStandFiltersGetter(CategoryFiltersGetter):
    @property
    def not_shared_features(self) -> set:
        return TVSTAND_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    def get_height_filter(self) -> Q:
        return Q()

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=2),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class VinylStorageFiltersGetter(CategoryFiltersGetter):
    @staticmethod
    def _get_new_colors_mode() -> bool:
        return False

    @staticmethod
    def _get_color_cycle() -> itertools.cycle:
        # Vinyl storages have only one mild color - Dusty Pink, so the cycle is adjusted
        return itertools.cycle(
            [
                ColorGroup.NUDE,
                ColorGroup.MILD,
                ColorGroup.BOLD,
                ColorGroup.NUDE,
                ColorGroup.BOLD,
            ]
        )

    def _should_different_shelf_type_be_used(self) -> bool:
        # Vinyl Storage has only TYPE01
        return False

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=0),
            ColorGroup.BOLD: deque(maxlen=3),
        }

    def get_esthetic_filters(self, category: str = None) -> Q:
        return Q(
            # pink and red are allowed to be neighbours
            self.get_colors_filter(self._get_same_colors)
            & self.get_excluded_duplicated_variants_filter()
            & self.get_excluded_features_filter()
            & self.get_height_filter()
            & self.get_width_filter()
        )

    @property
    def not_shared_features(self) -> set:
        return VINYL_STORAGE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_previous_entries_widths_deque() -> deque:
        return deque(maxlen=3)

    @staticmethod
    def _get_previous_entries_heights_deque() -> deque:
        return deque(maxlen=3)


class WallStorageFiltersGetter(CategoryFiltersGetter):
    def get_height_filter(self) -> Q:
        return Q()

    @staticmethod
    def _get_previous_entries_widths_deque() -> deque:
        return deque(maxlen=5)

    @property
    def not_shared_features(self) -> set:
        return WALLSTORAGE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=2),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class WardrobeFiltersGetter(CategoryFiltersGetter):
    @staticmethod
    def _get_new_colors_mode() -> bool:
        return False

    @property
    def not_shared_features(self) -> set:
        return WARDROBE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    def get_height_filter(self) -> Q:
        return Q()


class EdgeWardrobeFiltersGetter(CategoryFiltersGetter):
    def __init__(self, category: FurnitureCategory):
        super().__init__(category=category)
        self.height_ranges = EdgeWardrobeCategoryStats(self.category).height_ranges
        self.width_ranges = EdgeWardrobeCategoryStats(self.category).width_ranges
        self.previous_entries_heights = self._get_previous_entries_heights_deque()
        self.previous_entries_widths = self._get_previous_entries_widths_deque()

    @staticmethod
    def _get_new_colors_mode() -> bool:
        return False

    @property
    def not_shared_features(self) -> set:
        return WARDROBE_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    def get_height_filter(self) -> Q:
        return Q()

    def get_width_filter(self) -> Q:
        return Q()

    def _should_different_shelf_type_be_used(self) -> bool:
        return False

    def _get_color_group_map(self):
        color_service = ColorService(self.category)
        color_service.available_shelf_types = {ShelfType.TYPE13}
        return color_service.edge_color_group_map

    def _update_previous_entries_data(self, entry: CatalogueEntry) -> None:
        entry_data = {
            'order': self.order,
            'bad_neighbours': ColorsGetter(
                entry.shelf_type,
                entry.material,
            ).get_bad_neighbours_for_edge_wardrobes(),
            'same_colors': ColorsGetter(
                entry.shelf_type,
                entry.material,
            ).get_same_colors(self.category),
            'features': set(
                entry.attributes.filter(name__in=self.not_shared_features)
                .values_list('name', flat=True)
                .distinct()
            ),
        }
        self.previous_entries_data.appendleft(entry_data)

    @staticmethod
    def _get_last_colors_from_group_dict() -> dict[ColorGroup, deque]:
        return {
            ColorGroup.NUDE: deque(maxlen=1),
            ColorGroup.MILD: deque(maxlen=1),
            ColorGroup.BOLD: deque(maxlen=2),
        }


class SofaFiltersGetter(CategoryFiltersGetter):
    @property
    def not_shared_features(self) -> set:
        return SOFA_CATEGORY_ATTRIBUTES_NOT_SHARED_BY_NEIGHBOURS

    def get_height_filter(self) -> Q:
        return Q()

    def get_width_filter(self) -> Q:
        return Q()

    def _should_different_shelf_type_be_used(self) -> bool:
        return False
