from typing import Iterable

import factory.django
import factory.fuzzy


class CatalogueEntryFactory(factory.django.DjangoModelFactory):
    order = factory.Sequence(int)
    category_order = factory.Sequence(int)
    furniture = factory.SubFactory(
        'gallery.tests.factories.JettyFactory',
    )
    shelf_type = 0
    material = 0
    product_unreal_image_webp = factory.django.ImageField()
    product_unreal_thumbnail_image_webp = factory.django.ImageField()
    width = factory.fuzzy.FuzzyInteger(30, 240)
    enabled = True

    class Meta:
        model = 'catalogue.CatalogueEntry'

    @factory.post_generation
    def attributes(self, create, tags: Iterable[str], **kwargs):
        if not create or not tags:
            return

        self.attributes.add(*tags)


class BoardManualOrderFactory(factory.django.DjangoModelFactory):
    entry = factory.SubFactory(CatalogueEntryFactory)
    board_name = ''
    order = factory.Sequence(lambda n: n)
    published = True

    class Meta:
        model = 'catalogue.BoardManualOrder'
