import json

from django.core.cache import cache
from django.core.paginator import (
    InvalidPage,
    Page,
    Paginator,
)

from rest_framework.exceptions import NotFound
from rest_framework.pagination import PageNumberPagination

PARAMS_LIMIT_FOR_CACHING = 4


class CachedByParamsPaginator(Paginator):
    def __init__(self, *args, cache_key, query_params, cache_timeout=60, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_key = cache_key
        self.cache_timeout = cache_timeout
        self.query_params = query_params

    def page(self, number):
        number = self.validate_number(number)
        params_count = 0
        for param_value in self.query_params.values():
            params_count += len(param_value.split(','))
        if params_count > PARAMS_LIMIT_FOR_CACHING or number != 1:
            return super().page(number)
        cached_objects = cache.get(self.generate_cache_key())
        if cached_objects:
            return Page(
                object_list=cached_objects,
                number=number,
                paginator=self,
            )
        page = super().page(number)
        cache.set(
            self.generate_cache_key(),
            page.object_list,
            self.cache_timeout,
        )
        return page

    def generate_cache_key(self):
        return f'{self.cache_key}:{json.dumps(self.query_params)}'


class CatalogueBasicPagination(PageNumberPagination):
    page_size = 32
    page_size_query_param = 'pageSize'
    max_page_size = 100


class CatalogueCachedPagination(CatalogueBasicPagination):
    def paginate_queryset(self, queryset, request, view=None):
        """
        This is all copied from parent class in order to pass query params to paginator
        """
        page_size = self.get_page_size(request)
        if not page_size:
            return None

        paginator = CachedByParamsPaginator(
            queryset,
            page_size,
            cache_key='catalogue_entry',
            cache_timeout=60 * 10,
            query_params=request.query_params,
        )
        page_number = request.query_params.get(self.page_query_param, 1)
        if page_number in self.last_page_strings:
            page_number = paginator.num_pages

        try:
            self.page = paginator.page(page_number)
        except InvalidPage as exc:
            msg = self.invalid_page_message.format(
                page_number=page_number, message=str(exc)
            )
            raise NotFound(msg)

        if paginator.num_pages > 1 and self.template is not None:
            # The browsable API should display pagination controls.
            self.display_page_controls = True
        self.request = request
        return list(self.page)
