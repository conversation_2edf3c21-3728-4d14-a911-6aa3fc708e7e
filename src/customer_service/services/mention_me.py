import csv
import dataclasses
import datetime
import logging
import operator

from operator import attrgetter
from typing import (
    Iterable,
    Optional,
    Union,
)

from django.contrib.auth.models import User
from django.db.models import QuerySet
from django.urls import reverse

from custom.enums import Furniture
from custom.internal_api.dto import LogisticOrderDTO
from custom.utils.slack import notify_about_new_b2b_reward
from customer_service import enums
from customer_service.correction_request_strategies import (
    CorrectionRequestAmountStrategy,
)
from customer_service.enums import CSCorrectionRequestStatus
from customer_service.models import (
    CSCorrectionRequest,
    MentionMeB2BReward,
    MentionMeFile,
    MentionMeInfo,
    RefundInfo,
    RefundSetting,
)
from customer_service.services import errors
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from orders.enums import OrderStatus
from orders.models import Order

logger = logging.getLogger('cstm')


B2B_CAMPAIGN_KEYWORD = 'Designer'


@dataclasses.dataclass
class MentionMeInfoData:
    referee_email: str
    referer_email: str
    amount: int
    currency_code: str
    campaign_name: str


class LoadMentionMeFile:
    V1 = 'v1'
    V2 = 'v2'

    V1_HEADER_ROW = 2
    V2_HEADER_ROW = 0

    HEADERS = {
        'referee_email': {V1: 'Referee Email', V2: 'RefereeEmail'},
        'referrer_email': {V1: 'Referrer Email', V2: 'ReferrerEmail'},
        'reward_amount': {V1: 'Reward Amount', V2: 'RewardAmount'},
        'reward_currency': {V1: 'Reward Currency', V2: 'RewardCurrency'},
        'campaign_name': {V1: 'Campaign Name', V2: 'CampaignName'},
    }

    def __init__(self, mention_me_file: MentionMeFile):
        self.mention_me_file = mention_me_file
        self.csv_lines = mention_me_file.file_content.splitlines()
        self.version = self.get_version()

    def load_mention_me_file_to_db(self) -> None:
        refunds_data = self.load_mention_me_data()
        self.create_mention_me_info_objects(refunds_data)
        self.mention_me_file.parsed = True
        self.mention_me_file.save(update_fields=['parsed'])

    def load_mention_me_data(self) -> list[MentionMeInfoData]:
        reader = self.get_dict_reader()
        return [
            MentionMeInfoData(
                referee_email=row[self.get_header('referee_email')],
                referer_email=row[self.get_header('referrer_email')],
                amount=row[self.get_header('reward_amount')],
                currency_code=row[self.get_header('reward_currency')],
                campaign_name=row[self.get_header('campaign_name')],
            )
            for row in reader
        ]

    def create_mention_me_info_objects(
        self,
        mention_me_infos: list[MentionMeInfoData],
    ) -> None:
        for mention_me_info in mention_me_infos:
            data = dict(
                referer_email=mention_me_info.referer_email,
                referee_email=mention_me_info.referee_email,
                defaults={
                    'amount': mention_me_info.amount,
                    'currency_code': mention_me_info.currency_code,
                    'origin_file': self.mention_me_file,
                    'campaign_name': mention_me_info.campaign_name,
                },
            )
            if B2B_CAMPAIGN_KEYWORD in mention_me_info.campaign_name:
                MentionMeB2BRewardService().update_or_create(**data)
            else:
                RefundInfoService().update_or_create(**data)

    def get_version(self) -> str:
        v1_headers = set(header[self.V1] for header in self.HEADERS.values())
        v2_headers = set(header[self.V2] for header in self.HEADERS.values())

        if v1_headers.issubset(self.get_row_set(self.csv_lines[self.V1_HEADER_ROW])):
            return self.V1
        elif v2_headers.issubset(self.get_row_set(self.csv_lines[self.V2_HEADER_ROW])):
            return self.V2
        else:
            raise errors.WrongMentionMeFileHeader('Mention Me file wrong headers')

    @classmethod
    def get_row_set(cls, row: str) -> set:
        return set(row.replace('"', '').split(','))

    def get_dict_reader(self):
        header_row = (
            self.V1_HEADER_ROW if self.version == self.V1 else self.V2_HEADER_ROW
        )
        return csv.DictReader(self.csv_lines[header_row:])

    def get_header(self, header_name):
        return self.HEADERS[header_name][self.version]


class MentionMeBaseService:
    def __init__(self):
        self.currency_to_settings = self.get_settings_dict()

    @classmethod
    def get_settings_dict(cls) -> dict:
        refund_settings = RefundSetting.objects.select_related('currency')
        if not refund_settings.exists():
            raise errors.RefundSettingDoesNotExist('Create refund settings!!!')
        return {
            refund_setting.currency.symbol: refund_setting
            for refund_setting in refund_settings
        }

    @classmethod
    def get_orders_matching_email(cls, email: str) -> Union[QuerySet, Iterable[Order]]:
        return Order.objects.filter_with_proxy_by_email(email=email).filter(
            status__in=[
                OrderStatus.IN_PRODUCTION,
                OrderStatus.SHIPPED,
                OrderStatus.DELIVERED,
                OrderStatus.TO_BE_SHIPPED,
            ]
        )

    @classmethod
    def get_orders_without_free_return(cls, orders: list[Order]) -> list[Order]:
        orders_without_free_return = []
        for order in orders:
            if not order.items.filter(free_return__isnull=False).exists():
                orders_without_free_return.append(order)
        return orders_without_free_return

    def get_orders_with_high_enough_invoices(
        self, orders: Union[QuerySet, Iterable[Order]]
    ) -> Union[QuerySet, Iterable[Order]]:
        invoices = [o.get_invoices().order_by('-created_at').first() for o in orders]
        high_enough_orders = []
        for invoice in invoices:
            if not invoice:
                continue
            setting = self.currency_to_settings.get(invoice.currency_symbol, None)
            if not setting:
                raise errors.NoSettingsForCurrency(
                    f'No refund setting for {invoice.currency_symbol}'
                )
            if invoice.get_total_gross() > setting.min_order_value:
                high_enough_orders.append(invoice.order)
        return high_enough_orders


class RefundInfoService(MentionMeBaseService):
    MAX_REFUND_NO = 10
    DAYS_FROM_DELIVERY = 100

    def update_or_create(self, referer_email, referee_email, **kwargs):
        refund_info, created = RefundInfo.objects.get_or_create(
            referer_email=referer_email, referee_email=referee_email, **kwargs
        )
        self.update(refund_info)

    def update(self, refund_info: RefundInfo) -> RefundInfo:
        if refund_info.status in [
            enums.RefundStatus.REFUNDED,
            enums.RefundStatus.READY_TO_REFUND,
        ]:
            return refund_info
        refund_info.status = enums.RefundStatus.CORRECT_ORDER_NOT_FOUND
        if (
            RefundInfo.objects.filter(referer_email=refund_info.referer_email).count()
            >= self.MAX_REFUND_NO
        ):
            refund_info.referee_order_info = enums.RefereeOrderInfo.TO_MANY_REFUNDS
            refund_info.save()
            return refund_info

        referer_order = self.get_referer_correct_order(refund_info.referer_email)
        if not referer_order:
            refund_info.referee_order_info = (
                enums.RefereeOrderInfo.INCORRECT_REFERER_ORDER
            )
            refund_info.save()
            return refund_info

        refund_info.referer_order = referer_order
        referee_orders = self.get_referee_order_and_set_order_info(refund_info)
        if not referee_orders:
            refund_info.save()
            return refund_info

        last_logistic_order = self.get_last_delivered_logistic_order(referee_orders)
        order = Order.objects.filter(pk__in=[order.id for order in referee_orders]).get(
            serialized_logistic_info__contains=[{'id': last_logistic_order.id}]
        )
        refund_info.referee_order = order

        if self.is_enough_days_from_delivery(last_logistic_order):
            refund_info.referee_order_info = (
                enums.RefereeOrderInfo.NOT_ENOUGH_DAYS_FROM_DELIVERY
            )
            refund_info.status = enums.RefundStatus.WAITING
        else:
            refund_info.referee_order_info = enums.RefereeOrderInfo.OK
            refund_info.status = enums.RefundStatus.READY_TO_REFUND
        refund_info.save()
        return refund_info

    def get_referee_order_and_set_order_info(self, mention_me_info: MentionMeInfo):
        orders = self.get_orders_matching_email(mention_me_info.referee_email)
        if not orders:
            mention_me_info.referee_order_info = (
                enums.RefereeOrderInfo.NOT_MATCHED_ORDER
            )
            return []

        delivered_orders = [o for o in orders if o.status == OrderStatus.DELIVERED]
        if not delivered_orders:
            mention_me_info.referee_order_info = (
                enums.RefereeOrderInfo.NOT_DELIVERED_YET
            )
            return []

        not_sample_orders = [
            o
            for o in delivered_orders
            if o.cached_items_type != Furniture.sample_box.value
        ]
        if not not_sample_orders:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.ONLY_SAMPLES
            return []

        orders_without_free_return = self.get_orders_without_free_return(
            not_sample_orders
        )
        if not orders_without_free_return:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.RETURN
            return []

        correct_orders = self.get_orders_with_high_enough_invoices(not_sample_orders)
        if not correct_orders:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.TO_HIGH_REFUNDS
            return []

        return correct_orders

    @classmethod
    def is_enough_days_from_delivery(
        cls, last_logistic_order: 'LogisticOrderDTO', days: int = DAYS_FROM_DELIVERY
    ) -> bool:
        return (
            last_logistic_order
            and last_logistic_order.delivered_date
            and datetime.timedelta(days=days)
            > datetime.datetime.now().date() - last_logistic_order.delivered_date
        )

    @classmethod
    def get_last_delivered_logistic_order(cls, correct_orders) -> 'LogisticOrderDTO':
        logistic_orders = []
        for order in correct_orders:
            logistic_orders.extend(order.logistic_info)

        logistic_orders = sorted(
            [
                logistic_order
                for logistic_order in logistic_orders
                if logistic_order.delivered_date
            ],
            key=operator.attrgetter('delivered_date'),
            reverse=True,
        )
        return logistic_orders[-1]

    def get_referer_correct_order(self, referee_email) -> Order:
        orders = self.get_orders_matching_email(referee_email).exclude(
            cached_items_type=Furniture.sample_box.value
        )
        orders = self.get_orders_with_high_enough_invoices(orders)
        return orders[0] if orders else None


class MentionMeB2BRewardService(MentionMeBaseService):
    DAYS_FROM_ORDER = 60

    def update_or_create(self, referer_email, referee_email, **kwargs):
        b2b_reward, created = MentionMeB2BReward.objects.update_or_create(
            referer_email=referer_email, referee_email=referee_email, **kwargs
        )
        self.update(b2b_reward)
        if created:
            self.notify_slack_about_new_b2b_reward(b2b_reward)

    def update(self, b2b_reward: MentionMeB2BReward):
        if b2b_reward.status not in [
            enums.MentionMeB2BRewardStatus.REFEREE_ORDER_NOT_FOUND,
            enums.MentionMeB2BRewardStatus.WAITING,
        ]:
            return b2b_reward
        referee_orders = self.get_referee_order_and_set_order_info(b2b_reward)
        if not referee_orders:
            b2b_reward.save()
            return b2b_reward

        order = self.get_first_paid_order(referee_orders)
        b2b_reward.referee_order = order

        if self.is_enough_days_from_order(order, days=self.DAYS_FROM_ORDER):
            b2b_reward.status = enums.MentionMeB2BRewardStatus.WAITING
        else:
            b2b_reward.status = enums.MentionMeB2BRewardStatus.READY_TO_PAY_OUT
        b2b_reward.referee_order_info = enums.RefereeOrderInfo.OK
        b2b_reward.save()
        return b2b_reward

    def get_referee_order_and_set_order_info(self, mention_me_info: MentionMeInfo):
        orders = self.get_orders_matching_email(mention_me_info.referee_email)
        if not orders:
            mention_me_info.referee_order_info = (
                enums.RefereeOrderInfo.NOT_MATCHED_ORDER
            )
            return []

        paid_orders = [o for o in orders if o.paid_at]
        if not paid_orders:
            mention_me_info.referee_order_info = (
                enums.RefereeOrderInfo.NOT_DELIVERED_YET
            )
            return []

        not_sample_orders = [
            o for o in paid_orders if o.cached_items_type != Furniture.sample_box.value
        ]
        if not not_sample_orders:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.ONLY_SAMPLES
            return []

        orders_without_free_return = self.get_orders_without_free_return(
            not_sample_orders
        )
        if not orders_without_free_return:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.RETURN
            return []

        correct_orders = self.get_orders_with_high_enough_invoices(not_sample_orders)
        if not correct_orders:
            mention_me_info.referee_order_info = enums.RefereeOrderInfo.TO_HIGH_REFUNDS
            return []
        return correct_orders

    @classmethod
    def is_enough_days_from_order(
        cls, order: Order, days: int = DAYS_FROM_ORDER
    ) -> bool:
        time_delta = datetime.datetime.now().date() - order.paid_at.date()
        return order.paid_at and datetime.timedelta(days=days) > time_delta

    @classmethod
    def get_first_paid_order(cls, orders) -> Optional[Order]:
        if not orders:
            return None
        orders = list(sorted(orders, key=attrgetter('paid_at')))
        return orders[0]

    @classmethod
    def notify_slack_about_new_b2b_reward(cls, b2b_reward: MentionMeB2BReward):
        url = reverse(
            'admin:customer_service_mentionmeb2breward_change', args=[b2b_reward]
        )
        notify_about_new_b2b_reward(url)


def create_refund_invoice_correction_request(refund_info: RefundInfo, user: 'User'):
    invoice = refund_info.referer_order.get_invoices().order_by('-created_at').first()
    validate_refund_invoice_correction(invoice, refund_info.currency_code)
    correction_request = CSCorrectionRequest.objects.create(
        correction_amount_gross=refund_info.amount,
        invoice=invoice,
        tag=InvoiceItemTag.DISCOUNT_REFERRAL.value,
        status=CSCorrectionRequestStatus.STATUS_NEW,
        issuer=user,
    )
    strategy = CorrectionRequestAmountStrategy(correction_request)
    strategy.prepare_correction_request()
    return correction_request


def validate_refund_invoice_correction(invoice, expected_currency_code):
    correctable_statuses = {
        InvoiceStatus.CORRECTING,
        InvoiceStatus.ENABLED,
        InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
    }
    if not invoice:
        raise errors.CorrectionCreationError('Invoice does not exist')
    if invoice.get_currency_code().lower() != expected_currency_code.lower():
        raise errors.CorrectionCreationError('Wrong currency code')
    if invoice.pending_correction_requests.exists():
        raise errors.CorrectionCreationError('Invoice has pending request')
    if (
        invoice.corrected_invoice
        and invoice.corrected_invoice.corrections.filter(id__gt=invoice.id).exists()
    ):
        raise errors.CorrectionCreationError('Invoice already has correction')
    if invoice.status not in correctable_statuses:
        raise errors.CorrectionCreationError(
            'Invoice with this status cannot be corrected'
        )
