from customer_service.admin_actions import CSCorrectionRequestActionsMixin
from customer_service.enums import CSCorrectionRequestStatus
from customer_service.models import CSCorrectionRequest
from invoice.choices import InvoiceStatus


class TestCSCorrectionRequestActionsMixin:
    def test_decline_correction_request_should_delete_correction_invoice_when_exists(
        self,
        cs_correction_request_factory,
        admin_action_request,
        admin_user,
        invoice_factory,
        mocker,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')

        normal_invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
        )

        correction_invoice = invoice_factory(
            corrected_invoice=normal_invoice,
            pretty_id='correction/1/2',
            status=InvoiceStatus.CORRECTING_DRAFT,
        )
        cs_correction_request = cs_correction_request_factory(
            invoice=normal_invoice,
            correction_invoice=correction_invoice,
            status=CSCorrectionRequestStatus.STATUS_NEW,
            issuer=admin_user,
        )

        CSCorrectionRequestActionsMixin().decline_correction_request(
            request=admin_action_request,
            queryset=CSCorrectionRequest.objects.filter(pk=cs_correction_request.pk),
        )

        cs_correction_request.refresh_from_db()

        assert cs_correction_request.status == CSCorrectionRequestStatus.STATUS_REJECTED
        assert cs_correction_request.correction_invoice is None
