from django.db.models import IntegerChoices
from django.utils.translation import (
    gettext_lazy,
    ngettext,
)


class CookieCategories(IntegerChoices):
    NECESSARY = 1
    PERFORMANCE = 2
    FUNCTIONAL = 3
    ADVERTISING = 4


class CookieDurationUnits(IntegerChoices):
    SECONDS = 1
    MINUTES = 2
    HOURS = 3
    DAYS = 4
    WEEKS = 5
    MONTHS = 6
    YEARS = 7
    SESSION = 8

    def get_translation(self, value: int) -> str:
        return {
            self.SECONDS: ngettext('%(count)d second', '%(count)d second', value),
            self.MINUTES: ngettext('%(count)d minute', '%(count)d minute', value),
            self.HOURS: ngettext('%(count)d hour', '%(count)d hour', value),
            self.DAYS: ngettext('%(count)d day', '%(count)d day', value),
            self.WEEKS: ngettext('%(count)d week', '%(count)d week', value),
            self.MONTHS: ngettext('%(count)d month', '%(count)d month', value),
            self.YEARS: ngettext('%(count)d year', '%(count)d year', value),
            self.SESSION: gettext_lazy('session'),
        }[self]
