from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import smart_selects.db_fields


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0003_new_colors_type01'),
    ]

    operations = [
        migrations.CreateModel(
            name='Area',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('is_deprecated', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ComplaintType',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('is_deprecated', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Responsibility',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('is_deprecated', models.BooleanField(default=False)),
                (
                    'area',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='complaints.Area',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='TypicalIssues',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('is_deprecated', models.BooleanField(default=False)),
                (
                    'complaint_type',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='complaints.ComplaintType',
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='complaint',
            name='is_customer_responsible',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='complaint',
            name='is_repeated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='area',
            name='typical_issue',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='complaints.TypicalIssues',
            ),
        ),
        migrations.AddField(
            model_name='complaint',
            name='area',
            field=smart_selects.db_fields.ChainedForeignKey(
                blank=True,
                chained_field='typical_issues_new',
                chained_model_field='typical_issues',
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='complaints.Area',
            ),
        ),
        migrations.AddField(
            model_name='complaint',
            name='complaint_type_new',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='complaints.ComplaintType',
            ),
        ),
        migrations.AddField(
            model_name='complaint',
            name='responsibility_new',
            field=smart_selects.db_fields.ChainedForeignKey(
                blank=True,
                chained_field='area',
                chained_model_field='area',
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='complaints.Responsibility',
            ),
        ),
        migrations.AddField(
            model_name='complaint',
            name='typical_issues_new',
            field=smart_selects.db_fields.ChainedForeignKey(
                chained_field='complaint_type_new',
                chained_model_field='complaint_type',
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='complaints.TypicalIssues',
            ),
        ),
    ]
