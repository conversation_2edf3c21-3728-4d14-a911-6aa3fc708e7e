from complaints.models import Complaint
from complaints.serializers import ComplaintSerializer
from custom.serializers import dynamic_serializer


def test_complaint_serializer_raise_error_when_fieldset_is_changed():
    exclude = (
        'owner',
        'product',
        'reporter',
        'reproduction_order',
        'fittings_only',
        'complaint_costs',
        'deleted_by_cascade',
        'serialized_complaint_service',
        'exported_to_big_query',
        'has_deprecated_elements',
        'complaint_cost',
        'product_cost',
        'closed_at',
    )

    excluded_fields_serializer = dynamic_serializer(
        <PERSON><PERSON><PERSON><PERSON>, ComplaintSerializer, exclude
    )
    dynamic_fields = set(excluded_fields_serializer().get_fields().keys())
    actual_fields = set(ComplaintSerializer().get_fields().keys())

    error_message = (
        'Add to exclude or talk with <PERSON><PERSON> when you add field to this serializer'
    )

    assert dynamic_fields == actual_fields, error_message
