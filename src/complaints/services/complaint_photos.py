from io import Bytes<PERSON>
from zipfile import ZipFile

from django.db.models import QuerySet

from complaints.models import (
    Complaint,
    ComplaintPhoto,
)
from custom.utils.report_file import ReportFile


def match_complaint_with_photo_base_on_orders(photo: ComplaintPhoto):
    complaint = Complaint.objects.get(product__order__in=photo.orders.all())
    photo.complaint = complaint
    photo.save(update_fields=['complaint'])


def create_zipfile_with_complaint_photos(complaints: QuerySet[Complaint]):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zip_file:
        for complaint in complaints:
            for photo in complaint.photos.all():
                zip_file.writestr(
                    f'{complaint.product_id}/{photo.file_name}',
                    data=photo.photo.read(),
                )
    return ReportFile(name='photo_of_damages.zip', content=stream.getvalue())
