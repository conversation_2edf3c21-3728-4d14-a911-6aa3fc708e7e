{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "name": "hinge_door_part_L"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "hinge_door_part_L"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 702, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 702, "max": [48.8315468, 21.9999962, 6.246383e-06], "min": [-18.2042828, -21.9999962, -27.8634834], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 8424, "componentType": 5126, "count": 702, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 702, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 5616, "componentType": 5126, "count": 702, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 1404, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 1404, "byteLength": 16848, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 18252, "byteLength": 11232, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "hinge_door_part_L.bin", "byteLength": 29484}]}