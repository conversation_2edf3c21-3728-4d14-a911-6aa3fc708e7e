{% extends "front/logistic/_complaint_service_date_proposal.html" %}
{% load i18n %}
{% block container %}
    <p class="mw-unset normal-16 text-offblack-600">
        {% if date_accepted %}
            {% blocktrans with date=date|date:"d/m/Y" from_hour=from_hour|time:"H:i" to_hour=to_hour|time:"H:i" %}complaint_service_dates_chosen_confirmation_{{ date }}_{{ from_hour }}_{{ to_hour }}{% endblocktrans %}
        {% elif has_accepted_date %}
            {% trans 'complaint_service_dates_already_selected' %}
        {% elif date_expired_or_not_proposed %}
            {% blocktrans with date=date|date:"d/m/Y" from_hour=from_hour|time:"H:i" to_hour=to_hour|time:"H:i" %}complaint_service_date_expired_{{ date }}_{{ from_hour }}_{{ to_hour }}{% endblocktrans %}
        {% else %}
            {% blocktrans with date=date|date:"d/m/Y" %}complaint_service_dates_confirm_question_{{ date }}{% endblocktrans %}
            <div class="flex flex-column middle-xs around-xs mt-40">
                <div>
                    <a href="{% url 'complaint_service_accept_date_proposal' date_proposal_id=date_proposal_id %}?confirm=true" class="ml-24 btn-cta">
                        {% trans 'assembly_service_dates_confirm_answer_yes' as translated_answer_yes %}
                        {{ translated_answer_yes|upper }}
                    </a>
                    <a href="{% url 'complaint_service_accept_date_proposal' date_proposal_id=date_proposal_id %}?confirm=false" class="ml-24 btn-cta">
                        {% trans 'assembly_service_dates_confirm_answer_no' as translated_answer_no %}
                        {{ translated_answer_no|upper }}
                    </a>
                </div>
            </div>
        {% endif %}
    </p>

    {% if show_request_new_dates_btn %}
        <div class="flex flex-column middle-xs around-xs mt-40">
            <a class="btn-cta mb-24 md:mb-16"
               href="{% url 'complaint_service_new_date_request' service_id=service_id %}">
                {% trans 'complaint_service_request_new_dates' %}
            </a>
        </div>
    {% endif %}
{% endblock %}

