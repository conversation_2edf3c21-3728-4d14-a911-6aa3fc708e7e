{% load i18n %}


<div class="infobox mobile review-grade" style="position: absolute;">
    <div style="top: 80px !important;">
                        <a href="#" data-target="next"  data-type="{{ furniture_type }}" data-page="1" data-amount="3" onclick="Site.Reviews.getReviews(this, '{{ LANGUAGE_CODE }}'); $(this).parent().parent().toggleClass('active'); $('body').removeClass('hidden-overflow'); return false;">{% trans 'reviews_show_all' %}</a>
                        {% for i in "12345" %}
                            <a href="javascript:void(0);" data-target="next" data-rating="{{ i }}" data-type="{{ furniture_type }}" data-page="1" data-amount="3" onclick="Site.Reviews.getReviews(this, '{{ LANGUAGE_CODE }}'); $(this).parent().parent().toggleClass('active'); $('body').removeClass('hidden-overflow'); return false;">{% if i == 1 %}reviews_show_1star{% else %}{% blocktrans with i as star_number %}reviews_show_{{ star_number }}_stars{% endblocktrans %}{% endif %}</a>
                        {% endfor %}

                     <a href="#" class="btn-cta btn-cta--border w-full" onclick="$(this).parent().parent().toggleClass('active'); $('body').removeClass('hidden-overflow'); return false;">{% trans 'common_close' %}</a>
    </div>
      <span class="overlay" onclick="$(this).parent().toggleClass('active'); $('body').removeClass('hidden-overflow'); return false;"></span>
</div>
