{% load cache %}
{% load components %}
{% load galleries %}
{% load i18n %}
{% load region_tags %}
{% load seo_tags %}
{% load static %}
{% load user_agents %}
{% load util_tags %}
{% load voucher_tags %}
{% load tracking %}

<!doctype html>
{% get_current_language as LANGUAGE_CODE %}
<html class="no-js {% block extra_html_class %}{% endblock %}" lang="{{ LANGUAGE_CODE }}">
<head>
    {% block extraheadfirst %}
    {% endblock %}
    <meta charset="utf-8">

    <meta name="google" value="notranslate">
    <title>{% block title %}{% blocktrans %}metatags_homepage_1{% endblocktrans %}{% endblock %}</title>
    <meta name="title" content="{% block meta_title %}{% blocktrans %}metatags_homepage_1{% endblocktrans %}{% endblock %}">
    <meta name="description" content="{% block description %}{% blocktrans %}metatags_homepage_description_1{% endblocktrans %}{% endblock %}">
    <meta name="viewport" content="viewport-fit=cover, width=device-width, minimum-scale=1, maximum-scale=1">
    {% block extra_head_prefetch %}{% endblock %}
    <meta name="p:domain_verify" content="af453366b8129efaca68bf9c659fa83f"/>

    <!-- Place favicon.ico in the root directory -->
    {% if IS_PRODUCTION %}
        <meta name="robots" content="INDEX,FOLLOW">
    {% else %}
        <meta name="robots" content="noindex, nofollow">
    {% endif %}
    <link rel="manifest" href="{% static 'favicons/manifest.json' %}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">

    <link rel="stylesheet" href="{% static "dist/css/ds.css" %}?1">
    <link rel="stylesheet" href="{% static "dist/css/style.css" %}?1">
    <meta property="fb:app_id" content="739339722848856" />
    <meta name="p:domain_verify" content="64f280ea528bae6144728bdb8f2dcd19"/>
    <meta name="p:domain_verify" content="af453366b8129efaca68bf9c659fa83f"/>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@tylko_furniture">
    <meta name="twitter:creator" content="@tylko_furniture">
    <meta name="twitter:title" content="tylko app makes furniture personal. Yours by design.">
    <meta name="twitter:description" content="Adapt designer furniture to your personal needs in the tylko app, and have it made-to-order and delivered to your home.">
    <script src="https://unpkg.com/pubsub-js@1.9.2/src/pubsub.js"></script>

    <link rel="canonical" href="https://tylko.com{% block canonical_url %}{{ request.get_full_path }}{% endblock %}" />

    {% block hreflangs %}
        {% if not is_category_generated %}
            {% cache 86400 hreflangs request.path %}
            {% for language in LANGUAGES %}
                <link rel="alternate" hreflang="{{ language }}" href="https://tylko.com{% hreflang_url request.path language %}" />
            {% endfor %}
            {% endcache %}
        {% endif %}
    {% endblock %}

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KWDFT');
    </script>
    <!-- End Google Tag Manager -->

<!-- modernizer inline -->
    <script type="text/javascript">
        if(!window.console) {
          var console = {
            log : function(){},
            warn : function(){},
            error : function(){},
            time : function(){},
            timeEnd : function(){}
          }
        }
        window.Modernizr=function(m,h,n){function x(a,b){for(var f in a){var d=a[f];if(!~(""+d).indexOf("-")&&l[d]!==n)return"pfx"==b?d:!0}return!1}function p(a,b,f){var d=a.charAt(0).toUpperCase()+a.slice(1),g=(a+" "+y.join(d+" ")+d).split(" ");if("string"===typeof b||"undefined"===typeof b)return x(g,b);g=(a+" "+z.join(d+" ")+d).split(" ");a:{a=g;for(var e in a)if(d=b[a[e]],d!==n){if(!1===f){b=a[e];break a}if("function"===typeof d){b=d.bind(f||b);break a}b=d;break a}b=!1}return b}var e={},k=h.documentElement,
c=h.createElement("modernizr"),l=c.style,A=" -webkit- -moz- -o- -ms- ".split(" "),y=["Webkit","Moz","O","ms"],z=["webkit","moz","o","ms"];c={};var q=[],r=q.slice,t=function(a,b,f,e){var g=h.createElement("div"),d=h.body,c=d||h.createElement("body");if(parseInt(f,10))for(;f--;){var l=h.createElement("div");l.id=e?e[f]:"modernizr"+(f+1);g.appendChild(l)}f=['&#173;<style id="smodernizr">',a,"</style>"].join("");g.id="modernizr";(d?g:c).innerHTML+=f;c.appendChild(g);if(!d){c.style.background="";c.style.overflow=
"hidden";var m=k.style.overflow;k.style.overflow="hidden";k.appendChild(c)}a=b(g,a);d?g.parentNode.removeChild(g):(c.parentNode.removeChild(c),k.style.overflow=m);return!!a},u={}.hasOwnProperty;var B="undefined"===typeof u||"undefined"===typeof u.call?function(a,b){return b in a&&"undefined"===typeof a.constructor.prototype[b]}:function(a,b){return u.call(a,b)};Function.prototype.bind||(Function.prototype.bind=function(a){var b=this;if("function"!=typeof b)throw new TypeError;var c=r.call(arguments,
1),e=function(){if(this instanceof e){var d=function(){};d.prototype=b.prototype;d=new d;var f=b.apply(d,c.concat(r.call(arguments)));return Object(f)===f?f:d}return b.apply(a,c.concat(r.call(arguments)))};return e});c.touch=function(){var a;"ontouchstart"in m||m.DocumentTouch&&h instanceof DocumentTouch?a=!0:t(["@media (",A.join("touch-enabled),("),"modernizr){#modernizr{top:9px;position:absolute}}"].join(""),function(b){a=9===b.offsetTop});return a};c.history=function(){return!(!m.history||!history.pushState)};
c.rgba=function(){l.cssText="background-color:rgba(150,255,150,.5)";return!!~(""+l.backgroundColor).indexOf("rgba")};c.csstransforms=function(){return!!p("transform")};c.csstransforms3d=function(){var a=!!p("perspective");a&&"webkitPerspective"in k.style&&t("@media (transform-3d),(-webkit-transform-3d){#modernizr{left:9px;position:absolute;height:3px;}}",function(b,c){a=9===b.offsetLeft&&3===b.offsetHeight});return a};c.video=function(){var a=h.createElement("video"),b=!1;try{if(b=!!a.canPlayType)b=
Boolean(b),b.ogg=a.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),b.h264=a.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),b.webm=a.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,"")}catch(f){}return b};c.localstorage=function(){try{return localStorage.setItem("modernizr","modernizr"),localStorage.removeItem("modernizr"),!0}catch(a){return!1}};c.svg=function(){return!!h.createElementNS&&!!h.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect};
for(var v in c)if(B(c,v)){var w=v.toLowerCase();e[w]=c[v]();q.push((e[w]?"":"no-")+w)}e.addTest=function(a,b){if("object"==typeof a)for(var c in a)B(a,c)&&e.addTest(c,a[c]);else{a=a.toLowerCase();if(e[a]!==n)return e;b="function"==typeof b?b():b;k.className+=" "+(b?"":"no-")+a;e[a]=b}return e};l.cssText="";c=null;e._version="2.8.3";e._prefixes=A;e._domPrefixes=z;e._cssomPrefixes=y;e.testProp=function(a){return x([a])};e.testAllProps=p;e.testStyles=t;k.className=k.className.replace(/(^|\s)no-js(\s|$)/,
"$1$2")+(" js "+q.join(" "));return e}(this,this.document);
    </script>

    <script src="//ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
    <script src="{% static "dist/js/head.min.js" %}" defer></script>

    <script type="text/javascript">
        window.cstm_uuid = {{ request.user.id|default_if_none:"0" }};
        window.is_mobile_loaded = {% if not request|is_pc and not request|is_tablet%}true{% else %}false{% endif %};
        window.is_ie_loaded = {% if request|is_ie %}true{% else %}false{% endif %};
        window.is_tablet_loaded = {% if request|is_tablet%}true{% else %}false{% endif %};
        window.is_fb_loaded = {% if request|is_fb %}true{% else %}false{% endif %};
        window.is_ios_loaded = {% if request|is_ios %}true{% else %}false{% endif %};
        window.is_webview= {% if its_webview %}true{% else %}false{% endif %};
        {% with region=user_region %}
        window.ga_new_conf_test = true;
        window.ga_new_design = true;
    {% for test in TESTS %}
        window.{{ test.codename }} = {% if request|is_ab_test:test.codename %}true{%else%}false{%endif%};
    {% endfor %}
        window.abg_grfgf = [{% for test in TESTS %}['{{ test.codename }}|{{ test.rate_split }}',{% if request|is_ab_test:test.codename %}true{%else%}false{%endif%}]{% if not forloop.last %},{% endif %}{% endfor %}];
        window.ab_ids = "{{ AB_IDS }}";
        window.feature_flags_ids = "{{ FEATURE_FLAGS_IDS }}";
        window.cstm_i18n = {
            "language": "{{ LANGUAGE_CODE }}",
            "error": "{% trans 'addreview_page_upload_problem' as translated_text %}{{ translated_text|escapejs }}",
            "account": "{% trans "Account" %}",
            "cart": "{% trans "Cart" %}",
            "added_to_cart": "{% trans "Added to cart" %}",
            "removed_from_cart": "{% trans "Removed from cart" %}",
            "dorothy_assembly_service_cart_ribbon_4": "{% trans "dorothy_assembly_service_cart_ribbon_4" %}",
            "dorothy_assembly_service_cart_ribbon_2": "{% trans "dorothy_assembly_service_cart_ribbon_2" %}",
            "promo_accepted": "{% trans "Promo Code accepted." %}",
            "promo_not_accepted": "{% trans "Please enter a valid promo code." %}",
            "user_agent": "{{ request.user_agent.ua_string|lower }}",
            "delivery_dynamic_text" : "{% trans "delivery_dynamic_text_with_br" %}",
            "delivery_dynamic_text_with_broken_copy" : "{% trans "delivery_dynamic_text_with_tick" %}",
            "shelf_saved" : "{% trans "Your piece has been successfully saved." %}",
            "shelf_not_saved" : "{% trans "Saving failed. Please try again." %}",
            "email_sent" : "{% trans 'popup_save_createaccount_preheader' %}",
            "vat_included": '{% trans 'common_includes_vat' %}',
            "region_name": "{{ region.name }}",
            "last_clouse_up": 0,
            "twitter_message": "{% trans "emily_exit_popup_sharing_twitter_input_headline_1" %}",
            "product_page_configurator_width_wider": "{% trans "product_page_configurator_width_wider" %}",
            "product_page_configurator_width_narrower": "{% trans "product_page_configurator_width_narrower" %}",
            "region_currency_code": "{{ USER_REGION_CURRENCY.code }}",
            "region_currency_symbol": "{{ USER_REGION_CURRENCY.symbol }}",

        {% endwith %}

        {% if promotion != None %}
            "sale_enabled" : true,
            {% else %}
            "sale_enabled" : false,
        {% endif %}

            "sidebarCart" : true,

        };
        window.dataLayer = window.dataLayer || [{
                'marketing_grant': '1',
        }];
    {% if request.user.is_authenticated == True %}
        if (typeof dataLayer != 'undefined') {
            dataLayer.push({
                'userId': 'user-{{ request.user.id }}',
                'event': 'authentication',
                'userLanguage': '{{ LANGUAGE_CODE }}'
            });
        }
        {% if "userGaId" not in request.COOKIES %}
                var d = new Date();
                d.setTime(d.getTime()+1000*60*60*24*365*2);
                var expires = 'expires='+d.toGMTString();
                document.cookie = 'userGaId="user-{{ request.user.id }}"; '+expires+'; path=/';
            {% endif %}
    {% else %}
            {% if "userGaId" in request.COOKIES %}
                if (typeof dataLayer != 'undefined') {
                    window.dataLayer.push({
                        'userId': '{{ request.COOKIES.userGaId }}',
                        'event': 'authentication',
                        'userLanguage': '{{ LANGUAGE_CODE }}'
                    });
                }
            {% else %}

                if (typeof dataLayer != 'undefined') {
                    window.dataLayer.push({
                        'userId': undefined,
                        'event': 'authentication',
                        'userLanguage': '{{ LANGUAGE_CODE }}'
                    });
                }
            {% endif %}
    {% endif %}

    </script>

    {% if request|is_ie or request|is_ios_11 or request|is_safari_11 %}
        <script crossorigin="anonymous" src="https://polyfill.io/v3/polyfill.min.js?features=default%2CMutationObserver%2CURLSearchParams%2CURL%2CArray.prototype.flatMap%2CArray.prototype.flat%2CObject.fromEntries"></script>
    {% endif %}

    <script type="text/javascript">
    cstm_language_change = function(language){
        if (window.location.pathname.substring(0,4) == "/pl/" || window.location.pathname.substring(0,4) == "/de/"){
            var path_to_use = window.location.pathname.substring(3)
        }
        else {
            var path_to_use = window.location.pathname
        }
        window.location = "{% url 'set_language' %}?language=" + language + "&next=" + window.location.pathname;
        console.log("{% url 'set_language' %}?language=" + language + "&next=" + window.location.pathname);
    };

    </script>

    <script type="text/javascript">
        window.onload = function() {
            {% if not request.user.is_anonymous %}
                if ( typeof Site !== 'undefined' ) {
                    Site.CartController.sync();
                    Site.lazyLoadingImg.initialize();
                    //$('.link-cart').click();
                    {% block window_on_load %}{% endblock %}
                }
            {% endif %}
        };

    </script>

    {% block extra_scripts_type %}
        <script type="text/javascript">
            window.fb_content_type = 'other';
            window.content_ids = ['0'];
        </script>
    {% endblock %}

    {% block extrahead %}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://tylko.com/" />
        <meta property="og:title" content="tylko app makes furniture personal. Yours by design." />
        <meta property="og:description" content="tylko is a whole new approach to furniture design, locked into an iOS app. Founded on the simple truth that every home is different, the tylko app offers the possibility to customize designer furniture for the requirements of your personal space." />
        <meta property="og:image" content="https://tylko.com/r_static/share-preview-images/tylko-meta-preview-image.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
    {% endblock %}

    </head>
<body class="{% block extra_body_class %}{% endblock %}" style="{% block extra_body_style %}{% endblock %}" {% block extra_body_property %}{% endblock %} data-script="lazyLoadingImg" data-lazyLoadingStrategy="{ strategy: 'default' }">

<!-- Google Tag Manager -->
    <noscript><iframe src="//www.googletagmanager.com/ns.html?id=GTM-5KWDFT"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager -->

<!-- Google Tag Manager helper init -->
{% block gtmTrack %}
    {% trackECommerce %}
{% endblock %}

<!--[if lt IE 9]>
            <p class="browserupgrade z-20">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->



{% block notify_block %}
{#    old popup - use new popup component from now on but if you really want to/need to use this one go for it :) #}
{#    {% if show_mobile_notify %}{% include 'front/_mobile-overlay.html' %}{% endif %}#}
{#    alseo have a flaming sword o===]['.'.'.'.'~ #}
{% endblock %}

{% block full_overlay %}{% endblock %}

<div class="wrapper {% if request|is_pc or request|is_tablet %} block-width{% else %}mobile{% endif %}" style="{% block wrapper_style %}{% endblock %}">
    {#  HEADER  #}
    {% block header %}
            {% include 'components/header/_header.html' %}



            {% block ribbon_block %}
                {% if promotion != None %}
                    {% include 'components/ribbon/ribbon_sale_desktop.html' %}
                {% endif %}
            {% endblock %}

            {% block ribbon_block_body %}
                {% if promotion != None %}
                    {% include 'components/ribbon/ribbon_sale_mobile.html' %}
                {% endif %}
            {% endblock %}

    {% endblock %}
    <main class="content" id="content" data-title="tylko" style="background: transparent;{% block content_style %}{% endblock %}">
        {% if messages %}
            {% for message in messages %}
                <span class="visually-hidden" data-notify='{"status":"{% if message.level == 40 %}error{% else %}success{% endif %}", "message":"{{ message}}", "timeout": 100000}'>
                </span>
            {% endfor %}
        {% endif %}

        {% block content %}
            EMPTY BASE, without content block
        {% endblock %}
    </main>
</div>

{% block cart_sidebar %}
    {% include 'components/sidebar-cart/sidebar-cart-jsrender.html' %}
{% endblock %}

{% block normal_footer %}

    {% include 'front/_footer/_footer.html' %}

{% endblock %}
{% comment %}
{% block cookies_ribbon %}
    {% if request|is_pc %}
        {% include 'components/ribbon/ribbon_cookies_bottom.html' %}
    {% else %}
        {% include 'components/ribbon/ribbon_cookies_mobile.html' %}
    {% endif %}
{% endblock %}
{% endcomment %}
{% block extra_footer %}
    {% if "sliderMoved" in request.COOKIES and not request|is_user and not request|is_mobile %}
        {% popup "product_save_nouser" exit_trap="true" show_close=True cookie="session" dataLayer="popup_exitpopup" dataLayerAdditional='{"eventAction": "impression"}' %}
    {% endif %}
{% endblock %}
{#{% if not request.user.is_anonymous and request|is_mobile %}<div id="cart-mobile-wrapper">#}
{% if not request.user.is_anonymous %}
    <div id="cart-mobile-wrapper">
        <div id="cart-mobile-overlay" class="z-20">
{#    loaded through ajax #}
{#        {% show_cart_bubble_mobile  %}#}
        </div>
    </div>
{% endif %}

{% popup "delete" is_modal=True show_close=True %}
{% popup "region" is_modal=True show_close=True %}

{% block assembly_popup %}
    {% popup_assembly content_name="assembly-add" user_region=user_region cart_handler=True modify_class="full-height" delivery_delay=True with_cta=True auto_open=False show_close=True dataLayer='popup_paid_assembly' onCloseDataLayer='{"eventAction": "close"}'%}
    {% popup_assembly content_name="assembly-mobile-tooltip" content_template="components/popup-luke/popup_luke_assembly_tooltip.html" user_region=user_region cart_handler=False modify_class="" delivery_delay=True with_cta=False auto_open=False show_close=True %}
{% endblock %}

{% block fasttrack_popup %}
    {% popup_fasttrack content_name="fasttrack-add" user_region=user_region cart_handler=True modify_class="full-height" delivery_delay=True with_cta=True auto_open=False show_close=True dataLayer='popup_fasttrack' onCloseDataLayer='{"eventAction": "close"}'%}
{% endblock %}


<div id="MyCustomTrustbadgeFixed" class="z-20"></div>

{% comment %}
{% if request|is_pc and LANGUAGE_CODE in "en de" and not request|is_user %}
    {% popup "subscribe" is_modal=True auto_open=True open_delay=10000 show_close=True cookie_repeat=2 cookie_interval=14 dataLayer="popup_impression" %}
{% endif %}
{% endcomment %}

{% if request|is_tablet %}
    {% include 'front/cover-tablet-portrait.html' %}
{% elif request|is_mobile%}
    {% include 'front/cover-phone-portrait.html' %}
{% endif %}



{# Show Wishlist menu icon on save-to-wishlist-button click #}
{% if request.user.is_authenticated == True and request.user.profile.user_type == 2 %}
    {% with items_in_lib=USER_LIBRARY_ITEMS %}
        {% if items_in_lib == 0 %}
            <script>
                $('.configurator_save_button').on('click', function() {
                    $('.menu-wishlist').removeClass('hidden-wishlist');
                });
            </script>
        {% endif %}
    {% endwith %}
{% endif %}
{#End - Show Wishlist...#}

{% if DEBUG == False %}

{% block trusted_shop_footer %}
    {% include 'components/footer/trusted_shops.html' with reviews=True %}
{% endblock %}

{% endif %}
{% include 'front/_cookiebar.html' %}

<script src="{% static "dist/js/scripts.min.js" %}?2"></script>
    </body>
</html>
