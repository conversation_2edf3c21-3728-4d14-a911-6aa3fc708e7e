from django import template
from django.utils.translation import gettext

register = template.Library()


@register.simple_tag
def material_trans(material):
    if not material:
        return ''

    colour_name = material['colour']['name']
    material_name = material['material']
    return f'{colour_name.title()} {material_name.title()}'.strip()


@register.simple_tag
def item_custom_product_name(item):
    return gettext('common_custom_order')
