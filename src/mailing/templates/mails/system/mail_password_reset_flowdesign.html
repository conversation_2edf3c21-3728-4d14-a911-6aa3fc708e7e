{% extends 'mails/_base_templates/base_flow.html' %}
{% load mailing_tags %}
{% load i18n static %}


{# Preheader #}

{% block preheader %}{% trans 'mail_system_password_change_preheader_1' %}{% endblock %}


{% block content %}

{# Translation Keys Variables #}

    {% trans 'mail_system_password_change_headline_1' as headline_1 %}
    {% trans 'mail_system_password_change_paragraph_1' as paragraph_1_1 %}
    {% trans 'mail_system_password_change_buton_1' as button_alt_1 %}
    {# 'front-changepassword-token' *args -> token  #}
    {% trans 'mail_system_password_change_paragraph_2' as paragraph_2_1 %}

{# Mail Components structure #}

    {% mail_logo 'front-changepassword-token' token=token utm='utm_campaign=system_password_change' %}

    {% mail_headline headline_1 %}
    {% mail_paragraph paragraph_1_1 %}
    {% mail_button button_alt_1 'front-changepassword-token' token=token utm='utm_campaign=system_password_change' button_align='center' %}
    {% mail_paragraph paragraph_2_1 %}

{% endblock %}

{% block footer_extension_top %}{% endblock %}
