import datetime
import logging

from django.contrib.auth import get_user_model
from django.utils import translation
from django.utils.translation import gettext_lazy as _

from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView

from complaints.choices import NotificationTypes
from custom import permissions
from events.domain_events.logistic_events import (
    AcceptedProviderRequestEvent,
    ComplaintReadyToBeShippedEvent,
    DeliveryDateProposalReminderRequestEvent,
    DeliveryDateProposalRequestEvent,
    DeliverySettledEvent,
    DeliveryTimeFrameDateChangedRequestEvent,
    DeliveryTimeFrameNewDateRequestEvent,
    DeliveryTimeFrameProposalReadyEvent,
    Email24DeliveryConfirmationReadyEvent,
    Email24DeliveryProposalReadyEvent,
    Email24LogisticConfirmationReadyEvent,
    ServiceDeliveryDateChosenEvent,
    ServiceDeliveryNewDateRequestEvent,
)
from mailing.internal_api.serializers import (
    DomesticInjectionEmailSerializer,
    ServiceDateProposalEventSerializer,
    ServiceDateProposalSerializer,
    ServiceDeserializer,
)
from mailing.templates_logistic import (
    AssemblyServiceAcceptedNotification,
    AssemblyServiceChooseDateMail,
    AssemblyServiceChooseDateReminderMail,
    AssemblyServiceDateChosenConfirmationMail,
    AssemblyServiceNewDateRequestMail,
    ComplaintServiceAcceptedNotification,
    ComplaintServiceChooseDateMail,
    ComplaintServiceChooseDateReminderMail,
    ComplaintServiceDateChosenConfirmationMail,
    ComplaintServiceFreeReturnChooseDateMail,
    ComplaintServiceNewDateRequestMail,
    ComplaintWillBeSentIn24Hours,
    ComplaintWillBeSentWithAvailabilityQuestion,
    CustomerServiceContactServiceProtocolMail,
    DeliverySettled,
    DeliverySettledEstimatedDeliveryDate,
    DeliveryTimeFrameDateChangedNotification,
    DeliveryTimeFrameNewDateNotification,
    DeliveryTimeslotsProposalMail,
    DeliveryTimeslotsProposalResendAcceptedMail,
    DeliveryTimeslotsProposalResendNotAcceptedMail,
    DomesticInjectionEmail,
    Email24Confirmation,
    Email24ConfirmationLogistic,
    Email24DeliveryDetailProposal,
    ProductShippedRegularMail,
)
from orders.models import Order
from user_profile.models import RetargetingBlacklistToken

User = get_user_model()

logger = logging.getLogger('cstm')


class ServiceNewDateRequestMailSendView:
    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        service_id = data_html['service_id']
        order_id = data_html['order_id']
        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=order.email,
                data_html={
                    'user_name': order.first_name,
                    'service_id': service_id,
                    'blacklist_token': (
                        RetargetingBlacklistToken.get_or_create_for_email(
                            email=order.email
                        ).token
                    ),
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class AssemblyServiceNewDateRequestMailSendView(
    ServiceNewDateRequestMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = AssemblyServiceNewDateRequestMail


class ComplaintServiceNewDateRequestMailSendView(
    ServiceNewDateRequestMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintServiceNewDateRequestMail


class ServiceDeliveryNewDateRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        service_id = request.data['service_id']
        order_id = request.data['order_id']
        service_type = request.data['service_type']
        order = Order.objects.get(pk=order_id)

        ServiceDeliveryNewDateRequestEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            service_id=service_id,
            service_type=service_type,
        )

        return Response(status=status.HTTP_201_CREATED)


class ServiceAcceptedNotificationSendView:
    def post(self, request, *args, **kwargs):
        to_address = request.data['to_address']
        data_html = request.data['data_html']
        order_id = data_html['order_id']

        self.mail_class(
            to_address=to_address,
            data_html={'order_id': order_id},
            topic_variables={'order_id': order_id},
        ).send()

        return Response(status=status.HTTP_201_CREATED)


class AssemblyServiceAcceptedNotificationSendView(
    ServiceAcceptedNotificationSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = AssemblyServiceAcceptedNotification


class ComplaintServiceAcceptedNotificationSendView(
    ServiceAcceptedNotificationSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintServiceAcceptedNotification


class AcceptedProviderRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        to_address = request.data['to_address']
        order_id = request.data['order_id']
        service_type = request.data['service_type']
        AcceptedProviderRequestEvent(
            user=User.objects.get(username='admin'),
            service_type=service_type,
            logistic_order_id=order_id,
            email=to_address,
        )

        return Response(status=status.HTTP_201_CREATED)


class ComplaintWillBeSentIn24HoursSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintWillBeSentIn24Hours

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=order.email,
                data_html={
                    'username': order.first_name,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class ComplaintWillBeSentWithAvailabilityQuestionSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintWillBeSentWithAvailabilityQuestion

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=order.email,
                data_html={
                    'username': order.first_name,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class ComplaintReadyToBeShippedEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']
        order = Order.objects.get(pk=order_id)
        complaint_notification_type = order.get_complaint_notification_type()
        ComplaintReadyToBeShippedEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            availability_question=(
                complaint_notification_type == NotificationTypes.CONFIRM_AVAILABILITY
            ),
        )

        return Response(status=status.HTTP_201_CREATED)


class ServiceAssemblyServiceChooseDateReminderMailSendView:
    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        service_id = data_html['service_id']
        order_id = data_html['order_id']

        proposals_serializer = ServiceDateProposalSerializer(
            data=data_html['proposals'], many=True
        )
        proposals_serializer.is_valid(raise_exception=True)
        proposals = proposals_serializer.validated_data

        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=order.email,
                data_html={
                    'user_name': order.first_name,
                    'service_id': service_id,
                    'proposals': proposals,
                    'blacklist_token': (
                        RetargetingBlacklistToken.get_or_create_for_email(
                            email=order.email
                        ).token
                    ),
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class AssemblyServiceChooseDateReminderMailSendView(
    ServiceAssemblyServiceChooseDateReminderMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = AssemblyServiceChooseDateReminderMail


class ComplaintServiceChooseDateReminderMailSendView(
    ServiceAcceptedNotificationSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintServiceChooseDateReminderMail


class DeliveryDateProposalReminderRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        service_id = request.data['service_id']
        order_id = request.data['order_id']
        service_type = request.data['service_type']
        service_date_serializer = ServiceDateProposalEventSerializer(
            data=request.data['proposals'], many=True
        )
        service_date_serializer.is_valid(raise_exception=True)
        proposals = service_date_serializer.validated_data

        order = Order.objects.get(pk=order_id)

        DeliveryDateProposalReminderRequestEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            service_id=service_id,
            service_type=service_type,
            proposals=proposals,
        )

        return Response(status=status.HTTP_201_CREATED)


class ServiceChooseDateMailSendView:
    def get_mail_class(self, is_free_return=False):
        return self.mail_class

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']

        service_serializer = ServiceDeserializer(data=data_html['service'])
        service_serializer.is_valid(raise_exception=True)

        service_dto = service_serializer.validated_data
        order_id = data_html['order_id']

        proposals_serializer = ServiceDateProposalSerializer(
            data=data_html['proposals'], many=True
        )
        proposals_serializer.is_valid(raise_exception=True)
        proposals = proposals_serializer.validated_data

        order = Order.objects.get(pk=order_id)
        is_free_return = data_html.get('is_free_return', None)

        with translation.override(language=order.owner.profile.language):
            mail_class = self.get_mail_class(is_free_return)
            mail_class(
                to_address=order.email,
                data_html=self._get_data_html(order, service_dto, proposals),
                topic_variables={'order_id': order.id},
            ).send()

        return Response(status=status.HTTP_201_CREATED)

    def _get_data_html(self, order, service_dto, proposals) -> dict:
        return {
            'user_name': order.first_name,
            'service_id': service_dto.id,
            'duration': service_dto.duration,
            'proposals': proposals,
            'expiration_date': service_dto.min_expiration_date,
            'expiration_time': datetime.time(23, 59),
            'blacklist_token': (
                RetargetingBlacklistToken.get_or_create_for_email(
                    email=order.email
                ).token
            ),
        }


class AssemblyServiceChooseDateMailSendView(ServiceChooseDateMailSendView, APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = AssemblyServiceChooseDateMail

    def _get_data_html(self, order, service_dto, proposals) -> dict:
        return {
            'user_name': order.first_name,
            'service_id': service_dto.id,
            'is_estimated_delivery_date': order.is_estimated_delivery_date,
            'duration': service_dto.duration,
            'proposals': proposals,
            'expiration_date': service_dto.min_expiration_date,
            'expiration_time': datetime.time(23, 59),
            'blacklist_token': (
                RetargetingBlacklistToken.get_or_create_for_email(
                    email=order.email
                ).token
            ),
        }


class ComplaintServiceChooseDateMailSendView(ServiceChooseDateMailSendView, APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintServiceChooseDateMail
    free_return_mail_class = ComplaintServiceFreeReturnChooseDateMail

    def get_mail_class(self, is_free_return=False):
        return self.mail_class if not is_free_return else self.free_return_mail_class


class DeliveryDateProposalRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']
        service_type = request.data['service_type']

        service_serializer = ServiceDeserializer(data=request.data['service'])
        service_serializer.is_valid(raise_exception=True)
        service_dto = service_serializer.validated_data

        service_date_serializer = ServiceDateProposalEventSerializer(
            data=request.data['proposals'], many=True
        )
        service_date_serializer.is_valid(raise_exception=True)
        proposals = service_date_serializer.validated_data

        order = Order.objects.get(pk=order_id)

        DeliveryDateProposalRequestEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            service_type=service_type,
            service_id=service_dto.id,
            duration=service_dto.duration,
            proposals=proposals,
            expiration_date=service_dto.min_expiration_date,
            expiration_time=datetime.time(23, 59),
        )

        return Response(status=status.HTTP_201_CREATED)


class ServiceDateChosenConfirmationMailSendView:
    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']

        # Deserialize
        service_date_serializer = ServiceDateProposalSerializer(
            data=data_html['accepted_date']
        )
        service_date_serializer.is_valid(raise_exception=True)
        accepted_date = service_date_serializer.validated_data

        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=order.email,
                data_html={
                    'user_name': order.first_name,
                    'service_date': accepted_date.date,
                    'service_from_hour': accepted_date.from_hour,
                    'service_to_hour': accepted_date.to_hour,
                    'blacklist_token': (
                        RetargetingBlacklistToken.get_or_create_for_email(
                            email=order.email
                        ).token
                    ),
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class AssemblyServiceDateChosenConfirmationMailSendView(
    ServiceDateChosenConfirmationMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = AssemblyServiceDateChosenConfirmationMail


class ComplaintServiceDateChosenConfirmationMailSendView(
    ServiceDateChosenConfirmationMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ComplaintServiceDateChosenConfirmationMail


class ServiceDeliveryDateChosenEventView(
    ServiceDateChosenConfirmationMailSendView, APIView
):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']
        service_type = request.data['service_type']

        # Deserialize
        service_date_serializer = ServiceDateProposalEventSerializer(
            data=request.data['accepted_date']
        )
        service_date_serializer.is_valid(raise_exception=True)

        accepted_date = service_date_serializer.validated_data
        order = Order.objects.get(pk=order_id)

        ServiceDeliveryDateChosenEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            service_type=service_type,
            accepted_proposal=accepted_date,
        )

        return Response(status=status.HTTP_201_CREATED)


class Email24ConfirmationLogisticSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = Email24ConfirmationLogistic

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        to_address = request.data['to_address']
        order_id = data_html['order_id']

        email24_id = data_html['email24_id']
        email24_elevator_floor_info = data_html['email24_elevator_floor_info']
        email24_note = data_html['email24_note']

        order = Order.objects.get(pk=order_id)
        manufacturer = order.get_manufacturer_name()

        with translation.override(language=order.owner.profile.language):
            self.mail_class(
                to_address=to_address,
                data_html={
                    'order_id': order.id,
                    'manufacturer': manufacturer,
                    'email24_id': email24_id,
                    'email24_elevator_floor_info': email24_elevator_floor_info,
                    'email24_note': email24_note,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class Email24LogisticConfirmationReadyEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        to_address = request.data['to_address']
        order_id = request.data['order_id']

        email24_id = request.data['email24_id']
        email24_elevator_floor_info = request.data['email24_elevator_floor_info']
        email24_note = request.data['email24_note']

        order = Order.objects.get(pk=order_id)
        manufacturer = order.get_manufacturer_name()

        Email24LogisticConfirmationReadyEvent(
            user=User.objects.get(username='admin'),
            email=to_address,
            order_id=order.id,
            manufacturer=manufacturer,
            email24_id=email24_id,
            email24_elevator_floor_info=email24_elevator_floor_info,
            email24_note=email24_note,
        )

        return Response(status=status.HTTP_201_CREATED)


class Email24ConfirmationSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = Email24Confirmation

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        login_access_token = data_html['login_access_token']

        order = Order.objects.get(pk=order_id)

        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'username': order.first_name,
                    'blacklist_token': token,
                    'login_access_token': login_access_token,
                    'order_number': order.id,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class DomesticInjectionEmailSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)
    mail_class = DomesticInjectionEmail
    serializer_class = DomesticInjectionEmailSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        domestic_injection = serializer.validated_data
        order = Order.objects.get(pk=domestic_injection.order_id)
        carrier = domestic_injection.carrier
        days = domestic_injection.days
        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'user_name': order.first_name,
                    'carrier': carrier,
                    'days': str(days),
                    'blacklist_token': token,
                },
            ).send()
        return Response(status=status.HTTP_201_CREATED)


class Email24DeliveryConfirmationReadyEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']
        login_access_token = request.data['login_access_token']

        order = Order.objects.get(pk=order_id)

        Email24DeliveryConfirmationReadyEvent(
            user=order.owner,
            email=order.email,
            order_id=order_id,
            email24_lat=login_access_token,
        )

        return Response(status=status.HTTP_201_CREATED)


class Email24DeliveryDetailProposalSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = Email24DeliveryDetailProposal

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        login_access_token = data_html['login_access_token']
        expiration_date = data_html['expiration_date']
        expiration_hour = data_html['expiration_hour']
        order = Order.objects.get(pk=order_id)

        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'username': order.first_name,
                    'blacklist_token': token,
                    'login_access_token': login_access_token,
                    'order_number': order.id,
                    'is_estimated_delivery_date': order.is_estimated_delivery_date,
                    'expiration_date': expiration_date,
                    'expiration_hour': expiration_hour,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class Email24DeliveryDetailProposalReadyEvent(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']
        login_access_token = request.data['login_access_token']

        order = Order.objects.get(pk=order_id)
        Email24DeliveryProposalReadyEvent(
            user=order.owner,
            email=order.email,
            order_id=order_id,
            email24_lat=login_access_token,
        )

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeslotsProposalMailSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = DeliveryTimeslotsProposalMail

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        login_access_token = data_html['login_access_token']

        dtf_id = data_html['dtf_id']
        is_email24_sent = data_html['is_email24_sent']
        minimum_slots_number = data_html['minimum_slots_number']

        order = Order.objects.get(pk=order_id)

        if is_email24_sent:
            email_topic = _('mailing_dtf_proposal_after_email24_subject')
        else:
            email_topic = _('mailing_dtf_proposal_subject')

        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'dtf_id': dtf_id,
                    'username': order.first_name,
                    'blacklist_token': token,
                    'login_access_token': login_access_token,
                    'order_id': order.pk,
                    'is_estimated_delivery_date': order.is_estimated_delivery_date,
                    'is_email24_sent': is_email24_sent,
                    'minimal_timeslots': minimum_slots_number,
                },
                topic=email_topic,
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeFrameProposalReadyEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']

        dtf_id = request.data['dtf_id']
        is_email24_sent = request.data['is_email24_sent']
        minimum_slots_number = request.data['minimum_slots_number']
        proposal_type = request.data['proposal_type']

        order = Order.objects.get(pk=order_id)

        DeliveryTimeFrameProposalReadyEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            dtf_id=dtf_id,
            proposal_type=proposal_type,
            is_email24_sent=is_email24_sent,
            minimal_timeslots=minimum_slots_number,
        )

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeslotsProposalResendAcceptedMailView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = DeliveryTimeslotsProposalResendAcceptedMail

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        login_access_token = data_html['login_access_token']

        dtf_id = data_html['dtf_id']
        is_email24_sent = data_html['is_email24_sent']
        minimum_slots_number = data_html['minimum_slots_number']

        order = Order.objects.get(pk=order_id)

        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'dtf_id': dtf_id,
                    'username': order.first_name,
                    'blacklist_token': token,
                    'login_access_token': login_access_token,
                    'order_id': order.pk,
                    'is_email24_sent': is_email24_sent,
                    'minimal_timeslots': minimum_slots_number,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeslotsProposalResendNotAcceptedMailView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = DeliveryTimeslotsProposalResendNotAcceptedMail

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        login_access_token = data_html['login_access_token']

        dtf_id = data_html['dtf_id']
        is_email24_sent = data_html['is_email24_sent']
        minimum_slots_number = data_html['minimum_slots_number']

        order = Order.objects.get(pk=order_id)

        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            self.mail_class(
                to_address=order.email,
                data_html={
                    'dtf_id': dtf_id,
                    'username': order.first_name,
                    'blacklist_token': token,
                    'login_access_token': login_access_token,
                    'order_id': order.pk,
                    'is_email24_sent': is_email24_sent,
                    'minimal_timeslots': minimum_slots_number,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)


class DeliverySettledSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    delivery_settled_mail = DeliverySettled
    delivery_settled_estimated_delivery_date_mail = DeliverySettledEstimatedDeliveryDate

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']

        accepted_slot_date = data_html['accepted_slot_date']
        accepted_slot_start_hour = data_html['accepted_slot_start_hour']
        accepted_slot_end_hour = data_html['accepted_slot_end_hour']

        order = Order.objects.get(pk=order_id)
        with translation.override(language=order.owner.profile.language):
            token = RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token
            mail_class = self._get_mail_class(order)
            mail_class(
                to_address=order.email,
                data_html={
                    'username': order.first_name,
                    'date': accepted_slot_date,
                    'start_hour': accepted_slot_start_hour,
                    'end_hour': accepted_slot_end_hour,
                    'blacklist_token': token,
                    'order': order,
                },
            ).send()

        return Response(status=status.HTTP_201_CREATED)

    def _get_mail_class(self, order):
        if not order.is_estimated_delivery_date:
            return self.delivery_settled_mail
        return self.delivery_settled_estimated_delivery_date_mail


class DeliverySettledSendEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        order_id = request.data['order_id']

        accepted_slot_date = request.data['accepted_slot_date']
        accepted_slot_start_hour = request.data['accepted_slot_start_hour']
        accepted_slot_end_hour = request.data['accepted_slot_end_hour']

        order = Order.objects.get(pk=order_id)
        DeliverySettledEvent(
            user=order.owner,
            email=order.email,
            order_id=order.id,
            date=accepted_slot_date,
            start_hour=accepted_slot_start_hour,
            end_hour=accepted_slot_end_hour,
        )

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeFrameDateChangedNotificationSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = DeliveryTimeFrameDateChangedNotification

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        to_address = request.data['to_address']
        dtf_id = data_html['dtf_id']
        order_ids = data_html['order_ids']
        manufactor = data_html['manufactor']

        self.mail_class(
            to_address=to_address,
            data_html={
                'dtf_id': dtf_id,
                'order_ids': order_ids,
                'manufactor': manufactor,
            },
        ).send()

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeFrameDateChangedRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        to_address = request.data['to_address']
        order_ids = request.data['order_ids']
        manufactor = request.data['manufactor']

        DeliveryTimeFrameDateChangedRequestEvent(
            user=User.objects.get(username='admin'),
            email=to_address,
            order_ids=order_ids,
            manufacturer=manufactor,
        )
        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeFrameNewDateNotificationSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = DeliveryTimeFrameNewDateNotification

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        to_address = request.data['to_address']
        dtf_id = data_html['dtf_id']
        order_ids = data_html['order_ids']
        manufactor = data_html['manufactor']

        self.mail_class(
            to_address=to_address,
            data_html={
                'dtf_id': dtf_id,
                'order_ids': order_ids,
                'manufactor': manufactor,
            },
        ).send()

        return Response(status=status.HTTP_201_CREATED)


class DeliveryTimeFrameNewDateRequestEventView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        to_address = request.data['to_address']
        order_ids = request.data['order_ids']
        manufactor = request.data['manufactor']

        DeliveryTimeFrameNewDateRequestEvent(
            user=User.objects.get(username='admin'),
            email=to_address,
            order_ids=order_ids,
            manufacturer=manufactor,
        )

        return Response(status=status.HTTP_201_CREATED)


class ProductShippedRegularMailSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = ProductShippedRegularMail

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        tracking_link = data_html['tracking_link']
        tracking_number = data_html['tracking_number']

        order = Order.objects.get(pk=order_id)
        order_language = order.owner.profile.language
        furniture_type = (
            order.items.all().order_by('-region_price').first().order_item.default_title
        )

        self.mail_class(
            to_address=order.email,
            data_html={
                'furniture_type': furniture_type,
                'country': order.country,
                'tracking_number': tracking_number,
                'tracking_link': tracking_link,
            },
        ).send(language=order_language)

        return Response(status=status.HTTP_201_CREATED)


class ServiceProtocolContactCSMailSendView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    mail_class = CustomerServiceContactServiceProtocolMail

    def post(self, request, *args, **kwargs):
        data_html = request.data['data_html']
        order_id = data_html['order_id']
        message = data_html['message']

        self.mail_class(
            to_address='<EMAIL>',
            data_html={
                'order_id': order_id,
                'message': message,
            },
        ).send()

        return Response(status=status.HTTP_201_CREATED)
