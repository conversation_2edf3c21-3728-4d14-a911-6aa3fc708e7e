from django.db import models

from checkout.enums import ViesResponseStatus
from cstm_be.media_storage import private_media_storage
from custom.models import FileFieldWithHash


class ViesVatValidation(models.Model):
    vat_number = models.CharField(max_length=20)
    confirmation_pdf = FileFieldWithHash(
        upload_to='invoice/vies_vat_validation/%Y/%m',
        storage=private_media_storage,
        max_length=150,
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    response_status = models.IntegerField(
        choices=ViesResponseStatus.choices,
        default=ViesResponseStatus.VALID,
    )
