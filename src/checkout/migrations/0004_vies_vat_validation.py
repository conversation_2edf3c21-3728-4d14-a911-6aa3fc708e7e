# Generated by Django 4.1.9 on 2024-04-18 17:09

import django.core.files.storage

from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('checkout', '0003_delete_payment_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='ViesVatValidation',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('vat_number', models.CharField(max_length=20)),
                (
                    'confirmation_pdf',
                    custom.models.fields.FileFieldWithHash(
                        blank=True,
                        max_length=150,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='invoice/vies_vat_validation/%Y/%m',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
