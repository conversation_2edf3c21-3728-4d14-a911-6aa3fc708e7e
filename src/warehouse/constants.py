from custom.enums import ShelfType
from custom.enums.colors import Sofa01Color
from warehouse.tools import filter_single_sample_variants


class SottySampleBoxVariantTypes:
    @classmethod
    def get_types_with_corduroy(cls) -> set[int]:
        return {
            variant_type
            for variant_type, shelf_type_and_color in filter_single_sample_variants(
                shelf_types=[ShelfType.SOFA_TYPE01],
                colors=Sofa01Color.get_corduroy_colors(),
            )
        }

    @classmethod
    def get_types_without_corduroy(cls) -> set[int]:
        return {
            variant_type
            for variant_type, shelf_type_and_color in filter_single_sample_variants(
                shelf_types=[ShelfType.SOFA_TYPE01],
                colors=[
                    color
                    for color in Sofa01Color.get_active_colors()
                    if color not in Sofa01Color.get_corduroy_colors()
                ],
            )
        }
