import copy

from io import (
    BytesIO,
    StringIO,
)
from zipfile import ZipFile

from django.conf import settings
from django.db import transaction
from django.db.models import F
from django.template.loader import render_to_string
from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger

from custom.enums import LanguageEnum
from custom.utils.report_file import ReportFile
from mailing.templates import MissingSampleBoxStocksReportMail
from producers.cost_calculations import get_loss_factor
from producers.models import (
    Manufactor,
    ProductBatch,
)
from producers.services.match_usage import MatchUsageWithMaterialsHelper
from producers.utils import round_down_if_item
from production_margins.models import CustomPricingFactor
from warehouse.drewtur import DREWTUR_CODES
from warehouse.models import StockSampleBox

task_logger = get_task_logger('celery_task')


STOCK_SAMPLE_BOX_LEVEL_ALERT = 100


@shared_task
def check_info_about_missing_elements():
    stocks_that_need_refill = StockSampleBox.objects.filter(
        quantity__lte=STOCK_SAMPLE_BOX_LEVEL_ALERT,
    )

    if stocks_that_need_refill.exists():
        for _, email in settings.STOCKS_RECIPIENTS:
            mail = MissingSampleBoxStocksReportMail(
                email,
                data_html={'stocks': stocks_that_need_refill},
            )
            mail.send(language=LanguageEnum.EN)


def delete_excluded_element_from_material(copy_of_batch_data):
    for codename in (
        'packaging_cardboard_white-print_z-fold-1000mm',
        'packaging_cardboard_white-print_z-fold-1400mm',
    ):
        copy_of_batch_data['material_data'].pop(codename, None)


def update_drewtur_code(copy_of_batch_data, manufactor):
    # TODO: get codes from database
    if manufactor != 'Drewtur':
        return

    for codename in copy_of_batch_data['material_data']:
        drewtur_code = copy_of_batch_data['material_data'][codename]['drewtur_code'][3:]
        if drewtur_code in DREWTUR_CODES:
            drewtur_code = 'DR-{}'.format(drewtur_code)
            copy_of_batch_data['material_data'][codename]['drewtur_code'] = drewtur_code


def filter_and_update_batch_data(batch_data, manufactor):
    """
    Drop not needed data for novum.

    Because meble pl produces packaging for novum we filter out
    cardboard usage from usage files that we send to novum.
    """
    copy_of_batch_data = copy.deepcopy(batch_data)
    delete_excluded_element_from_material(copy_of_batch_data)
    update_drewtur_code(copy_of_batch_data, manufactor)

    for material_code_name, material_details in copy_of_batch_data[
        'material_data'
    ].items():
        manufactor = Manufactor.objects.get(name=manufactor)
        loss_factor = float(get_loss_factor(material_code_name, manufactor))
        material_details['usage'] = round(
            material_details['usage'] * (1 + loss_factor), 3
        )

    return copy_of_batch_data


@transaction.atomic
def material_details_per_codename(batch, match_usage_helper, elements_single_choice):
    details_per_codename = {}
    for item in batch.batch_items.all():
        for material_type, material_data in (
            item.get_serialized_product_info().get('materials', {}).items()
        ):
            if material_type == 'services_data':
                continue
            for material_name, material_details in material_data.items():
                if match_usage_helper.is_material_excluded(
                    material_name, material_type
                ):
                    continue
                stored_material_id = elements_single_choice[str(batch.pk)][
                    material_name
                ]

                custom_pricing_factor = CustomPricingFactor.objects.annotate(
                    code=F('manufacturer_code__code'), name=F('manufacturer_code__name')
                ).get(pk=stored_material_id)

                if custom_pricing_factor.codename not in details_per_codename:
                    details_per_codename[custom_pricing_factor.codename] = {'usage': 0}

                details_per_codename[custom_pricing_factor.codename]['drewtur_code'] = (
                    custom_pricing_factor.code or 'fake code'
                )
                details_per_codename[custom_pricing_factor.codename][
                    'name_pl'
                ] = custom_pricing_factor.name
                details_per_codename[custom_pricing_factor.codename][
                    'unit'
                ] = material_details['unit']
                details_per_codename[custom_pricing_factor.codename][
                    'material_type'
                ] = material_type
                details_per_codename[custom_pricing_factor.codename][
                    'usage'
                ] += material_details['usage']

    return details_per_codename


def generate_match_usage_with_materials_zip(
    batch_list,
    elements_single_choice,
) -> ReportFile:
    zip_file = BytesIO()
    log_file = StringIO()

    manufacturer = ''
    sum_batch_data = {
        'order_info': '{0}_{1}'.format(batch_list[0], batch_list[-1]),
        'material_data': {},
        'service_data': {},
    }

    batches = ProductBatch.objects.filter(pk__in=batch_list).prefetch_related(
        'batch_items'
    )
    batches_manufactor_ids = batches.values_list('manufactor_id', flat=True)
    match_usage_helper = MatchUsageWithMaterialsHelper(batches_manufactor_ids)

    with ZipFile(zip_file, mode='w') as zip_file_handle:
        for batch in batches:
            batch_data = {
                'order_info': batch,
                'material_data': {},
            }

            try:
                manufacturer = batch.manufactor.name
                batch_data['material_data'] = material_details_per_codename(
                    batch, match_usage_helper, elements_single_choice
                )
                for material_name, material_data in batch_data['material_data'].items():
                    if match_usage_helper.should_usage_be_increased_by_loss_factor(
                        material_type=material_data['material_type'],
                        material_name=material_name,
                        manufactor_name=manufacturer,
                    ):
                        material_data[
                            'usage'
                        ] = match_usage_helper.get_usage_increased_by_loss_factor(
                            material_name=material_name,
                            manufacturer_id=batch.manufactor.id,
                            current_value=material_data['usage'],
                        )
                    material_data.pop('material_type', None)
                    if material_name not in sum_batch_data['material_data']:
                        sum_batch_data['material_data'][material_name] = material_data
                    else:
                        sum_batch_data['material_data'][material_name][
                            'usage'
                        ] += material_data['usage']
                for item in batch.batch_items.all():
                    for service_codename, usage in list(
                        item.get_serialized_product_info()
                        .get('materials', {})
                        .get('services_data', {})
                        .items()
                    ):
                        if service_codename not in sum_batch_data['service_data']:
                            sum_batch_data['service_data'][service_codename] = usage
                        else:
                            sum_batch_data['service_data'][service_codename][
                                'usage'
                            ] += usage['usage']
            except Exception as e:
                task_logger.error(e, exc_info=True)
                log_file.write('{}\n'.format(str(e)))
            csv = render_to_string('admin/weaver_import_usage.csv', context=batch_data)
            zip_file_handle.writestr(
                '{}_weaver_zuzycie_{}.csv'.format(batch, manufacturer),
                csv.encode('utf8'),
            )
            csv = render_to_string(
                'admin/novum_import_usage.csv',
                context=filter_and_update_batch_data(batch_data, manufacturer),
            )
            zip_file_handle.writestr(
                '{}_csv_zuzycie_{}.csv'.format(batch, manufacturer),
                csv.encode('cp1250'),
            )

        round_down_usage_if_needed(sum_batch_data['material_data'])
        round_down_usage_if_needed(sum_batch_data['service_data'])
        usage_file_name = get_match_usage_file_name(batch_list)
        xml_file_name = usage_file_name.replace('zuzycie', 'zamowienie')
        sum_xml = render_to_string(
            'admin/drewtur_material_usage.xml', context=sum_batch_data
        )
        zip_file_handle.writestr(
            f'{xml_file_name}.xml',
            sum_xml.encode('utf8'),
        )
        csv = render_to_string(
            'admin/novum_import_usage.csv',
            context=filter_and_update_batch_data(sum_batch_data, manufacturer),
        )
        zip_file_handle.writestr(
            '{}_{}_csv_sumaryczne_zuzycie.csv'.format(batch_list[0], batch_list[-1]),
            csv.encode('cp1250'),
        )

        csv = render_to_string('admin/service_usage.csv', context=sum_batch_data)
        zip_file_handle.writestr(
            '{}_{}_csv_sumaryczne_uslugi.csv'.format(batch_list[0], batch_list[-1]),
            csv.encode('cp1250'),
        )

        if log_file.tell() > 0:
            zip_file_handle.writestr('log.txt', log_file.getvalue().encode('utf8'))
    zip_file.seek(0)
    return ReportFile(content=zip_file.getvalue(), name=f'{usage_file_name}.zip')


def round_down_usage_if_needed(materials_data):
    for material_name, material_data in materials_data.items():
        usage = round_down_if_item(
            material_data['usage'],
            material_name,
            material_data['unit'],
        )
        materials_data[material_name]['usage'] = usage


@shared_task
def send_match_usage_with_materials_zip(
    email,
    batches,
    elements_single_choice,
):
    report_file = generate_match_usage_with_materials_zip(
        batches,
        elements_single_choice,
    )
    report_file.send_as_email_attachment(
        emails=[email] if email else [],
        subject='Match with materials',
        body='Usage materials',
    )


def get_match_usage_file_name(batches):
    product_batches = ProductBatch.objects.filter(id__in=batches).order_by('id')

    if all(batch.has_complaints for batch in product_batches):
        now_time = timezone.now()
        complaint_suffix = '_reklamacyjne_CW{}_{}'.format(
            now_time.isocalendar()[1],
            now_time.strftime('%Y%m%d'),
        )
    else:
        complaint_suffix = ''
    return f'B{batches[0]}-B{batches[-1]}_zuzycie{complaint_suffix}'
