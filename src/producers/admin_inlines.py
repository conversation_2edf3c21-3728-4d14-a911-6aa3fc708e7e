import logging

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from producers.admin_mixins import ProductDetailsFilesChangelogMixin
from producers.models import (
    ProductPriorityHistory,
    ProductStatusHistory,
)
from producers.models_split.product_batch_details import (
    ProductBatchDetailsJetty,
    ProductBatchDetailsWatty,
)
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsSotty,
    ProductDetailsWatty,
)

logger = logging.getLogger('producers')


class ProductStatusHistoryInline(admin.TabularInline):
    model = ProductStatusHistory
    extra = 0
    ordering = ('-changed_at',)
    raw_id_fields = ('owner',)

    def has_add_permission(self, request, obj=None):
        return False


class ProductPriorityHistoryInline(admin.TabularInline):
    model = ProductPriorityHistory
    extra = 0
    ordering = ('-created_at',)
    raw_id_fields = ('owner',)

    def has_add_permission(self, request, obj=None):
        return False


class ProductDetailsInline(admin.TabularInline):
    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class ProductDetailsStackedInline(admin.StackedInline):
    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class ProductDetailsFurnitureInline(ProductDetailsStackedInline):
    template = 'admin/producers/product/detailsjetty_inline.html'
    fieldsets = (
        ('Pictures', {'fields': ('get_preview_img',)}),
        (
            'Serialization',
            {'fields': ('display_cached_serialization',)},
        ),
    )
    readonly_fields = (
        'display_cached_serialization',
        'get_preview_img',
    )

    @staticmethod
    def get_preview_img(obj):
        image_url = (
            obj.product.order_item.order_item.preview.url
            if obj.product.order_item
            and obj.product.order_item.order_item
            and obj.product.order_item.order_item.preview
            else ''
        )

        return format_html(
            '<img style="height: 150px;" src="{}"/>',
            image_url,
        )

    def get_queryset(self, request):
        return super().get_queryset(request).defer('cached_serialization')

    @staticmethod
    def display_cached_serialization(obj):
        json_url = reverse(
            'admin:admin_json_production_view',
            args=[obj.product_id, 'json'],
        )
        return format_html(
            '<a href="{}" target="_blank">See cached serialization</a>',
            json_url,
        )


class ProductDetailsWattyInline(ProductDetailsFurnitureInline):
    model = ProductDetailsWatty
    fieldsets = ProductDetailsFurnitureInline.fieldsets + (
        (
            'Files',
            {
                'fields': (
                    'instruction',
                    'front_view',
                    'packaging_instruction',
                    'cnc_connections_zip',
                )
            },
        ),
    )


class ProductDetailsSottyInline(ProductDetailsFurnitureInline):
    model = ProductDetailsSotty
    fieldsets = ProductDetailsFurnitureInline.fieldsets + (
        (
            'Files',
            {
                'fields': (
                    'front_view',
                    'labels_packaging',
                    'instruction',
                )
            },
        ),
    )


class ProductDetailsJettyInline(ProductDetailsFurnitureInline):
    model = ProductDetailsJetty
    fieldsets = ProductDetailsFurnitureInline.fieldsets + (
        (
            'Files',
            {
                'fields': (
                    'instruction',
                    'front_view',
                    'packaging_instruction',
                    'cnc_connections_zip',
                    'horizontals_pdf',
                    'vertical_labels',
                )
            },
        ),
    )


class ProductBatchDetailsJettyInline(
    ProductDetailsFilesChangelogMixin,
    ProductDetailsStackedInline,
):
    model = ProductBatchDetailsJetty
    fieldsets = (
        (
            'Nestings',
            {
                'fields': (
                    'nesting_zip',
                    'nesting',
                    'nesting_backs',
                    'nesting_drawers',
                    'nesting_bottom_drawers',
                    'nesting_front_drawers',
                    'nesting_handle_blende',
                    'nesting_desk_beam',
                ),
            },
        ),
        (
            'Packaging',
            {
                'fields': (
                    'packaging_csvs',
                    'cardboard_beams_xls',
                ),
            },
        ),
        (
            'Labels',
            {
                'fields': (
                    'labels_elements',
                    'labels_packaging',
                    'labels_logistic',
                    'labels_verticals',
                    'nameplate',
                ),
            },
        ),
        ('Meble pl packages', {'fields': ('meblepl_zip',)}),
        (
            'Novum additional files',
            {
                'fields': (
                    'horizontal_pdfs_zip',
                    'accessories_packaging_list',
                )
            },
        ),
        (
            'Additional files',
            {'fields': ('rotated_elements',)},
        ),
    ) + ProductDetailsFilesChangelogMixin.fieldsets
    readonly_fields = ProductDetailsFilesChangelogMixin.readonly_fields


class ProductBatchDetailsWattyInline(
    ProductDetailsFilesChangelogMixin,
    ProductDetailsStackedInline,
):
    model = ProductBatchDetailsWatty
    fieldsets = (
        (
            'Nestings',
            {
                'fields': (
                    'nesting_zip',
                    'nesting_bar',
                    'nesting_mask',
                    'nesting_drawer_synchro',
                    'nesting_hang_slat',
                    'nesting_led_profile',
                    'lighting_completion_list',
                )
            },
        ),
        ('Packaging', {'fields': ('packaging_csvs',)}),
        (
            'Labels',
            {
                'fields': (
                    'labels_elements',
                    'labels_packaging',
                    'labels_adapters',
                    'labels_logistic',
                    'nameplate',
                )
            },
        ),
        (
            'Additional files',
            {'fields': ('rotated_elements',)},
        ),
    ) + ProductDetailsFilesChangelogMixin.fieldsets
    readonly_fields = ProductDetailsFilesChangelogMixin.readonly_fields
