import logging

from rest_framework import serializers

from custom.enums import Furniture
from orders.internal_api.serializers import ForLogisticOrderItemSerializer
from producers.models import (
    Manufactor,
    Product,
)
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsWatty,
)

logger = logging.getLogger('producers')


class ForLogisticManufactorSerializer(serializers.ModelSerializer):
    owner_id = serializers.IntegerField(source='owner.id', allow_null=True)

    class Meta:
        model = Manufactor
        fields = ['id', 'name', 'invoice_city', 'owner_id']


class ForLogisticElementSerializer(serializers.Serializer):
    package_info_pack_id = serializers.IntegerField(
        source='package_info.pack_id', allow_null=True
    )
    surname = serializers.CharField(allow_blank=True)

    class Meta:
        fields = ['package_info_pack_id', 'surname']


class ForLogisticProductDetailsSerializer(serializers.ModelSerializer):
    instruction = serializers.Char<PERSON>ield(source='instruction.name')
    front_view = serializers.Char<PERSON>ield(source='front_view.name')
    packaging_instruction = serializers.Char<PERSON>ield(source='packaging_instruction.name')

    class Meta:
        fields = (
            'instruction',
            'front_view',
            'packaging_instruction',
        )


class ForLogisticProductDetailsJettySerializer(
    ForLogisticProductDetailsSerializer,
):
    class Meta(ForLogisticProductDetailsSerializer.Meta):
        model = ProductDetailsJetty


class ForLogisticProductDetailsWattySerializer(
    ForLogisticProductDetailsSerializer,
):
    class Meta(ForLogisticProductDetailsSerializer.Meta):
        model = ProductDetailsWatty


class ForLogisticPackagingSerializer(serializers.Serializer):
    dim_x = serializers.IntegerField()
    dim_y = serializers.IntegerField()
    dim_z = serializers.IntegerField()
    adjusted_weight = serializers.FloatField()
    weight = serializers.FloatField()
    id_production = serializers.IntegerField()
    pack_id = serializers.IntegerField()
    all_elements = serializers.SerializerMethodField()

    def get_all_elements(self, obj):
        return [
            {
                'ELEM_TYPE': element.ELEM_TYPE,
                'full_name': element.full_name,
            }
            for element in obj.all_elements
        ]


class ForLogisticProductSerializer(serializers.ModelSerializer):
    details = serializers.SerializerMethodField()
    product_id = serializers.SerializerMethodField(read_only=False)
    manufactor = ForLogisticManufactorSerializer()
    weight_brutto = serializers.SerializerMethodField(read_only=False)
    weight_netto = serializers.SerializerMethodField(read_only=False)
    order_item_serialized = serializers.SerializerMethodField(read_only=False)
    packaging = ForLogisticPackagingSerializer(source='get_packaging', many=True)
    damagedata_set = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    last_tbs_changed_at = serializers.DateField(
        source='get_last_tbs_changed_at', allow_null=True
    )
    order_item = ForLogisticOrderItemSerializer()
    elements = ForLogisticElementSerializer(
        source='details_serialized.cached_serialization.item.elements',
        many=True,
        read_only=True,
    )
    sliders = serializers.SerializerMethodField(read_only=True)
    preview_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Product
        fields = (
            'id',
            'product_id',
            'logistic_order',
            'batch_id',
            'details',
            'order',
            'order_item',
            'copy_of',
            'manufactor',
            'warehouse',
            'status',
            'previous_status',
            'cached_product_type',
            'priority',
            'source_priority',
            'delivery_priority',
            'requested_postponed_delivery_date',
            'expected_postponed_release_date',
            'created_at',
            'updated_at',
            'delivered_at',
            'added_referral',
            'has_errors',
            'cached_depth',
            'cached_has_top_or_bottom_storage',
            'cached_shelf_type',
            'is_extended',
            'is_sidebohr',
            'has_doors',
            'has_drawers',
            'has_lighting',
            'cached_material',
            'cached_material_color',
            'cached_area',
            'cached_configurator_type',
            'cached_physical_product_version',
            'reproduction_time_in_days',
            'additional_order_items',
            'weight_brutto',
            'weight_netto',
            'packaging',
            'order_item_serialized',
            'damagedata_set',
            'last_tbs_changed_at',
            'elements',
            'sliders',
            'deleted',
            'preview_url',
        )

    def get_sliders(self, obj):
        return obj.get_sliders()

    def get_product_id(self, obj):
        return obj.get_product_id()

    def get_weight_brutto(self, obj):
        return obj.get_weight_brutto()

    def get_weight_netto(self, obj):
        try:
            return obj.get_weight_netto()
        except (AttributeError, KeyError):
            logger.error('Error on product %s get_weight_netto' % obj.id, exc_info=True)
            return 0

    def get_details(self, obj):
        details_serializers = {
            Furniture.jetty.value: ForLogisticProductDetailsJettySerializer,
            Furniture.watty.value: ForLogisticProductDetailsWattySerializer,
        }
        serializer = details_serializers.get(obj.product_type)
        if serializer:
            return serializer(obj.details).data
        return {}

    def get_order_item_serialized(self, obj):
        if not obj.order_item_serialized:
            return {}
        return {
            'category': obj.order_item_serialized.get('category'),
            'size_txt': obj.order_item_serialized.get('size_txt'),
            'color_name': obj.order_item_serialized.get('color_name'),
        }

    @staticmethod
    def get_preview_url(obj):
        return (
            obj.order_item.order_item.preview.url
            if obj.order_item
            and obj.order_item.order_item
            and obj.order_item.order_item.preview
            else ''
        )
