import pytest

from orders.enums import OrderStatus
from orders.tests.factories import OrderFactory
from producers.choices import ProductStatus
from producers.tests.factories import ProductFactory


@pytest.mark.django_db
class TestProductPostSaveSignalChangeOrderStatus:
    def test_change_status_when_product_status_changed_to_be_shipped(self):
        order = OrderFactory(status=OrderStatus.IN_PRODUCTION)
        product = ProductFactory(
            order=order, status=ProductStatus.ASSIGNED_TO_PRODUCTION
        )
        product.status = ProductStatus.TO_BE_SHIPPED
        product.save()
        product.refresh_from_db()
        assert product.order.status == OrderStatus.TO_BE_SHIPPED

    def test_no_change_status_when_one_of_product_is_ready_to_be_shipped(self):
        order = OrderFactory(status=OrderStatus.IN_PRODUCTION)
        products = ProductFactory.create_batch(
            size=2,
            status=ProductStatus.ASSIGNED_TO_PRODUCTION,
            order=order,
        )
        product = products[0]
        product.status = ProductStatus.TO_BE_SHIPPED
        product.save()
        product.refresh_from_db()
        assert product.order.status == OrderStatus.IN_PRODUCTION
