{% load i18n admin_urls static %}

{% include "admin/edit_inline/stacked.html" %}

<script type="text/javascript">
(function($) {
  $("#{{ inline_admin_formset.formset.prefix }}-group .inline-related").stackedFormset({
    prefix: '{{ inline_admin_formset.formset.prefix }}',
    adminStaticPrefix: '{% static "admin/" %}',
    deleteText: "{% translate "Remove" %}",
    addText: "{% blocktrans with verbose_name=inline_admin_formset.opts.verbose_name|capfirst %}Add another {{ verbose_name }}{% endblocktrans %}"
  });
})(django.jQuery);
</script>
<style type="text/css">
    .inline-group[data-inline-type="stacked"] .form-row {
        display: block;
        width: 50%;
        float: left;
    }
</style>
