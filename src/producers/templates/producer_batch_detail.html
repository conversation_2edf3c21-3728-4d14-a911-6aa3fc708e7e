{% extends "producer_base.html" %}
{% load humanize %}
{% load defaulttags %}
{% load static %}

{% block extra_head %}
    <script src="//ajax.googleapis.com/ajax/libs/jquery/2.1.0/jquery.min.js"></script>
    <link rel="stylesheet" href="{% static 'css/calendar.css' %}">
    <link rel="stylesheet" href="{% static 'css/datepicker3.css' %}">
    <style>
        .small-table td {
            padding:10px;
        }
    </style>
    <script type="text/javascript" src="{% static 'js/bootstrap-datepicker.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/lodash.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/ejs.js' %}"></script>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        const csrfToken = "{{ csrf_token }}";
        let already_in_progress = false;

        document.querySelectorAll('.button-ready-to-pickup').forEach(function (button) {
            button.addEventListener('click', function (event) {
                if (already_in_progress) {
                    alert('Regał już zwalniany');
                    return;
                }

                const btn = event.target;
                const parent = btn.closest('form') || btn.parentElement;

                const productInput = parent.querySelector('.input_product_id');
                const productId = productInput ? productInput.value : null;

                if (!productId) {
                    alert('Brakuje identyfikatora produktu.');
                    return;
                }

                btn.textContent = '...w trakcie';
                btn.disabled = true;
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-success');
                already_in_progress = true;

                fetch('/api/v1/producers/producer_can_be_picked_up/{{ batch_list.0.id }}/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({ product: productId }),
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => {
                                throw new Error(text);
                            });
                        }
                        location.reload();
                    })
                    .catch(error => {
                        btn.disabled = false;
                        already_in_progress = false;
                        btn.innerHTML = 'Coś poszło nie tak.<br>Skontaktuj się z zespołem.';
                        alert(error.message || 'Wystąpił nieznany błąd.');
                    });
            });
        });
    });
</script>

{% endblock %}

{% block content %}
    <hr>
    <div class="container-body">
        <div id="batches-in-production">
            {% for batch in batch_list %}
                <h3> Batch {% if batch.has_complaints %} REKLAMACYJNY {% endif %}{{ batch.id }} z {{ batch.batch_items.count}} produktami - {{ batch.get_status_display }} </h3>
                {% if batch.status == 3 %}
                    Data odebrania: {{ batch.picked_up_at|date }} <br/>
                {% endif %}
                <br/>
                {% if batch.product_type == "jetty" %}
                    {% if batch.details.nesting %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.nesting.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój elementy podstawowe</a>
                    {% else %}
                        <button type="button" class="btn btn-default " disabled="disabled">Brakuje rozkroju elementó podstawowych</button>
                    {% endif %}

                    {% if batch.details.labels_elements %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.labels_elements.url }}"> <span class="glyphicon glyphicon-download"></span> Etykiety na elementy</a>
                    {% else %}
                        <button type="button" class="btn btn-default " disabled="disabled">Brakuje etykiet na elementy</button>
                    {% endif %}

                    {% if batch.details.labels_packaging %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.labels_packaging.url }}"> <span class="glyphicon glyphicon-download"></span> Etykiety na paczki</a>
                    {% else %}
                        <button type="button" class="btn btn-default " disabled="disabled">Brakuje etykiet na paczki</button>
                    {% endif %}
                    <br/>
                    <br/>
                    {% if batch.details.nesting_backs %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.nesting_backs.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój ściany tylne</a>
                    {% endif %}

                    {% if batch.details.nesting_drawers %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.nesting_drawers.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój elementy szuflad</a>
                    {% endif %}

                    {% if batch.details.drawers_drilling %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.drawers_drilling.url }}"> <span class="glyphicon glyphicon-download"></span> Wiercenie szuflad</a>
                    {% endif %}


                    {% if batch.details.nesting_bottom_drawers %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.nesting_bottom_drawers.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój dno szuflad</a>
                    {% endif %}

                    {% if batch.details.nesting_front_drawers %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.nesting_front_drawers.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój drzwi frontów szuflad </a>
                    {% endif %}

                    {% if batch.details.packaging_csvs %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.packaging_csvs.url }}"> <span class="glyphicon glyphicon-download"></span> Rozkrój pakowanie </a>
                    {% endif %}

                    {% if batch.details.horizontal_pdfs_zip and batch.manufactor and batch.manufactor.id %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.horizontal_pdfs_zip.url }}"> <span class="glyphicon glyphicon-download"></span> Horizontals </a>
                    {% endif %}

                    {% if batch.details.accessories_packaging_list and batch.manufactor and batch.manufactor.id %}
                        <a type="button" class="btn btn-info" href="{{ batch.details.accessories_packaging_list.url }}"> <span class="glyphicon glyphicon-download"></span> Lista pakowania akcesoriów </a>
                    {% endif %}
                {% endif %}
                {% if item.manufactor.owner == request.user %}
                    {% if batch.status == 2 %}
                        <br/>
                        <a href="/pages/api/v1/producer_sent_customer/{{ batch.id }}/">
                            <button type="button" class="btn btn-success">Batch odebrany</button>
                        </a>
                    {% elif batch.status == 3 %}
                        <br/>
                        <a href="/pages/api/v1/producer_sent_customer/{{ batch.id }}/">
                            <button type="button" class="btn btn-success">Cofnij do produkcji</button>
                        </a>
                    {% endif %}
                {% endif %}
                <br/>
                {% if batch.batch_items.count > 0 %}
                    <br/>
                    Produkty w batchu:
                    <table class="table table-bordered table-hover ng-scope">
                        <thead>
                            <tr>
                                <th>Order Id</th>
                                <th>Priorytet</th>
                                <th>Paczki</th>
                                <th>Gotowe paczki</th>
                                <th>Standard</th>
                                {% if batch.has_complaints %}
                                    <th> Podsumowanie reklamacji</th>
                                    <th> Pliki produkcyjne z Bazowego Produktu</th>
                                    <th> Pliki produkcyjne reklamacji</th>
                                {% else %}
                                    <th> Pliki produkcyjne</th>
                                {% endif %}
                                <th> Uwagi</th>
                                <th> Akcje</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in batch.sorted_batch_items %}
                                <tr>
                                    <td>{{ item.order_id }} / {{ item.id }}</td>
                                    <td>{{ item.get_source_priority_display }} {{ item.get_priority_display}}</td>
                                    <td>{{ item.get_packaging_quantity }}</td>
                                    <td>{{ item.released_packages }}</td>
                                    <td>{{ item.get_cached_physical_product_version_display }}</td>
                                    {% if batch.has_complaints %}
                                        <td>
                                            Bazowy produkty: <strong>{{ item.copy_of.get_product_id }}</strong> <br/>
                                        </td>
                                        <td>
                                            {% if item.copy_of.details.instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji</button>
                                            {% endif %}
                                            {% if item.copy_of.details.packaging_instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.packaging_instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja pakowania</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji pakowania</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.copy_of.details.front_view %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.front_view.url }}"> <span class="glyphicon glyphicon-download"></span> Front </a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak frontów</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.copy_of.details.cnc_connections %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.cnc_connections.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki CNC </a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak CNC</button>
                                            {% endif %}
                                            {% if item.copy_of.details.cnc_connections_zip %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.cnc_connections_zip.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki CNC - zip</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak CNC - zip</button>
                                            {% endif %}
                                            {% if item.copy_of.details.horizontals_pdf and batch.manufactor and batch.manufactor.id == 25 %}
                                                <a type="button" class="btn btn-info" href="{{ item.copy_of.details.horizontals_pdf.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki horizontali</a>
                                            {% endif %}
                                            <br/>
                                        </td>
                                        <td>
                                            {% if item.details.instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji</button>
                                            {% endif %}
                                            {% if item.details.packaging_instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.packaging_instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja pakowania</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji pakowania</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.details.front_view %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.front_view.url }}"> <span class="glyphicon glyphicon-download"></span> Front </a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak frontów</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.details.cnc_connections_zip %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.cnc_connections_zip.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki CNC - zip</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak CNC - zip</button>
                                            {% endif %}
                                            {% if item.details.horizontals_pdf and batch.manufactor and batch.manufactor.id == 25 %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.horizontals_pdf.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki horizontali</a>
                                            {% endif %}
                                            <br/>
                                        </td>
                                    {% else %}
                                        <td>
                                            {% if item.details.instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji</button>
                                            {% endif %}
                                            {% if item.details.packaging_instruction %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.packaging_instruction.url }}"> <span class="glyphicon glyphicon-download"></span> Instrukcja pakowania</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak instrukcji pakowania</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.details.front_view %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.front_view.url }}"> <span class="glyphicon glyphicon-download"></span> Front </a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak frontów</button>
                                            {% endif %}
                                            <br/>
                                            {% if item.details.cnc_connections_zip %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.cnc_connections_zip.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki CNC - zip</a>
                                            {% else %}
                                                <button type="button" class="btn btn-default " disabled="disabled">Brak CNC - zip</button>
                                            {% endif %}
                                            {% if item.details.horizontals_pdf and batch.manufactor and batch.manufactor.id == 25 %}
                                                <a type="button" class="btn btn-info" href="{{ item.details.horizontals_pdf.url }}"> <span class="glyphicon glyphicon-download"></span> Pliki horizontali</a>
                                            {% endif %}
                                            <br/>
                                        </td>
                                    {% endif %}
                                    <td>
                                        {{ item.notes|default_if_none:'' }}
                                    </td>
                                    <td>
                                        {% if item.was_picked_up %}
                                            <button type="button" class="btn btn-success" disabled="disabled">Wysłano do klienta</button>
                                        {% elif item.is_waiting_for_quality_control %}
                                            <button type="button" class="btn btn-info" disabled="disabled">Czeka na Kontrolę Jakości</button>
                                        {% elif item.is_waiting_be_picked_up %}
                                            <button type="button" class="btn btn-info" disabled="disabled">Czeka na wysłanie</button>
                                        {% else %}
                                            {% if item.manufactor.owner == request.user %}
                                                <form>
                                                    <input type="hidden" value="{{ item.id }}" name="input_product_id" class="input_product_id"/>
                                                    <button type="button" class="button-ready-to-pickup btn btn-danger">
                                                        Gotowe
                                                    </button>
                                                </form>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% endif %}
            {% endfor %}
        </div>
    </div>

    <div class="modal fade" id="notesModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                        <span class="sr-only">Close</span>
                    </button>
                    <h4 class="modal-title">Add notes</h4>
                </div>
                <div class="modal-body">
                    <p><textarea name="notes" id="" cols="60" rows="10" placeholder="Additional notes"></textarea></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save changes</button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}
