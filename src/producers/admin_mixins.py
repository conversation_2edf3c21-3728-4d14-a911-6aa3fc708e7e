from django.conf import settings
from django.contrib import (
    admin,
    messages,
)
from django.db.models import Prefetch
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.urls import (
    re_path,
    reverse,
)

from orders.enums import OrderType
from orders.models import OrderItem
from producers.choices import BatchType
from producers.constants import LAST_INVALID_PRODUCT_ID
from producers.models import Product
from producers.models_split.file_history_entry import FileHistoryEntry
from production_margins.enums import STANDARD_ELEMENTS_ORDER_TYPES
from production_margins.models import ElementsOrder


class ProductDetailsFilesChangelogMixin:
    fieldsets = (
        (
            'Changelog',
            {'fields': ('render_files_changelog',)},
        ),
    )
    readonly_fields = ('render_files_changelog',)

    @staticmethod
    @admin.display(description='Files Changelog')
    def render_files_changelog(obj):
        url_name = ProductDetailsFilesChangelogMixin.get_reload_file_url_name(obj._meta)
        return render_to_string(
            template_name='admin/producers/files_changelog.html',
            context={
                'reload_url': reverse(f'admin:{url_name}'),
                'object_id': obj.pk,
                'fields': obj.get_deserialized_changelog(),
                'show_ids': not settings.IS_PRODUCTION,
            },
        )

    def get_urls(self, *args, **kwargs):
        urlpatterns = [
            re_path(
                'reload_files/$',
                self.admin_site.admin_view(self.reload_file),
                name=self.get_reload_file_url_name(self.model._meta),
            ),
        ]
        return urlpatterns + super().get_urls(*args, **kwargs)

    @staticmethod
    def get_reload_file_url_name(opts):
        object_type = 'watty' if 'watty' in opts.model_name.lower() else 'jetty'
        url_name = '{}_{}'.format(opts.app_label, object_type)
        return f'{url_name}_reload_file'

    def reload_file(self, request):
        # get file type from request's body
        filetype = request.POST.get('file_type')

        # if file type is missing, show an error
        if filetype is None:
            messages.add_message(
                request,
                messages.ERROR,
                'Error while refreshing file - no file type provided.',
            )
            return HttpResponse('No file type', status=400)

        # request file by file type
        try:
            instance = self.model.objects.get(id=request.POST['object_id'])
            handlers_mapping = {
                'nameplate': 'nameplate_txt',
                'cnc_connections_zip': 'connections_zip',
            }
            handler_name = handlers_mapping.get(filetype, filetype)

            handler = getattr(instance, f'generate_{handler_name}')
            handler()
        except AttributeError:
            messages.add_message(
                request,
                messages.ERROR,
                'Error while refreshing file - wrong file type provided.',
            )
            return HttpResponse(f'Unknown file type {filetype}', status=400)
        # set message for user
        messages.add_message(
            request,
            messages.INFO,
            f'Refreshing file - {filetype}.',
        )
        # http 200 (js will reload page)
        return HttpResponse('Ok')


class ProductAdminQuerySetMixin:
    def get_queryset(self, request, exclude_complaint=True):
        qs = super().get_queryset(request)
        qs = qs.filter(id__gt=LAST_INVALID_PRODUCT_ID)
        qs = qs.select_related(
            'manufactor',
            'order',
            'batch',
            'batch__manufactor',
            'order_item',
            'order__owner',
            'product_details_jetty',
            'product_details_watty',
        ).prefetch_related(
            'order_item__order_item',
            'reproduction_complaints',
            Prefetch(
                'batch__elementsorder_set',
                queryset=ElementsOrder.objects.filter(
                    order_type__in=STANDARD_ELEMENTS_ORDER_TYPES,
                ),
                to_attr='elementsorders',
            ),
            Prefetch(
                'product_details_jetty__file_history_entries',
                queryset=FileHistoryEntry.objects.all().order_by('pk'),
                to_attr='jetty_file_history_entries',
            ),
            Prefetch(
                'product_details_watty__file_history_entries',
                queryset=FileHistoryEntry.objects.all().order_by('pk'),
                to_attr='watty_file_history_entries',
            ),
        )
        if exclude_complaint:
            qs = qs.exclude(order__order_type=OrderType.COMPLAINT)
        return qs


class ProductBatchAdminQuerySetMixin:
    def get_queryset(self, request, exclude_complaint=True):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('manufactor')
        queryset = queryset.prefetch_related(
            Prefetch(
                'elementsorder_set',
                queryset=ElementsOrder.objects.all(),
                to_attr='elementsorders',
            ),
            'batch_details_jetty',
            'batch_details_watty',
            Prefetch(
                'batch_details_jetty__file_history_entries',
                queryset=FileHistoryEntry.objects.all().order_by('pk'),
                to_attr='jetty_file_history_entries',
            ),
            Prefetch(
                'batch_details_watty__file_history_entries',
                queryset=FileHistoryEntry.objects.all().order_by('pk'),
                to_attr='watty_file_history_entries',
            ),
            Prefetch(
                'batch_items',
                queryset=Product.objects.defer(
                    'order_item_serialized',
                    'product_details_jetty__cached_serialization',
                    'product_details_watty__cached_serialization',
                )
                .select_related(
                    'manufactor',
                    'order',
                    'order__owner',
                    'product_details_jetty',
                    'product_details_watty',
                )
                .prefetch_related(
                    Prefetch(
                        'order_item',
                        OrderItem.objects.prefetch_related(
                            'order_item',
                        ),
                    ),
                ),
            ),
        )
        if exclude_complaint:
            queryset = queryset.exclude(batch_type=BatchType.COMPLAINTS)
        return queryset.distinct()
