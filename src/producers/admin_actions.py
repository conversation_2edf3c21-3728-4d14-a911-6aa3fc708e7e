import copy
import io
import json
import logging
import typing

from collections import defaultdict
from datetime import (
    date,
    datetime,
)
from io import BytesIO
from zipfile import ZipFile

from django import forms
from django.contrib import (
    admin,
    messages,
)
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.http import (
    FileResponse,
    HttpResponse,
    HttpResponseNotFound,
    HttpResponseRedirect,
)
from django.shortcuts import (
    redirect,
    render,
)
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from googleapiclient.http import HttpRequest
from openpyxl.utils import get_column_letter

from complaints.exceptions import CannotAbortComplaintException
from complaints.models import ProductionComplaint
from complaints.serializers import ComplaintExportSerializer
from complaints.utils import generate_production_order_as_html
from custom.admin_action import admin_action_with_form
from custom.enums import Furniture
from custom.models.models import DocumentRequest
from custom.utils.decorators import (
    CSVObject,
    csv_export,
    with_document_async_message,
)
from gallery.serializers import (
    JettyImportExportSerializer,
    WattyImportExportSerializer,
)
from producers.batch_files_operations import generate_zip_with_all_batch_files
from producers.choices import (
    BatchStatus,
    BatchType,
    ProductPriority,
    ProductStatus,
)
from producers.constants import (
    CODENAMES_EXCLUDED_SERVICES,
    ONLY_AMIR_CODENAMES,
)
from producers.cost_calculations import (
    CachedCustomPricing,
    get_management_cost,
    get_manufacturer_code,
    is_managed_by_us,
    is_service_cost,
    is_sofa_material,
    is_sofa_part,
)
from producers.enums import Manufacturers
from producers.errors import (
    CannotBatchAbortedProducts,
    CannotChangeToPostponePriorityException,
)
from producers.forms import (
    ChangeManufactorForProductForm,
    ChangePriorityWithHistoryForm,
    ChangeStatusWithHistoryForm,
)
from producers.internal_api.events import RequestProposedDeliveryReportEvent
from producers.models import (
    Manufactor,
    Product,
    ProductBatch,
)
from producers.models_split.product_details import ProductDetailsJetty
from producers.product_updater import ProductStatusUpdaterWithLogisticSplit
from producers.production_files.summary_usage import (
    generate_usage_vertical_rows,
    get_batch_filename,
    get_usage_for_batches_raw,
)
from producers.production_system_utils.client import ProductionSystemClient
from producers.reports.batch_spacers_summary import report_spacers_in_batches
from producers.reports.speedtrans_order import (
    get_speedtrans_order_type01_doors_xlsx,
    get_speedtrans_order_type01_drawers_xlsx,
    get_speedtrans_order_type02_xlsx,
)
from producers.services import (
    ProductBatchService,
    send_auto_batching_mail,
)
from producers.services.match_usage import MatchBatchUsage
from producers.services.send_auto_batching_mail import validate_s93_order
from producers.tasks import (
    calculate_and_send_ivy_product_usage,
    create_carton_files_for_s93_task,
    generate_and_send_t13_door_handling_order_batch_report,
    generate_and_send_t25_legs_reports,
    generate_instruction_task,
    generate_instructions_for_selected_batches_task,
    generate_order_summary_xls,
    generate_order_summary_xls_for_products,
    generate_order_summary_xls_for_products_by_batches,
    generate_order_summary_xls_manufactor_fault,
    generate_production_report,
    generate_products_timeline_with_labels,
    generate_t13_door_handling_order_products_report,
    send_batches_to_meblepl_by_ftp,
    send_batching_mail,
    send_delivered_packages_report,
    send_delivered_products_report,
    send_last_quarter_france_orders_report,
    trigger_logistic_split_on_ready_orders,
)
from producers.utils import (
    add_file_to_zip,
    generate_production_timeline_with_labels,
    round_down_if_item,
    unzip_file_to_another_zip,
)
from production_margins.enums import (
    STANDARD_ELEMENTS_ORDER_TYPES,
    ElementsOrderType,
)
from production_margins.models import (
    ElementManagedInfo,
    ElementsOrder,
)

if typing.TYPE_CHECKING:
    from django.contrib.admin import ModelAdmin


logger = logging.getLogger('producers')


@admin.action(description='Change status')
def change_status_with_history(self, request, queryset_products):
    form = None
    if 'apply' in request.POST:
        form = ChangeStatusWithHistoryForm(request.POST, products=queryset_products)
        if form.is_valid():
            target_status = form.cleaned_data['status_to_change']
            if target_status in ProductStatus.with_logistic_split_required():
                updater_with_logistic_split = ProductStatusUpdaterWithLogisticSplit(
                    queryset_products, target_status
                )
                products_status_changed_count = (
                    updater_with_logistic_split.update_status_and_split_logistic_orders(
                        request.user
                    )
                )
            else:
                products_status_changed_count = 0
                for product in queryset_products:
                    try:
                        product.status_updater.change_status(
                            target_status, request.user
                        )
                    except CannotAbortComplaintException as exc:
                        messages.add_message(
                            request, level=messages.WARNING, message=exc
                        )
                        continue
                    products_status_changed_count += 1

            if products_status_changed_count:
                self.message_user(
                    request,
                    f'Changed status for {products_status_changed_count} '
                    f'from {queryset_products.count()} items',
                    level=messages.INFO,
                )
            return redirect(request.get_full_path())
        else:
            self.message_user(
                request, 'Not changed any items status', level=messages.WARNING
            )

    if not form:
        form = ChangeStatusWithHistoryForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)},
            products=queryset_products,
        )

    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/producers/product/change_status_action.html',
        {
            'products': queryset_products,
            'status_form': form,
            'opts': opts,
            'app_label': app_label,
        },
    )


@admin.action(description='Export production files as zip')
def export_files_as_zip(self, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for queryset_chunk in Paginator(queryset, 100):
            for product in queryset_chunk.object_list:
                if product.product_type == 'jetty':
                    details = product.details
                    if details.instruction:
                        zipfile.writestr(
                            'T1_{}/{}'.format(
                                product.id,
                                details.instruction.name.split('/')[-1],
                            ),
                            details.instruction.read(),
                        )
                    if details.cnc_connections_zip:
                        zipfile.writestr(
                            'T1_{}/{}'.format(
                                product.id,
                                details.cnc_connections_zip.name.split('/')[-1],
                            ),
                            details.cnc_connections_zip.read(),
                        )
                    if details.front_view:
                        zipfile.writestr(
                            'T1_{}/{}'.format(
                                product.id,
                                details.front_view.name.split('/')[-1],
                            ),
                            details.front_view.read(),
                        )
                    if details.front_view_zip:
                        zipfile.writestr(
                            'T1_{}/{}'.format(
                                product.id,
                                details.front_view_zip.name.split('/')[-1],
                            ),
                            details.front_view_zip.read(),
                        )
                    if details.packaging_instruction:
                        zipfile.writestr(
                            'T1_{}/{}'.format(
                                product.id,
                                details.packaging_instruction.name.split('/')[-1],
                            ),
                            details.packaging_instruction.read(),
                        )

    stream.seek(0)
    return FileResponse(stream, as_attachment=True, filename='production_files.zip')


class BatchWithManufactorForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    manufactor = forms.ModelChoiceField(Manufactor.objects)


class GalaBatchingForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)


@admin.action(description=_('Batch with manufactor'))
def batch_with_manufactor(self, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = BatchWithManufactorForm(request.POST)
        if form.is_valid():
            manufactor = form.cleaned_data['manufactor']
            product_batch_service = ProductBatchService()
            try:
                product_batch_service.create(
                    queryset,
                    manufactor,
                    generate_files=True,
                )
            except CannotBatchAbortedProducts as exc:
                self.message_user(
                    request,
                    message=str(exc),
                    level=messages.WARNING,
                )
                return
            self.message_user(
                request,
                'Selected products batched with a manufactor {}.'.format(
                    manufactor,
                ),
            )
            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = BatchWithManufactorForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/product_batch.html',
        {
            'queryset': queryset,
            'form': form,
            'opts': opts,
            'app_label': app_label,
            'action': 'batch_with_manufactor',
        },
    )


@admin.action(description='Create zip for all items from batch')
def create_zip_for_all_items_from_batch(modeladmin, request, queryset):
    batch = queryset.first()
    report_file = generate_zip_with_all_batch_files(
        False, batch, filename=f'T1_B{batch.id}_all.zip'
    )
    return report_file.get_as_http_response() if report_file else HttpResponseNotFound()


@admin.action(description='Summary usage for all items from batch')
@csv_export
def sum_usage_for_all_items_from_batch(
    modeladmin: 'ModelAdmin',
    request: HttpRequest,
    queryset: QuerySet['Product'],
) -> CSVObject:
    queryset = queryset.order_by('id')
    data = get_usage_for_batches_raw(queryset)
    rows = data['content']

    return CSVObject(
        file_name=get_batch_filename(queryset),
        header_row=None,
        rows=rows,
        add_timestamp=False,
    )


@admin.action(description='Summary usage for all items from batch vertical')
@csv_export
def sum_usage_for_all_items_from_batch_vertical(
    modeladmin: 'ModelAdmin',
    request: HttpRequest,
    queryset: QuerySet['Product'],
) -> CSVObject:
    queryset = queryset.order_by('id')
    rows = generate_usage_vertical_rows(queryset)
    return CSVObject(
        file_name=get_batch_filename(queryset),
        header_row=None,
        rows=rows,
        add_timestamp=False,
    )


@admin.action(description='Summary usage for all items from batch - Asynchronous')
@with_document_async_message
def sum_usage_for_all_items_from_batch_async(
    modeladmin: 'ModelAdmin',
    document_request: DocumentRequest,
    request: HttpRequest,
    queryset: QuerySet['Product'],
) -> None:
    from producers.tasks import get_usage_for_batches_raw_task

    get_usage_for_batches_raw_task.delay(
        get_batch_filename(queryset),
        document_request.pk,
        list(queryset.values_list('id', flat=True)),
    )


@admin.action(description='Summary usage for all items from batch - Telmex')
@with_document_async_message
def sum_usage_for_all_items_from_batch_telmex_async(
    modeladmin: 'ModelAdmin',
    document_request: DocumentRequest,
    request: HttpRequest,
    queryset: QuerySet['Product'],
) -> None:
    from producers.tasks import get_usage_for_batches_raw_telmex_task

    get_usage_for_batches_raw_telmex_task.delay(
        get_batch_filename(queryset),
        document_request.pk,
        list(queryset.values_list('id', flat=True)),
    )


@admin.action(description='Export for production timeline for items in batch')
def export_for_production_timeline(modeladmin, request, queryset):
    response = ''

    for nr, batch in enumerate(queryset.order_by('id'), start=1):
        for item in batch.batch_items.all().order_by('order__paid_at'):
            area = str(item.get_m2()).replace('.', ',')
            paid_at = (
                item.order.paid_at.strftime('%Y-%m-%d') if item.order.paid_at else '-'
            )
            estimated_delivery_time = (
                item.order.estimated_delivery_time.strftime('%Y-%m-%d')
                if item.order.paid_at
                else '-'
            )
            response += f'''
            {nr};
            T1_{item.id};
            {item.order_id};
            {batch.id};
            {item.order.get_customer_as_string(with_html=False)};
            {item.order.country};
            {batch.material_description};
            {area};
            {paid_at};
            {estimated_delivery_time};;;;;;
            {batch.get_batch_type_display()}\n
            '''

    now = datetime.now().strftime('%Y-%m-%d')
    response = HttpResponse(content_type='text/csv', content=response)
    response[
        'Content-Disposition'
    ] = f'attachment; filename=export_for_production_timeline-{now}.csv'
    return response


@admin.action(description='Export products for production timeline with labels')
@with_document_async_message
def export_for_production_timeline_with_labels(
    modeladmin: 'ModelAdmin',
    document_request: DocumentRequest,
    request: HttpRequest,
    queryset: QuerySet['Product'],
):
    export_source = request.path.split('/')[-2]  # productbatch or productbatchcomplaint
    generate_production_timeline_with_labels(
        document_request.pk, queryset, export_source=export_source
    )


@admin.action(description='T13 door handling report')
def generate_t13_door_handling_order_batches_report_action(
    modeladmin: 'ModelAdmin',
    request: HttpRequest,
    queryset: QuerySet['Product'],
):
    email = request.user.email
    batches_ids = list(queryset.values_list('id', flat=True))
    generate_and_send_t13_door_handling_order_batch_report(batches_ids, email)


@admin.action(description='Generate T25 legs reports')
def generate_t25_legs_report_products(modeladmin, request, queryset):
    unique_manufacturers_count = queryset.values('manufactor').distinct().count()
    if unique_manufacturers_count > 1:
        modeladmin.message_user(
            request,
            'Selected products have different manufacturers',
            level=messages.ERROR,
        )
        return
    email = request.user.email
    product_ids = list(queryset.values_list('id', flat=True))
    generate_and_send_t25_legs_reports.delay(product_ids, email)
    modeladmin.message_user(
        request,
        'T25 legs report generation started, email will be sent to {}'.format(email),
        level=messages.INFO,
    )


@admin.action(description='Generate T25 legs reports')
def generate_t25_legs_report_batches(modeladmin, request, queryset):
    unique_manufacturers_count = (
        queryset.values('batch_items__manufactor').distinct().count()
    )
    if unique_manufacturers_count > 1:
        modeladmin.message_user(
            request,
            'Selected batches have different manufacturers',
            level=messages.ERROR,
        )
        return
    email = request.user.email
    product_ids = [
        item_id
        for batch in queryset
        for item_id in batch.batch_items.values_list('id', flat=True)
    ]
    generate_and_send_t25_legs_reports.delay(product_ids, email)
    modeladmin.message_user(
        request,
        'T25 legs report generation started, email will be sent to {}'.format(email),
        level=messages.INFO,
    )


@admin.action(description='Upload batches to MeblePl by FTP')
def upload_batches_to_meblepl(modeladmin, request, queryset):
    queryset = queryset.filter(manufactor__name=Manufactor.MEBLE_PL)
    if not queryset.exists():
        modeladmin.message_user(
            request, 'No batches with manufactor MeblePl', level=messages.WARNING
        )
        return
    queryset = queryset.filter(uploaded_to_meblepl_at__isnull=True)
    if not queryset.exists():
        modeladmin.message_user(
            request, 'All batches already uploaded to MeblePl', level=messages.WARNING
        )
        return
    send_batches_to_meblepl_by_ftp.delay(list(queryset.values_list('id', flat=True)))


@admin.action(description='T13 door handling report')
def generate_t13_door_handling_order_products_report_action(
    modeladmin, requset, queryset
):
    email = requset.user.email
    products_ids = list(queryset.values_list('id', flat=True))
    generate_t13_door_handling_order_products_report.delay(products_ids, email)


@with_document_async_message
@admin.action(description='Export products for production timeline with labels quality')
def export_products_for_production_timeline_with_labels_quality(
    modeladmin, document_request, request, queryset
):
    product_ids = list(queryset.values_list('id', flat=True))
    generate_products_timeline_with_labels.delay(
        document_request.pk, product_ids, quality_panel=True
    )


@admin.action(description='Export for production timeline for items in product')
def export_for_production_timeline_product(modeladmin, request, queryset):
    response = ''
    id = 1
    queryset = queryset.order_by('id')
    for queryset_chunk in Paginator(queryset, 100):
        for item in queryset_chunk.object_list:
            response += '{};T1_{};{};{};{};{};{};{};{};{};;;;;;{}\n'.format(
                id,
                item.id,
                item.order_id,
                item.batch.id if item.batch else 'No batch',
                item.order.get_customer_as_string(with_html=False),
                item.order.country,
                item.batch.material_description if item.batch else 'No batch',
                str(item.get_m2()).replace('.', ','),
                item.order.paid_at.strftime('%Y-%m-%d') if item.order.paid_at else '-',
                item.order.estimated_delivery_time.strftime('%Y-%m-%d')
                if item.order.estimated_delivery_time
                else '-',
                item.batch.get_batch_type_display() if item.batch else 'No batch',
            )
            id += 1

    response = HttpResponse(content_type='text/csv', content=response)
    response[
        'Content-Disposition'
    ] = 'attachment; filename=export_for_production_timeline-{}.csv'.format(
        datetime.now().strftime('%Y-%m-%d')
    )
    return response


@admin.action(description='Recalculate front views')
def front_views_package_recalculate(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            product.details.generate_front_view()
    messages.info(
        request,
        'Frontview recalculation for {} items added to queue'.format(
            queryset.count(),
        ),
    )


@admin.action(description='Recalculate front views, connections and packaging')
def front_connection_and_packaging_recalculate(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            # Generate front view, packaging instruction,
            # connections zip for jetty and watty
            product.details.generate_packaging_instruction()
            product.details.generate_front_view()
            product.details.generate_connections_zip()
            if product.order_item.content_type.model == Furniture.jetty.value:
                # Connections dxf and pdf are only for jetty
                product.details.generate_connections_dxf()
                product.details.generate_horizontals_pdf()
            if product.manufactor_id == Manufacturers.TELMEX:
                # Production drawings (for blende) are only for Telmex
                product.details.generate_production_drawings()
    messages.info(
        request,
        'Front views, connections and packaging recalculation'
        + f' for {queryset.count()} items added to queue.',
    )


@admin.action(description='Recalculate packaging pdfs')
def packaging_instruction_package_recalculate(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            if product.order_item.content_type.model == Furniture.jetty.value:
                pdj, _ = ProductDetailsJetty.objects.get_or_create(product=product)
                pdj.generate_packaging_instruction()
    messages.info(
        request,
        'Packaging recalculation for {} items added to queue.'.format(
            len(queryset),
        ),
    )


@admin.action(description='Recalculate connection and connection zip')
def create_connections(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            pdj, c = ProductDetailsJetty.objects.get_or_create(product=product)
            pdj.generate_connections_dxf()
            pdj.generate_horizontals_pdf()
            pdj.generate_connections_zip()
    messages.info(
        request,
        'Connections recalculation for {} items added to queue.'.format(
            len(queryset),
        ),
    )


@admin.action(description='Download ivy product usage')
@with_document_async_message
def ivy_product_usage(modeladmin, document_request, request, queryset):
    product_ids = list(
        queryset.filter(
            cached_product_type__in=Product.PS_SERIALIZED,
        ).values_list('id', flat=True)
    )
    calculate_and_send_ivy_product_usage.delay(document_request.id, product_ids)


@admin.action(description='==> Refresh serialization from production system <==')
def recalc_serialization_using_ps(modeladmin, request, queryset):
    queryset = queryset.filter(cached_product_type__in=Product.PS_SERIALIZED)
    success = 0
    failed = 0
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            try:
                product.serialization_updater.update_product_serialization()
            except Exception as e:
                logger.critical(
                    f'Product serialization error for product: {product.id}! {e}',
                )
                messages.error(
                    request,
                    f'Product serialization error for product: {product.id}!',
                )
                failed += 1
            else:
                success += 1
    if success:
        messages.success(
            request,
            f'Recalculated serialization for {success} products',
        )
    if failed:
        messages.error(
            request,
            f'Errors when recalculating serialization for {failed} products',
        )


@admin.action(description='Refresh serialization for all items in batch')
def recalculate_batch_products_serialization_using_ps(modeladmin, request, queryset):
    products = Product.objects.filter(
        pk__in=queryset.values_list('batch_items', flat=True)
    )
    recalc_serialization_using_ps(modeladmin, request, products)


@admin.action(description='Recalculate all files')
def recalculate_all_batch_files(modeladmin, request, queryset):
    for batch in queryset:
        if batch.manufactor and batch.product_type == Furniture.jetty.name:
            products_without_instructions_id = batch.batch_items.filter(
                product_details_jetty__instruction=''
            ).values_list('id', flat=True)

            if products_without_instructions_id:
                messages.add_message(
                    request,
                    messages.WARNING,
                    f'Products with id={list(products_without_instructions_id)} '
                    'have no instructions. '
                    f'So instruction related files can not be properly generated '
                    f'for batch with id={batch.id}. ',
                )

        batch.generate_all_files(request)


@admin.action(description='Recalculate nesting files')
def recalculate_batch_nesting_files(modeladmin, request, queryset):
    for page in Paginator(queryset, 100):
        for batch in page.object_list:
            batch.generate_nesting_files()


@admin.action(description='Clear CALCULATION_IN_PROGRESS status')
def check_for_all_files_manually(modeladmin, request, queryset):
    for page in Paginator(queryset, 100):
        for batch in page.object_list:
            batch.actions_needed_flow.process_manual_check_for_files_invoked()


@admin.action(description='Get speedtrans T2 handles order for selected batches')
def get_speedtrans_order_type02(modeladmin, request, queryset):
    file_name, speedtrans_xls = get_speedtrans_order_type02_xlsx(queryset)
    response = HttpResponse(
        ContentFile(speedtrans_xls), content_type='application/ms-excel'
    )
    response['Content-Disposition'] = 'attachment;filename={}.xlsx'.format(file_name)

    return response


@admin.action(description='Get speedtrans T1 drawer handles order for selected batches')
def get_speedtrans_order_type01_drawers(modeladmin, request, queryset):
    file_name, speedtrans_xls = get_speedtrans_order_type01_drawers_xlsx(queryset)
    response = HttpResponse(
        ContentFile(speedtrans_xls), content_type='application/ms-excel'
    )
    response['Content-Disposition'] = 'attachment;filename={}.xlsx'.format(file_name)
    return response


@admin.action(description='Get speedtrans T1 door handles order for selected batches')
def get_speedtrans_order_type01_doors(modeladmin, request, queryset):
    return get_speedtrans_order_type01_doors_xlsx(queryset)


class GenerateStatementSheetMixin:
    def __init__(
        self,
        sheet,
        queryset,
        guilty_manufactor_id=None,
        usage=None,
        manufactor=None,
    ):
        self.manufactor = manufactor or self.get_manufactor(
            guilty_manufactor_id, queryset
        )
        usage = copy.deepcopy(usage) or get_usage_for_batches_raw(
            queryset, guilty_manufactor_id
        )
        self.elements_units = usage['units']
        self.excel_data = self.filter_usage_content(usage)
        self.sheet = sheet
        self.update_excel_data_with_manufacturer_code_column()
        self.start_static_col = len(self.excel_data[0]) + 1
        self.total_costs_dict = defaultdict(int)
        self.total_costs_dict_per_column = defaultdict(lambda: defaultdict(int))
        self.cached_custom_pricing = CachedCustomPricing(self.manufactor, date.today())

    def filter_usage_content(self, usage):
        if self.manufactor.name == Manufactor.AMIR:
            return usage['content']
        return [row for row in usage['content'] if row[0] not in ONLY_AMIR_CODENAMES]

    @staticmethod
    def get_manufactor(manufactor_id, queryset):
        """
        If manufactor_id is not provided get manufactor from batch queryset,
        all batches here should be for the same producer
        """
        if manufactor_id:
            return Manufactor.objects.get(id=manufactor_id)
        return queryset[0].manufactor

    def update_excel_data_with_manufacturer_code_column(self):
        for row_num, row_data in enumerate(self.excel_data):
            if row_num == 0:
                row_data.insert(1, 'MANUFACTURER_CODE')
            else:
                codename = row_data[0]
                manufacturer_code = get_manufacturer_code(
                    date.today(), self.manufactor, codename
                )
                row_data.insert(1, manufacturer_code)

    # Temporary function to complete admin total_cost
    def get_total_dict(self):
        elements_data = self.excel_data[self.elements_start_row :]  # noqa: E203
        for row_num, element_row in enumerate(elements_data, self.elements_start_row):
            total_quantity = float(element_row[-1].replace(',', '.'))
            if total_quantity == float(0):
                continue

            current_codename = element_row[0]
            managed_by_us = is_managed_by_us(self.manufactor, current_codename)
            self.get_element_costs_and_update_totals(
                current_codename, element_row, total_quantity, managed_by_us
            )
        return self.get_result_dict()

    def get_element_costs_and_update_totals(
        self,
        current_codename,
        element_usage_row,
        total_quantity,
        managed_by_us,
    ):
        loss_factor = float(
            self.cached_custom_pricing.get_loss_factor(current_codename)
        )
        current_price = float(self.cached_custom_pricing.get_price(current_codename))

        element_costs = self.get_elements_costs_based_on_quantity(
            current_codename,
            total_quantity,
            loss_factor,
            current_price,
            managed_by_us,
            self.elements_units.get(current_codename),
        )

        self.update_total_costs_per_column(
            current_codename,
            element_usage_row,
            loss_factor,
            current_price,
            managed_by_us,
        )

        self.update_costs(
            self.total_costs_dict, current_codename, element_costs, managed_by_us
        )
        return element_costs

    def update_total_costs_per_column(
        self,
        current_codename,
        element_usage_row,
        loss_factor,
        current_price,
        managed_by_us,
    ):
        column_name_idx = {
            # self.excel_data[0] = [index, unit, {batch/product}, ..., total]
            # skip first, second and last element
            name: idx
            for idx, name in enumerate(self.excel_data[0][3:-1], 3)
        }
        for column_name, column_idx in column_name_idx.items():
            quantity_per_column = float(element_usage_row[column_idx].replace(',', '.'))
            batch_element_costs = self.get_elements_costs_based_on_quantity(
                current_codename,
                quantity_per_column,
                loss_factor,
                current_price,
                managed_by_us,
                self.elements_units.get(current_codename),
            )
            self.update_costs(
                self.total_costs_dict_per_column[column_name],
                current_codename,
                batch_element_costs,
                managed_by_us,
            )

    def get_result_dict(self):
        self.update_material_management_cost()
        result_dict = dict(self.total_costs_dict.copy())
        result_dict.update({'batches': self.total_costs_dict_per_column})
        return result_dict

    def update_material_management_cost(self):
        management_cost = get_management_cost(self.manufactor)
        for batch_costs in self.total_costs_dict_per_column.values():
            self.update_material_management_cost_in_dict(batch_costs, management_cost)
        self.update_material_management_cost_in_dict(
            self.total_costs_dict, management_cost
        )

    def write_data_to_sheet_and_get_total_costs(self):
        """
        return: {
            material_management_cost: value,
            total_netto_price: v,
            total_brutto_price: v,
            cost_service: v,
            batches: {
                B_01: {
                    material_management_cost: value,
                    total_netto_price: v,
                    total_brutto_price: v,
                    cost_service: v
                }
                ...
            }
        }

        """
        self.rewrite_data_to_excel(self.excel_data)
        elements_data = self.excel_data[self.elements_start_row :]  # noqa: E203
        codenames_not_managed_by_us = self.get_codenames_not_managed_by_us()
        for row_num, element_row in enumerate(elements_data, self.elements_start_row):
            total_quantity = float(element_row[-1].replace(',', '.'))
            if total_quantity == float(0):
                continue

            current_codename = element_row[0]
            managed_by_us = current_codename not in codenames_not_managed_by_us

            element_costs = self.get_element_costs_and_update_totals(
                current_codename,
                element_row,
                total_quantity,
                managed_by_us,
            )
            self.write_row_with_element_data(row_num, element_costs, managed_by_us)

        return self.get_result_dict()

    def get_codenames_not_managed_by_us(self) -> set:
        codenames_to_check = [row_data[0] for row_data in self.excel_data]
        not_manage_by_us = list(
            ElementManagedInfo.objects.valid_for_today()
            .filter(
                pricing_factor_item__codename__in=codenames_to_check,
                managed_by_us=False,
                manufactor=self.manufactor,
            )
            .values_list('pricing_factor_item__codename', flat=True)
        )
        manufactor_shortname = Manufactor.SHORT_NAME_IN_CODENAMES.get(
            self.manufactor.name
        )
        if not manufactor_shortname:
            return set(not_manage_by_us)
        for codename in codenames_to_check:
            if is_service_cost(codename) and manufactor_shortname in codename:
                not_manage_by_us.append(codename)
        return set(not_manage_by_us)

    def rewrite_data_to_excel(self, data_source):
        self.set_header_for_sheet()
        for row_num, row_data in enumerate(data_source):
            for col_num, col_data in enumerate(row_data):
                data_to_write = col_data if col_data != '0,0' else ''
                self.sheet.cell(
                    row=row_num + 1, column=col_num + 1, value=data_to_write
                )

        for idx, column in enumerate(self.sheet.columns):
            value = 25
            if idx == 0:
                value = 50
            self.sheet.column_dimensions[
                get_column_letter(column[0].column)
            ].width = value

    def update_costs(self, costs_dict, current_codename, costs, managed_by_us):
        # Additional calculation
        if current_codename in CODENAMES_EXCLUDED_SERVICES:
            return
        if is_sofa_part(current_codename):
            costs_dict['sofa_parts'] += costs['netto_price']
        elif is_sofa_material(current_codename):
            costs_dict['sofa_materials'] += costs['netto_price']
        if is_service_cost(current_codename):
            costs_dict['cost_service'] += costs['netto_price']
        elif not managed_by_us:
            costs_dict['total_netto_price'] += costs['netto_price']
            costs_dict['total_brutto_price'] += costs['brutto_price']
        else:
            costs_dict['tylko_netto_price'] += costs['netto_price']
            costs_dict['tylko_brutto_price'] += costs['brutto_price']

    @staticmethod
    def get_elements_costs_based_on_quantity(
        code_name, total_quantity, loss_factor, current_price, managed_by_us, unit=None
    ):
        netto_price = total_quantity * current_price
        total_brutto = total_quantity * (1 + loss_factor)
        total_brutto = round_down_if_item(total_brutto, code_name, unit)
        total_element_prices = dict(
            total_brutto='' if total_brutto == 0 else total_brutto,
            price='' if managed_by_us else current_price,
            netto_price=netto_price,
            brutto_price=(total_brutto - total_quantity) * current_price,
        )
        return total_element_prices

    def write_row_with_element_data(self, row_num, element_costs, managed_by_us):
        # column header - Total brutto
        self.sheet.cell(
            row=row_num + 1,
            column=self.start_static_col,
            value=get_value_comma_replaced(element_costs['total_brutto']),
        )
        # header - Price
        self.sheet.cell(
            row=row_num + 1,
            column=self.start_static_col + 1,
            value=get_value_comma_replaced(element_costs['price']),
        )
        # Tylko/Manufacture Netto
        col_netto = self.start_static_col + (2 if managed_by_us else 4)
        self.sheet.cell(
            row=row_num + 1,
            column=col_netto,
            value=get_value_comma_replaced(element_costs['netto_price']),
        )
        # Tylko/Manufacture Brutto
        col_brutto = self.start_static_col + (3 if managed_by_us else 5)
        self.sheet.cell(
            row=row_num + 1,
            column=col_brutto,
            value=get_value_comma_replaced(element_costs['brutto_price']),
        )

    @staticmethod
    def update_material_management_cost_in_dict(costs_dict, management_cost):
        costs_dict['material_management_cost'] = (
            costs_dict['total_netto_price'] + costs_dict['total_brutto_price']
        ) * management_cost

    def set_header_for_sheet(self):
        managed_info_titles = [
            'TOTAL BRUTTO',
            'PRICE',
            'TYLKO NETTO',
            'TYLKO BRUTTO',
            'MANUFACTURER NETTO',
            'MANUFACTURER BRUTTO',
        ]
        for col_num, col_title in enumerate(managed_info_titles):
            actual_title_col = self.start_static_col + col_num

            self.sheet.cell(row=1, column=actual_title_col, value=col_title)

        for idx, column in enumerate(self.sheet.columns):
            value = 25
            if idx == 0:
                value = 50
            self.sheet.column_dimensions[
                get_column_letter(column[0].column)
            ].width = value


class GenerateManufacturerStatementSheetMixin:
    def write_data_to_sheet_and_get_total_costs(self):
        """
        Only elements not managed by us
        return: {
            material_management_cost: value,
            total_netto_price: v,
            total_brutto_price: v,
            cost_service: v,
            batches: {
                B_01: {
                    material_management_cost: value,
                    total_netto_price: v,
                    total_brutto_price: v,
                    cost_service: v
                }
                ...
            }
        }

        """
        elements_data = self.get_elements_not_managed_by_us_with_headers()

        self.rewrite_data_to_excel(elements_data)
        for row_number, element_row in enumerate(elements_data[1:], start=1):
            total_quantity = float(element_row[-1].replace(',', '.'))

            current_codename = element_row[0]

            element_costs = self.get_element_costs_and_update_totals(
                current_codename, element_row, total_quantity, False
            )
            self.write_row_with_element_data(row_number, element_costs, False)

        return self.get_result_dict()

    def get_elements_not_managed_by_us_with_headers(self) -> list[list]:
        """
        Returns [
            ['index', 'MANUFACTURER_CODE', 'Unit', Products/Batch, ..., 'TOTAL'],
            ['material_codename', 'manufacturer_code', 'value', 'value'],
            ...
        ]

        """
        not_managed_by_us = self.get_codenames_not_managed_by_us()
        return [
            row_data
            for row_number, row_data in enumerate(self.excel_data)
            if row_data[0] in not_managed_by_us or row_number == 0
        ]


class GenerateStatementBatchSheet(GenerateStatementSheetMixin):
    elements_start_row = 4


class GenerateManufacturerStatementBatchSheet(
    GenerateManufacturerStatementSheetMixin, GenerateStatementBatchSheet
):
    pass


class GenerateStatementProductSheet(GenerateStatementSheetMixin):
    elements_start_row = 4

    def update_total_costs_per_column(
        self,
        current_codename,
        element_usage_row,
        loss_factor,
        current_price,
        managed_by_us,
    ):
        column_name_idx = {
            # self.excel_data[0] = [index, unit, {batch/product}, ..., total]
            # skip first, second and last element
            name: idx
            for idx, name in enumerate(self.excel_data[0][3:-1], 3)
        }
        for product_id, column_idx in column_name_idx.items():
            quantity_per_column = float(element_usage_row[column_idx].replace(',', '.'))
            batch_element_costs = self.get_elements_costs_based_on_quantity(
                current_codename,
                quantity_per_column,
                loss_factor,
                current_price,
                managed_by_us,
                self.elements_units.get(current_codename),
            )
            self.update_costs(
                self.total_costs_dict_per_column[product_id],
                current_codename,
                batch_element_costs,
                managed_by_us,
            )

    def get_result_dict(self):
        self.update_material_management_cost()
        result_dict = dict(self.total_costs_dict.copy())
        result_dict.update({'products': self.total_costs_dict_per_column})
        return result_dict

    def update_material_management_cost(self):
        management_cost = get_management_cost(self.manufactor)
        for product_cost in self.total_costs_dict_per_column.values():
            self.update_material_management_cost_in_dict(product_cost, management_cost)
        self.update_material_management_cost_in_dict(
            self.total_costs_dict, management_cost
        )


class GenerateManufacturerStatementProductSheet(
    GenerateManufacturerStatementSheetMixin, GenerateStatementProductSheet
):
    """
    Sheet Zestawienie Manufacturer
    """


def get_value_comma_replaced(value):
    value_replaced = str(value).replace('.', ',')
    return value_replaced if value_replaced != '0,0' else ''


@admin.action(description='Get order xlsx for selected batches')
def get_order_summary_xls(modeladmin, request, batches_queryset):
    batches_ids = [batch.pk for batch in batches_queryset]
    if ElementsOrder.objects.filter(
        batches__in=batches_ids, order_type__in=STANDARD_ELEMENTS_ORDER_TYPES
    ).exists():
        modeladmin.message_user(
            request,
            'Could not generate report. '
            'Elements order with the same batches already exists!',
            level=messages.ERROR,
        )
        return HttpResponseRedirect(request.get_full_path())

    generate_order_summary_xls.delay(batches_ids, request.user.email)
    modeladmin.message_user(
        request,
        'Email with report will be sent to {}.'.format(request.user.username),
    )


@admin.action(description='Get order xlsx for products')
def get_order_summary_xls_for_products_by_batches(
    modeladmin, request, products_queryset
):
    _get_order_xlsx_for_products(
        generate_report_function=generate_order_summary_xls_for_products_by_batches,
        products_queryset=products_queryset,
        modeladmin=modeladmin,
        request=request,
    )


@admin.action(description='Get order xlsx for products split by files')
def get_order_summary_xls_for_products(modeladmin, request, products_queryset):
    _get_order_xlsx_for_products(
        generate_report_function=generate_order_summary_xls_for_products,
        products_queryset=products_queryset,
        modeladmin=modeladmin,
        request=request,
    )


def _get_order_xlsx_for_products(
    generate_report_function, products_queryset, modeladmin, request
):
    products_without_batch = products_queryset.filter(batch__isnull=True)
    if products_without_batch.exists():
        products_without_batch_ids = list(
            products_without_batch.values_list('id', flat=True)
        )
        modeladmin.message_user(
            request,
            f'Choose reports with batch only ({products_without_batch_ids})',
        )
        return
    products_ids = products_queryset.values_list('id', flat=True)
    generate_report_function.delay(list(products_ids), request.user.email)
    modeladmin.message_user(request, 'Report will be sent soon')


@admin.action(description='Get order xlsx for selected batches manufactor fault')
def get_order_summary_xls_man_fault(modeladmin, request, batches_queryset):
    batches_manufactor_fault_qs = batches_queryset.filter(
        batch_items__reproduction_complaints__manufactor_fault=True
    ).distinct()
    batches_manufactor_fault = batches_manufactor_fault_qs.values_list('id', flat=True)

    if ElementsOrder.objects.filter(
        batches__in=batches_manufactor_fault,
        order_type=ElementsOrderType.COMPLAINT_MANUFACTOR,
    ).exists():
        modeladmin.message_user(
            request,
            'Could not generate report. '
            'Elements order with the same batches '
            'or with manufactor fault already exists!',
            level=messages.ERROR,
        )
        return HttpResponseRedirect(request.get_full_path())

    if not batches_manufactor_fault_qs.exists():
        messages.add_message(
            request,
            messages.WARNING,
            'There were no items with manufactor fault',
        )
        return HttpResponseRedirect(request.get_full_path())

    generate_order_summary_xls_manufactor_fault.delay(
        list(batches_manufactor_fault),
        request.user.email,
    )
    modeladmin.message_user(
        request,
        'Email with report will be sent to {}.'.format(request.user.username),
    )


@admin.action(description='Get filtered batching tool')
def get_filtered_batching_tool(self, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = BatchWithManufactorForm(request.POST)
        if form.is_valid():
            response = HttpResponse(status=301)
            location = (
                '/admin/batching_tool_vue?manufactor_id={}&manufactor_name={}&ids={}'
            )
            response['location'] = location.format(
                str(form.cleaned_data['manufactor'].id),
                form.cleaned_data['manufactor'].name,
                ','.join([str(x.id) for x in queryset]),
            )
            return response
        else:
            logger.warning('Applied form is not valid')

    if not form:
        form = BatchWithManufactorForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/product_batch.html',
        {
            'queryset': queryset,
            'select_across': request.POST['select_across'],
            'form': form,
            'opts': opts,
            'app_label': app_label,
            'action': 'get_filtered_batching_tool',
        },
    )


@admin.action(description='Get filtered batching tool complaint')
def get_filtered_batching_tool_complaints(modeladmin, request, queryset):
    """
    Admin action for creating batches for given manufactor.
    Producer has to be provided to be able to generate batch.
    After batch creation we dispatch creating production files.
    """

    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        queryset=queryset,
        form_class=BatchWithManufactorForm,
        success_function=batch_complaints_by_manufactor,
        success_function_kwargs={},
        template_name='admin/product_batch.html',
    )


def batch_complaints_by_manufactor(modeladmin, request, queryset, form, **kwargs):
    products_by_manufactor = defaultdict(list)
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            manufactor = form.cleaned_data['manufactor']
            products_by_manufactor[manufactor].append(product)

    product_batch_service = ProductBatchService()
    for manufactor, products in products_by_manufactor.items():
        product_batch_service.create(
            products=products,
            manufactor=manufactor,
            generate_files=True,
            batch_type=BatchType.COMPLAINTS,
            status=BatchStatus.IN_PRODUCTION,
        )
    return redirect(reverse('admin:producers_productcomplaint_changelist'))


@admin.action(description='Generate Instruction')
def generate_instruction(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for item in queryset_chunk.object_list:
            generate_instruction_task.delay(item.id)
    messages.info(
        request,
        'Manual generation started, please wait a few minutes',
    )


@admin.action(description='Generate Instruction Anonymous')
def generate_instruction_anonymous(modeladmin, request, queryset):
    for queryset_chunk in Paginator(queryset, 100):
        for item in queryset_chunk.object_list:
            generate_instruction_task.delay(item.id, anonymous=True)
    messages.info(
        request,
        'Anonymous manual generation started, please wait a few minutes',
    )


@admin.action(description='Generate Instruction for Batches')
def generate_instructions_for_selected_batches(modeladmin, request, queryset):
    batch_ids = list(queryset.values_list('id', flat=True))
    generate_instructions_for_selected_batches_task.delay(batch_ids)
    messages.info(
        request,
        'Zlecono tworzenie instrukcji dla wybranych Batchy, '
        'prośba o oczekiwanie (10-15 minut) '
        'na instrukcje przy plikach',
    )


def _get_packaging_csvs_from_batch(batch) -> dict:
    """Get packaging files with filenames from batch."""
    packaging_csvs = {}
    if not batch.details.packaging_csvs:
        raise ResourceWarning(
            f'Brakuje pliku Rozkroj pakowanie w batchu {batch.id}',
        )
    if batch.details.packaging_csvs.name.endswith('.zip'):
        with ZipFile(batch.details.packaging_csvs, 'a') as packaging_zip:
            for filename in packaging_zip.namelist():
                packaging_csvs[filename] = packaging_zip.open(filename).read()
    else:
        packaging_csvs[
            batch.details.packaging_csvs.name
        ] = batch.details.packaging_csvs.read()
    return packaging_csvs


def _combine_packaging_csvs_from_batches(batch_queryset):
    """
    This collects packaging file contents from batches in three groups.

    Why, you may ask?
    Well, ever sice Drewtur introduced a new cartoner machine, we have two ways
    of storing cartoner files: as a single txt (the old way) or as two txt
    files combined into a zip (the new way).
    Hence the three categories: we unzip the zips, and sort the content as
    either `compack` or `nexmode`. In case where there is just a txt, it lands
    into `razem` category, for backwards compatibility.
    """
    combined_lines = {
        'razem': [],
        'compack': [],
        'nextmode': [],
    }
    for batch in batch_queryset:
        packaging_csvs = _get_packaging_csvs_from_batch(batch)
        for filename, packaging_lines in packaging_csvs.items():
            if 'nextmode' in filename:
                combined_lines['nextmode'].append(bytes(packaging_lines))
            elif 'compack' in filename:
                combined_lines['compack'].append(bytes(packaging_lines))
            else:
                combined_lines['razem'].append(bytes(packaging_lines))
    combined_cartoner_files = {}
    for packaging_file_option, packaging_lines in combined_lines.items():
        if packaging_lines:
            combined_cartoner_files[
                f'Rozkroj_pakowanie_{packaging_file_option}.txt'
            ] = b''.join(packaging_lines)
    return combined_cartoner_files


@admin.action(description='Get file and lables for kartoniarka for selected batches')
def get_cardboard_merged_file(modeladmin, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for page in Paginator(queryset, 100):
            for batch in page.object_list:
                if batch.details.labels_packaging:
                    zipfile.writestr(
                        str(batch.details.labels_packaging.name.split('/')[-1]),
                        batch.details.labels_packaging.read(),
                    )
        try:
            combined_cartoner = _combine_packaging_csvs_from_batches(queryset)
        except ResourceWarning as missing_cartoner_file_warning:
            messages.error(
                request,
                missing_cartoner_file_warning.args[0],
            )
            return
        for cartoner_filename, cartoner_file in combined_cartoner.items():
            zipfile.writestr(cartoner_filename, cartoner_file)

    batch_ids = sorted([x.id for x in queryset])
    stream.seek(0)
    filename = f'kartoniarka_i_pakowanie_B{batch_ids[0]}-B{batch_ids[-1]}.zip'
    return FileResponse(stream, as_attachment=True, filename=filename)


@admin.action(description='get tylko_nameplate zip for selected batches')
def get_nameplate_files(modeladmin, request, queryset):
    stream = BytesIO()
    if queryset.count() == 1:
        batch = queryset.first()
        if not batch.details.nameplate:
            messages.error(request, 'Brak pliku tabliczki znamionowej w batchu')
            return
        return FileResponse(
            batch.details.nameplate,
            as_attachment=True,
            filename=f'tabliczki_znamionowe_{batch.manufactor.name}_B{batch.id}.txt',
        )

    grouped_batches_by_producer = defaultdict(list)
    for batch in queryset:
        if not batch.details.nameplate:
            messages.error(
                request,
                f'Brak pliku tabliczki znamionowej w batchu {batch.id}',
            )
        else:
            grouped_batches_by_producer[batch.manufactor.name].append(batch)

    with ZipFile(stream, 'w') as zipfile:
        for producer, batches in grouped_batches_by_producer.items():
            batches_ids = sorted([x.id for x in batches])
            dir_name = f'{producer}_B{batches_ids[0]}-B{batches_ids[-1]}'
            for batch in batches:
                file_name = str(batch.details.nameplate.name.split('/')[-1])
                zipfile.writestr(
                    f'{dir_name}/{file_name}',
                    batch.details.nameplate.read(),
                )

    batch_ids = sorted([x.id for x in queryset])
    stream.seek(0)
    filename = f'tabliczki_znamionowe_{batch_ids[0]}-B{batch_ids[-1]}.zip'
    return FileResponse(stream, as_attachment=True, filename=filename)


def get_cardboard_merged_file_for_producer(producer, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for queryset_chunk in Paginator(queryset, 100):
            for item in queryset_chunk.object_list:
                with ProductionSystemClient(suppress_errors=False) as ps_client:
                    cartoner_zip_data = ps_client.get_product_test_files(
                        item,
                        'cartoner',
                        manufacturer_id=producer,
                    )
                unzip_file_to_another_zip(
                    source_zip=BytesIO(cartoner_zip_data),
                    target_zip=zipfile,
                    path_in_target_zip='',
                )
    stream.seek(0)
    product_ids = [x.id for x in queryset]
    filename = f'kartoniarka_{min(product_ids):d}-{max(product_ids):d}.zip'
    return FileResponse(stream, as_attachment=True, filename=filename)


@admin.action(description='Get cartoner MPL files for selected items')
def get_cardboard_merged_file_meblepl(modeladmin, request, queryset):
    return get_cardboard_merged_file_for_producer(Manufacturers.MEBLE_PL, queryset)


@admin.action(description='Get cartoner DTR files for selected items')
def get_cardboard_merged_file_drewtur(modeladmin, request, queryset):
    return get_cardboard_merged_file_for_producer(Manufacturers.DREWTUR, queryset)


@admin.action(description='Get elements csv for meble.pl')
def get_elements_csv_meble_pl(modeladmin, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for page in Paginator(queryset, 100):
            for item in page.object_list:
                zipfile.writestr(
                    item.get_elements_csv_meble_pl_filename(),
                    item.get_production_ivy().get_elements_csv_meble_pl(item.batch),
                )

    stream.seek(0)
    return FileResponse(stream, as_attachment=True, filename='T6_elements.zip')


@admin.action(description='Get usage in xml for selected batch')
def get_usage_in_xml(modeladmin, request, queryset):
    from producers.gh.moduly_export.Ivy_export_XML import ExportXML

    production_jsons = []
    batch = queryset[0]
    for page in Paginator(queryset, 100):
        for batch in page.object_list:
            for item in batch.batch_items.all():
                production_jsons.append(item.get_serialized_product_info())

    xml_export = ExportXML.get_drawings(production_jsons, batch.id)
    response = HttpResponse(content_type='application/xml', content=xml_export[0][0])
    response['Content-Disposition'] = 'attachment; filename={}_zuzycie.xml'.format(
        '_'.join(['B' + str(batch.id) for batch in queryset])
    )
    return response


@admin.action(description='Match usage with materials')
def get_usage_in_xml_with_match(modeladmin, request, queryset):
    try:
        MatchBatchUsage.prepare_report(queryset, request.user.email)
    except ValidationError as e:
        messages.error(request, str(e))
        return
    messages.info(request, 'Report will be send.')


@admin.action(description='Generate Meble.pl package')
def generate_meblepl_package(modeladmin, request, queryset):
    for page in Paginator(queryset, 100):
        for batch in page.object_list:
            batch.generate_meblepl_package()
    messages.info(
        request,
        'Created meble pl zip for batches: {}'.format(
            ','.join([str(x) for x in queryset])
        ),
    )


@admin.action(description='Send email with production report')
@with_document_async_message
def send_email_production_report(modeladmin, document_request, request, queryset):
    generate_production_report.delay(document_request.id)


@admin.action(description='Send email with last quarter france orders report')
def send_last_quarter_orders_from_france_report(modeladmin, request, queryset):
    send_last_quarter_france_orders_report.delay(request.user.email)

    messages.info(request, f'The report will soon be sent to {request.user.email}')


@admin.action(description='Download nesting files for watty')
def download_nesting_files(modeladmin, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        skipped_batch_list = []
        for page in Paginator(queryset, 100):
            for batch in page.object_list:
                if batch.product_type != 'watty':
                    skipped_batch_list.append(batch.id)
                    continue

                batch_detail = batch.batch_details_watty

                add_file_to_zip(batch_detail.nesting_mask, zipfile)
                add_file_to_zip(batch_detail.nesting_bar, zipfile)
                add_file_to_zip(batch_detail.nesting_drawer_synchro, zipfile)
                add_file_to_zip(batch_detail.nesting_hang_slat, zipfile)
                add_file_to_zip(batch_detail.nesting_led_profile, zipfile)
                add_file_to_zip(batch_detail.lighting_completion_list, zipfile)

    if skipped_batch_list:
        # Works after refresh
        messages.info(request, f'Skipped batches with ids={skipped_batch_list}')

    stream.seek(0)
    return FileResponse(stream, as_attachment=True, filename='Nesting_all.zip')


@admin.action(description='Get wardrobe production drawings from PS')
def fetch_production_drawings_from_ps(modeladmin, request, queryset):
    queryset = queryset.filter(cached_product_type='watty')
    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            product.details.generate_production_drawings()


@admin.action(description='Export SKU completing list')
@csv_export
def export_sku_completing_list(modeladmin, request, queryset):
    header_row = [
        'Product ID',
        'Order ID',
        'SKU',
    ]
    rows = []

    for queryset_chunk in Paginator(queryset, 100):
        for product in queryset_chunk.object_list:
            serialization = product.get_serialized_product_info()
            if serialization['errors'] or not serialization.get('item', {}).get(
                'packs', []
            ):
                continue
            for pack in serialization.get('item', {}).get('packs', []):
                rows.append(
                    [
                        product.id,
                        product.order_id,
                        pack.get('type'),
                    ]
                )
    filename = f'kompletacja_{queryset.first().id}-{queryset.last().id}'
    return CSVObject(file_name=filename, header_row=header_row, rows=rows)


@admin.action(description="Export Product's Jetty/Watty to Zip")
def export_selected_products_for_file(modeladmin, request, queryset):
    return export_objects_for_file(queryset)


@admin.action(description='Export Product Complaint Jetty/Watty to Zip')
def export_selected_products_for_file_complaints(modeladmin, request, queryset):
    return export_objects_for_file(queryset, is_complaint=True)


def export_objects_for_file(queryset, is_complaint=False):
    queryset_jetty = queryset.filter(cached_product_type='jetty')
    queryset_watty = queryset.filter(cached_product_type='watty')

    zip_filename = (
        'export_gallery_complaints.zip' if is_complaint else 'export_gallery.zip'
    )
    zip_output = io.BytesIO()
    with ZipFile(zip_output, 'w') as zipfile:
        if queryset_jetty.exists():
            filename = 'jetty_complaint.json' if is_complaint else 'jetty.json'
            data_jetty = {}
            for queryset_chunk in Paginator(queryset_jetty.reverse(), 50):
                for product in queryset_chunk.object_list:
                    data_jetty[product.id] = get_data_for_exported_product(
                        product,
                        serializer=JettyImportExportSerializer,
                    )
            zipfile.writestr(filename, data=json.dumps(data_jetty))

        if queryset_watty.exists():
            filename = 'watty_complaint.json' if is_complaint else 'watty.json'
            data_watty = {}
            for queryset_chunk in Paginator(queryset_watty.reverse(), 50):
                for product in queryset_chunk.object_list:
                    data_watty[product.id] = get_data_for_exported_product(
                        product,
                        serializer=WattyImportExportSerializer,
                    )
            zipfile.writestr(filename, data=json.dumps(data_watty))

    zip_output.seek(0)
    response = FileResponse(zip_output, as_attachment=True, filename=zip_filename)
    response.headers['Cache-Control'] = 'no-cache'
    return response


def get_data_for_exported_product(product, serializer):
    gallery_object = product.order_item.order_item
    gallery_data = serializer(gallery_object).data
    product_data = {'gallery': gallery_data}
    if product.is_complaint():
        complaint_object = product.reproduction_complaints.first()
        complaint_serialized = ComplaintExportSerializer(complaint_object).data
        product_data.update({'complaint': complaint_serialized})
    return product_data


@admin.action(description='Change priority for Products')
def admin_change_priority_for_products(modeladmin, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = ChangePriorityWithHistoryForm(request.POST)
        if form.is_valid():
            priority = form.cleaned_data['priority_to_change']

            for product in queryset:
                if priority == ProductPriority.POSTPONED:
                    requested_postponed_delivery_date = form.cleaned_data[
                        'requested_postponed_delivery_date'
                    ]
                    try:
                        product.priority_updater.change_to_postponed(
                            requested_postponed_delivery_date
                        )
                    except CannotChangeToPostponePriorityException as e:
                        messages.add_message(
                            request,
                            messages.WARNING,
                            f'Changed priority for {product.id} not processed because '
                            f'"{e.message}"',
                        )
                else:
                    product.priority_updater.change_priority(
                        priority_to_change=priority,
                        owner=request.user,
                    )
            messages.add_message(
                request,
                messages.INFO,
                f'Changed priority for {queryset.count()} items',
            )
            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = ChangePriorityWithHistoryForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = modeladmin.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/producers/product/change_priority_action.html',
        {
            'products': queryset,
            'change_priority_form': form,
            'opts': opts,
            'app_label': app_label,
        },
    )


@admin.action(description='Change manufactor for Products')
def admin_change_manufactor_for_products(modeladmin, request, queryset):
    form = None
    if 'apply' in request.POST:
        form = ChangeManufactorForProductForm(request.POST)
        if form.is_valid():
            for product in queryset:
                if not product.batch:
                    manufactor_new = form.cleaned_data['manufactor_to_change']
                    product.serialization_updater.change_manufactor(
                        manufactor_new=manufactor_new,
                        owner=request.user,
                    )
                    messages.add_message(
                        request,
                        messages.INFO,
                        f'Changed manufactor for product(id={product.id})',
                    )
                else:
                    messages.add_message(
                        request,
                        messages.ERROR,
                        'You cannot change manufactor for already batched product. '
                        f'Product (id={product.id})',
                    )

            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = ChangeManufactorForProductForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = modeladmin.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/producers/product/change_manufactor_action.html',
        {
            'products': queryset,
            'change_manufactor_form': form,
            'opts': opts,
            'app_label': app_label,
        },
    )


@admin.action(
    description='Update cached features (flags, m2, color, etc.)',
)
def update_cached_features(modeladmin, request, queryset):
    paginator = Paginator(queryset, 100)
    for page in paginator:
        for product in page.object_list:
            product.serialization_updater.update_cached_features()


@admin.action(description='Mass change Quality control needed')
def change_quality_control_needed(modeladmin, request, queryset):
    count = queryset.filter(quality_control_needed=False).update(
        quality_control_needed=True
    )
    messages.add_message(
        request,
        messages.INFO,
        f'Changed Quality control needed for {count} item(s).',
    )


@admin.action(description='Mass change to Quality Blocker')
def change_status_to_quality_blocker(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Product'],
):
    updated_product_ids = []
    illegal_product_ids = []

    for product in queryset:
        if product.status == ProductStatus.TO_BE_SHIPPED:
            updated_product_ids.append(product.id)
            product.status_updater.change_status(
                status_to_change=ProductStatus.QUALITY_BLOCKER
            )
        else:
            illegal_product_ids.append(product.id)

    if updated_product_ids:
        updated_product_ids = ','.join(str(pk) for pk in updated_product_ids)
        messages.add_message(
            request,
            messages.SUCCESS,
            f'Changed Quality Blocker for {updated_product_ids} product(s).',
        )

    if illegal_product_ids:
        illegal_product_ids = ','.join(str(pk) for pk in illegal_product_ids)
        messages.add_message(
            request,
            messages.WARNING,
            f"Can't set Quality Blocker for {illegal_product_ids} product(s).",
        )


@admin.action(description='Get meblepl zips')
def get_meblepl_zips(modeladmin, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for batch in queryset:
            add_file_to_zip(batch.details.meblepl_zip, zipfile)

    stream.seek(0)
    return FileResponse(stream, as_attachment=True, filename='meblepl_all_zips.zip')


@admin.action(description='Create production order for all items in batch')
def create_production_order(modeladmin, reuqest, queryset):
    production_complaints = ProductionComplaint.objects.filter(
        reproduction_product__batch__in=queryset
    )
    return generate_production_order_as_html(production_complaints)


@admin.action(description='Lock selected batches')
def lock_selected_batches(modeladmin, request, queryset):
    batches_ids = list(queryset.values_list('pk', flat=True))
    trigger_logistic_split_on_ready_orders.delay(batches_ids)

    messages.add_message(
        request,
        messages.INFO,
        f'Successfully triggered lock on {queryset.count()} batch(es).',
    )


@admin.action(description='Withdraw products from production')
def remove_products_from_batch(modeladmin, request, queryset):
    locked_products = queryset.filter(batch__locked_at__isnull=False)
    if locked_products.exists():
        messages.add_message(
            request,
            messages.ERROR,
            f'You cannot remove locked products from batch. '
            f'Locked products '
            f'(ids={list(locked_products.values_list("id", flat=True))}). '
            f'Select only unlocked products.',
        )
        return
    gala_products = queryset.filter(batch__manufactor__name=Manufactor.GALA)
    if gala_products.exists():
        messages.add_message(
            request,
            messages.ERROR,
            f'You cannot remove gala products from batch. '
            f'You must withdraw whole batch. '
            f'(ids={list(locked_products.values_list("id", flat=True))}).',
        )
        return
    for product in queryset:
        remove_product_from_batch(product)
    recalculate_all_batch_files(
        None,
        request,
        ProductBatch.objects.filter(batch_items__in=queryset).distinct(),
    )


@admin.action(description='Withdraw products from production')
def remove_all_products_from_batches(modeladmin, request, queryset):
    locked_batches = queryset.filter(locked_at__isnull=False)
    if locked_batches.exists():
        messages.add_message(
            request,
            messages.ERROR,
            f'You cannot remove locked batches. '
            f'Locked batches '
            f'(ids={list(locked_batches.values_list("id", flat=True))}). '
            f'Select only unlocked batches.',
        )
        return
    for batch in queryset:
        products = batch.batch_items.all()
        for product in products:
            remove_product_from_batch(product)
        if batch.manufactor.is_gala:
            batch.withdrawn_from_production = datetime.now()
            batch.save(update_fields=['withdrawn_from_production'])
        else:
            batch.delete()


def remove_product_from_batch(product):
    product.batch = None
    product.manufactor = None
    product.save(update_fields=['batch', 'manufactor'])
    product.status_updater.change_status(ProductStatus.NEW)


@admin.action(description='Get flipper cnc count zip with txt for each batch')
def get_flipper_count(modeladmin, request, queryset):
    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        flippers = []
        for batch in queryset:
            for item in batch.batch_items.all():
                single = 0
                through = 0
                wall_info = ''
                slab_info = ''
                walls = []
                slabs = []
                elements = item.details_serialized['cached_serialization']['item'][
                    'elements'
                ]
                for element in elements:
                    if element['elem_type'] == 'W':
                        walls.append(element)
                    if element['elem_type'] == 'Sl':
                        slabs.append(element)
                for wall in walls:
                    left_pins = []
                    right_pins = []
                    right_domains = set()
                    left_domains = set()
                    for cnc in wall['cnc']:
                        if 'flipper' in cnc['name'] and 'pin' in cnc['name']:
                            if 'left' in cnc['name']:
                                left_pins.append(cnc)
                                left_domains.add(
                                    (
                                        (cnc['y_domain'][0], cnc['y_domain'][1]),
                                        (cnc['z_domain'][0], cnc['z_domain'][1]),
                                    )
                                )
                            elif 'right' in cnc['name']:
                                right_pins.append(cnc)
                                right_domains.add(
                                    (
                                        (cnc['y_domain'][0], cnc['y_domain'][1]),
                                        (cnc['z_domain'][0], cnc['z_domain'][1]),
                                    )
                                )
                    if not left_pins or not right_pins:
                        quantity = len(left_pins) + len(right_pins)
                        single += quantity
                        wall_info += f'{wall["surname"]}(0, {quantity}) '
                    else:
                        through_quantity = len(right_domains.intersection(left_domains))
                        through += through_quantity
                        single_quantity = (
                            len(left_pins) + len(right_pins) - 2 * through_quantity
                        )
                        single += single_quantity
                        wall_info += (
                            f'{wall["surname"]}({through_quantity}, {single_quantity}) '
                        )
                for slab in slabs:
                    flippers_quantity = 0
                    for cnc in slab['cnc']:
                        if 'flipper' in cnc['name'] and 'lock' in cnc['name']:
                            flippers_quantity += 1
                    slab_info += f'{slab["surname"]}({flippers_quantity}) '
                    single += flippers_quantity

                flippers.append(
                    f'{item.id}: {through}, {single} | {wall_info} | {slab_info}',
                )

        zipfile.writestr('flippers.txt', '\n'.join(flippers))
    stream.seek(0)

    return FileResponse(
        stream,
        as_attachment=True,
        filename='flippers.zip',
    )


@admin.action(description='Send email with reports to producers')
def send_email_with_reports_to_producers(modeladmin, request, queryset):
    grouped_batches = send_auto_batching_mail.group_batches_by_mail_conf(queryset)
    batches_with_many_order_xlsx = (
        send_auto_batching_mail.validate_if_many_order_xlsx_will_be_generated(
            grouped_batches
        )
    )
    batches_with_s93_order = validate_s93_order(grouped_batches)
    if batches_with_s93_order:
        messages.add_message(
            request,
            messages.ERROR,
            'Many orders for s93 karton will be '
            f'created for: {batches_with_s93_order}',
        )
        return
    if batches_with_many_order_xlsx:
        messages.add_message(
            request,
            messages.ERROR,
            f'Many order xlsx will be created for: {batches_with_many_order_xlsx}',
        )
        return
    for mail_conf_id, batch_ids in grouped_batches.items():
        send_batching_mail.delay(mail_conf_id, batch_ids)


@admin.action(description='Send email with proposed delivery')
def trigger_proposed_delivery_report(modeladmin, request, queryset):
    order_ids = list(queryset.values_list('order_id', flat=True))
    event = RequestProposedDeliveryReportEvent(
        order_ids=order_ids,
        email=request.user.email,
        manufactor=Manufactor.S93,  # in the future it should be chosen in form
    )
    messages.add_message(request, messages.INFO, event.response_message)


@admin.action
@with_document_async_message
def create_carton_files_for_s93(modeladmin, document_request, request, queryset):
    create_carton_files_for_s93_task.delay(
        document_request.id, list(queryset.values_list('id', flat=True))
    )


@admin.action
def gala_batching_tool(
    modeladmin: 'ModelAdmin',
    request: HttpRequest,
    queryset: QuerySet['Product'],
) -> None:
    only_sotty = queryset.filter(cached_product_type='sotty')
    if only_sotty.filter(batch__isnull=False).exists():
        messages.add_message(
            request,
            messages.ERROR,
            'You cannot batch already batched products',
        )
        return
    grouped_products = defaultdict(list)
    for product in only_sotty:
        grouped_products[f'{product.order_id}-{product.color_option.name}'].append(
            product
        )

    def _success_function(modeladmin, request, queryset, form, grouped_products):
        product_batch_service = ProductBatchService()
        manufactor = Manufactor.get_gala_manufactor()
        for products in grouped_products.values():
            product_batch_service.create(
                products=products,
                manufactor=manufactor,
                generate_files=True,
                status=BatchStatus.IN_PRODUCTION,
            )

    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        queryset=only_sotty,
        form_class=GalaBatchingForm,
        success_function=_success_function,
        success_function_kwargs={'grouped_products': grouped_products},
        template_name='admin/producers/gala_batching.html',
        extra_context={'grouped_products': dict(grouped_products)},
    )


@admin.action(description='Recalculate cartoner files')
def create_cartoner_file_for_batches(modeladmin, request, queryset):
    """
    Temporary action to allow using preciser machine on batches batched before
    deployment of release number 24.150.
    """
    for batch in queryset:
        batch.details.generate_packaging_csvs()


@admin.action(description='Generate delivered packages report - since 01.2024')
@with_document_async_message
def generate_delivered_packages_report(
    modeladmin: 'ModelAdmin',
    document_request: DocumentRequest,
    request: HttpRequest,
    queryset: QuerySet['Product'],
):
    send_delivered_packages_report.delay(document_request.id)


@admin.action(description='Generate delivered products report - since 01.2024')
@with_document_async_message
def generate_delivered_products_report(
    modeladmin: 'ModelAdmin',
    document_request: DocumentRequest,
    request: HttpRequest,
    queryset: QuerySet['Product'],
):
    send_delivered_products_report.delay(document_request.id)


@with_document_async_message
def generate_spacers_report(modeladmin, document_request, request, queryset):
    batch_ids = list(queryset.values_list('id', flat=True))
    return report_spacers_in_batches.delay(document_request.id, batch_ids)
