import glob
import json
import os
import subprocess

from xml.dom import minidom

from jinja2 import (
    Environment,
    FileSystemLoader,
)

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DIMENSION_KEYS = (
    'x',
    'y',
    'width',
    'height',
)

CLIPARTS_PATH = 'producers/gh/latex/cstm_instruction/assets'
TEX_PATH = '/producers/gh/latex/cstm_instruction/'
ASSETS_FOLDER = 'assets'
PAGE_WIDTH = 3507
PAGE_HEIGHT = 2480


def convert_svg_to_pdf(svg_file, output_file, width=None, height=None):
    doc = minidom.parse(svg_file)
    if not width:
        svg_node = doc.getElementsByTagName('svg')[0]
        svg_width = width
        if svg_node.hasAttribute('width'):
            svg_width = svg_node.getAttribute('width')
        elif svg_node.hasAttribute('viewBox'):
            svg_width = svg_node.getAttribute('viewBox').split()[2]
        width = int(
            round(
                float(
                    ''.join(x for x in svg_width if x.isdigit() or x == '.'),
                )
            )
        )

    if not height:
        svg_node = doc.getElementsByTagName('svg')[0]
        svg_height = height
        if svg_node.hasAttribute('height'):
            svg_height = svg_node.getAttribute('height')
        elif svg_node.hasAttribute('viewBox'):
            svg_height = svg_node.getAttribute('viewBox').split()[3]
        height = int(
            round(
                float(
                    ''.join(x for x in svg_height if x.isdigit() or x == '.'),
                )
            )
        )

    doc.unlink()

    width = min(width, PAGE_WIDTH)
    height = min(height, PAGE_HEIGHT)

    command_string = 'rsvg-convert -d 300 -p 300 -w {} -h {} -f pdf {}'.format(
        width, height, svg_file
    )
    subprocess.call(command_string.split(' '), stdout=open(output_file, 'w'))


def get_data_for_instruction(current_dir, path, language_code):
    pages_merged = {}
    for file_path in glob.glob(
        '{}/*.txt'.format(
            current_dir,
        )
    ):
        with open(file_path) as data_file:
            pages = json.load(data_file)

        for data in pages:
            if data['mainImage'] != '':
                image_path = os.path.join(current_dir, data['mainImage'])
                image_path_pdf = image_path.replace('svg', 'pdf')
                if not os.path.exists(image_path_pdf):
                    convert_svg_to_pdf(image_path, image_path_pdf)
                data['mainImage'] = os.path.join(BASE_DIR, image_path_pdf).replace(
                    '\\', '/'
                )
            for image in data['images']:
                image_path = os.path.join(
                    path, CLIPARTS_PATH, language_code, image['image']
                )
                image_path_pdf = '{}_{}x{}.pdf'.format(
                    image_path.split('.')[0], image['width'], image['height']
                )
                if not os.path.exists(image_path_pdf):
                    convert_svg_to_pdf(
                        image_path,
                        image_path_pdf,
                        width=image['width'],
                        height=image['height'],
                    )
                image['image'] = os.path.join(BASE_DIR, image_path_pdf).replace(
                    '\\', '/'
                )
                image['y'] = PAGE_HEIGHT - image['y'] - image['height']  # 30
                for dm in DIMENSION_KEYS:
                    if dm in image:
                        image[dm] = int(round(image[dm] / 11.8))
            for text in data['texts']:
                text['text'] = text['text'].replace('<br/>', '\\newline ')
                text['y'] = PAGE_HEIGHT - text['y'] - text['size']

                for dm in DIMENSION_KEYS:
                    if dm in text:
                        text[dm] = int(round(text[dm] / 11.8))
                if 'size' in text:
                    text['size'] = int(round(text['size'] / 4.16))

            if data['pageNumber'] in pages_merged:
                pages_merged[data['pageNumber']].append(data)
            else:
                pages_merged[data['pageNumber']] = [
                    data,
                ]
    return pages_merged


def generate_instruction_in_latex(current_dir, path, language_code):
    pages_merged = get_data_for_instruction(current_dir, path, language_code)

    j2_latex_env = Environment(
        block_start_string=r'\BLOCK{',
        block_end_string='}',
        variable_start_string=r'\VAR{',
        variable_end_string='}',
        comment_start_string=r'\#{',
        comment_end_string='}',
        line_statement_prefix='%%',
        line_comment_prefix='%#',
        trim_blocks=True,
        autoescape=False,
        loader=FileSystemLoader(path + TEX_PATH),
    )

    with open(os.path.join(current_dir, 'tmp.tex'), 'w') as output_tex:
        output_tex.write(
            j2_latex_env.get_template('instruction_template_header.tex').render(
                graphics_paths=[
                    os.path.join(BASE_DIR, ASSETS_FOLDER, language_code),
                    os.path.join(BASE_DIR, current_dir),
                ]
            )
        )

    pages_count = len(pages_merged)
    for i, page in enumerate(sorted(pages_merged.keys())):

        with open(os.path.join(current_dir, 'tmp.tex'), 'a') as output_tex:
            output_tex.write(
                j2_latex_env.get_template('instruction_template_page.tex').render(
                    entries=pages_merged[page],
                )
            )
            if not i == pages_count - 1:
                output_tex.write('\n\t\\newpage\n\n')

    with open(os.path.join(current_dir, 'tmp.tex'), 'a') as output_tex:
        output_tex.write(
            open(path + TEX_PATH + 'instruction_template_footer.tex').read()
        )

    wd = os.getcwd()
    os.chdir(current_dir)
    pdftk_command_string = (
        'pdflatex -interaction=nonstopmode -jobname=instruction {}'.format(
            os.path.join(current_dir, 'tmp.tex')
        )
    )
    subprocess.call(pdftk_command_string.split(' '), stdout=open('pdflatex.log', 'w'))
    os.chdir(wd)
