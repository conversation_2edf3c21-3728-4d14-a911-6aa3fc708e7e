import logging

from functools import wraps
from time import sleep

from requests import HTTPError
from rest_framework import status

logger = logging.getLogger('cstm')


def retry_request(max_retries: int = 3, delay_between_requests: int = 1):
    def retry_request_func(func):
        @wraps(func)
        def recursive_retry_wrapper(self, *args, retry_attempt=0, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except HTTPError as error:
                if (
                    error.response.status_code
                    in {
                        status.HTTP_504_GATEWAY_TIMEOUT,
                        status.HTTP_502_BAD_GATEWAY,
                    }
                    and retry_attempt < max_retries
                ):
                    sleep(delay_between_requests)
                    recursive_retry_wrapper(
                        self,
                        *args,
                        retry_attempt=retry_attempt + 1,
                        **kwargs,
                    )
                else:
                    logger.critical(
                        'Cant connect to remote system: %s',
                        str(error),
                    )

        return recursive_retry_wrapper

    return retry_request_func
