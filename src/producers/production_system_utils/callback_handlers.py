import io
import logging
import os

from mimetypes import guess_type
from typing import (
    ClassVar,
    Union,
)
from zipfile import ZipFile

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.forms import FileField

from producers.constants import (
    MATTY_PRODUCTION_CODES,
    WATTY_PRODUCTION_CODES,
)
from producers.enums import Manufacturers
from producers.models import (
    Product,
    ProductBatch,
)
from producers.production_system_utils.enums import FileType

logger = logging.getLogger('cstm')


class ProductionFileHandler:
    field_name: ClassVar[str]
    file_name_template: ClassVar[str] = ''

    def __init__(self, item: Union[ProductBatch, Product]):
        self.item = item

    @staticmethod
    def _update_field(
        file_contents: bytes,
        file_name: str,
        file_field: FileField,
    ) -> None:
        file_suf = SimpleUploadedFile(
            file_name,
            file_contents,
            content_type=guess_type(file_name)[0],
        )
        file_field.save(file_name, file_suf, save=False)

    def get_file_name(self, file_name_from_ps: str) -> str:
        original_file_name, file_ext = os.path.splitext(file_name_from_ps)
        file_name = self.file_name_template.format(item=self.item) or original_file_name
        return f'{file_name}{file_ext}'

    def handle_file(self, file_content: bytes, file_name_from_ps: str):
        file_field = getattr(self.item.details, self.field_name)

        file_name_with_extension = self.get_file_name(file_name_from_ps)

        self._update_field(file_content, file_name_with_extension, file_field)
        self.item.details.save(update_fields=[self.field_name])


class GcodesHandler(ProductionFileHandler):
    field_name = FileType.GCODES.field_name
    file_name_template = '{item.shelf_code}_{item.id}_gcodes'

    def handle_file(self, file_content: bytes, file_name_from_ps: str):
        if self.item.shelf_code in WATTY_PRODUCTION_CODES | MATTY_PRODUCTION_CODES:
            super().handle_file(file_content, file_name_from_ps)
        else:
            self.handle_for_jetty(file_content)

    def _add_gcodes_from_ps_to_zip(self, gcodes_from_ps, zipfile):
        with ZipFile(io.BytesIO(gcodes_from_ps), 'a') as gcodes_zf:
            for filename in gcodes_zf.namelist():
                zipfile.writestr(
                    'CNC/{}/{}'.format(self.item.id, filename),
                    gcodes_zf.open(filename).read(),
                )

    def handle_for_jetty(self, file_content):
        file_buffer = io.BytesIO()
        with ZipFile(file_buffer, 'w') as zipfile:
            self._add_gcodes_from_ps_to_zip(file_content, zipfile)

        self._update_field(
            file_buffer.getvalue(),
            f'{self.item.get_product_id()}_CNC.zip',
            self.item.details.cnc_connections_zip,
        )
        self.item.details.save(update_fields=[self.field_name])


class PackagingInstructionHandler(ProductionFileHandler):
    field_name = FileType.PACKAGING.field_name
    file_name_template = '{item.shelf_code}_{item.id}_packaging'


class FrontViewHandler(ProductionFileHandler):
    field_name = FileType.FRONT_VIEW_PDF.field_name
    file_name_template_watty = '{item.shelf_code}_{item.id}_front_view'
    file_name_template_jetty = '{product_id}_front'

    def get_file_name(self, file_name_from_ps: str) -> str:
        original_file_name, file_ext = os.path.splitext(file_name_from_ps)
        if self.item.cached_product_type == 'watty':
            file_name = self.file_name_template_watty.format(item=self.item)
        else:
            file_name = self.file_name_template_jetty.format(
                product_id=self.item.get_product_id()
            )
        file_name = file_name or original_file_name
        return f'{file_name}{file_ext}'


class FrontViewZipHandler(FrontViewHandler):
    field_name = FileType.FRONT_VIEW_ZIP.field_name


class ProductionDrawingsHandler(ProductionFileHandler):
    field_name = FileType.PRODUCTION_DRAWINGS.field_name


class ManualHandler(ProductionFileHandler):
    field_name = FileType.MANUAL.field_name


# BATCH
class NestingsHandler(ProductionFileHandler):
    field_name = FileType.NESTINGS.field_name
    file_name_template = 'B{item.id}_nestings'

    @property
    def details(self):
        return self.item.details

    def handle_file(self, file_content: bytes, file_name_from_ps: str):
        if (
            self.item.shelf_code in WATTY_PRODUCTION_CODES | MATTY_PRODUCTION_CODES
            or self.item.manufactor_id
            in {Manufacturers.DREWTUR, Manufacturers.CENTER_MEBEL, Manufacturers.TELMEX}
        ):
            super().handle_file(file_content, file_name_from_ps)
        else:
            self.handle_for_jetty(file_content)

    def handle_for_jetty(self, nesting_zip):
        file_mapping = {
            '_elementy_podstawowe.csv': self.details.nesting,
            '_elementy_podstawowe.txt': self.details.nesting,
            '_sciany_tylne.csv': self.details.nesting_backs,
            '_sciany_tylne.txt': self.details.nesting_backs,
            '_elementy_szuflad.zip': self.details.nesting_drawers,
            '_elementy_szuflad.csv': self.details.nesting_drawers,
            '_elementy_szuflad.txt': self.details.nesting_drawers,
            '_fronty_drzwi_i_szuflad.csv': self.details.nesting_front_drawers,
            '_elementy_niestandardowe.txt': self.details.nesting_bottom_drawers,
            '_uchwyty_szuflad.csv': self.details.nesting_zip,
            '_horizontale.txt': self.details.nesting_bottom_drawers,
            '_fronty_szuflad_drzwi.txt': self.details.nesting_front_drawers,
            '_blendy_uchwytu.zip': self.details.nesting_handle_blende,
            '_poprzeczka_biurka.xlsx': self.details.nesting_desk_beam,
        }
        update_fields = []
        with ZipFile(io.BytesIO(nesting_zip), 'r') as zip_with_nesting:
            for csv_file_name in zip_with_nesting.namelist():
                if self.item.manufactor_id == Manufacturers.STUDIO_93:
                    file_name_to_map = f'_{csv_file_name.split("_", maxsplit=1)[1]}'
                else:
                    file_name_to_map = csv_file_name.split('rozkroj')[1]
                if file_name_to_map in file_mapping:
                    field = file_mapping[file_name_to_map]
                    csv_file = zip_with_nesting.read(csv_file_name)
                    field.save(csv_file_name, io.BytesIO(csv_file), save=False)
                    update_fields.append(field.field.attname)
        # NOTE: in case the field that we asked for is not filled,
        #  we mark it as not needed. This happens for manufacturers that
        #  don't use the main nesting_zip
        if self.field_name not in update_fields:
            self.details.process_file_not_needed(self.field_name)
        self.details.save(update_fields=update_fields)


class CartonerHandler(ProductionFileHandler):
    # accessible from both product and batch endpoints
    field_name = FileType.CARTONER.field_name
    file_name_template = 'B{item.id}_rozkroj_pakowanie'


class BarNestingsHandler(ProductionFileHandler):
    field_name = FileType.BAR_NESTINGS.field_name
    file_name_template = 'B{item.id}_bar-nestings'


class MaskNestingsHandler(ProductionFileHandler):
    field_name = FileType.MASK_NESTINGS.field_name
    file_name_template = 'B{item.id}_mask-nestings'


class SynchroNestingsHandler(ProductionFileHandler):
    field_name = FileType.SYNCHRO_NESTINGS.field_name
    file_name_template = 'B{item.id}_synchro-nestings'


class LedProfileNestingsHandler(ProductionFileHandler):
    field_name = FileType.LED_PROFILE_NESTINGS.field_name
    file_name_template = 'B{item.id}_led-profile-nestings'


class BeamsListHandler(ProductionFileHandler):
    field_name = FileType.BEAMS_LIST.field_name
    file_name_template = 'B{item.id}_ceowniki-katowniki'


class PackagingAccessoriesListHandler(ProductionFileHandler):
    field_name = FileType.PACKAGING_ACCESSORIES.field_name
    file_name_template = 'B{item.id}_lista_pakowanych_akcesoriów'


class LightingCompletionListHandler(ProductionFileHandler):
    field_name = FileType.LIGHTING_COMPLETION.field_name
    file_name_template = 'B{item.id}_lista_kompletacji_oswietlenia'


class LabelsElementsHandler(ProductionFileHandler):
    field_name = FileType.LABELS_ELEMENTS.field_name
    file_name_template = '{item.unified_shelf_code}_{item.id}_etykiety_elementy'


class LabelsPackagingHandler(ProductionFileHandler):
    field_name = FileType.LABELS_PACKAGING.field_name
    file_name_template = 'B{item.id}_labels-packaging'


class LabelsVerticalHandler(ProductionFileHandler):
    field_name = FileType.LABELS_VERTICALS.field_name
    file_name_template = 'B{item.id}_verticals_labels'


class LabelsAdaptersHandler(ProductionFileHandler):
    field_name = FileType.LABELS_ADAPTERS.field_name
    file_name_template = 'B{item.id}_adapters_labels'


class RotatedElementsHandler(ProductionFileHandler):
    field_name = FileType.ROTATED_ELEMENTS.field_name
    file_name_template = 'B{item.id}_rotated_elements'


class HangSlatNestingsHandler(ProductionFileHandler):
    field_name = FileType.HANG_SLAT_NESTINGS.field_name
    file_name_template = 'B{item.id}_hang-slat-nestings'


PRODUCTION_FILE_HANDLERS = {
    FileType.GCODES: GcodesHandler,
    FileType.PACKAGING: PackagingInstructionHandler,
    FileType.FRONT_VIEW_PDF: FrontViewHandler,
    FileType.FRONT_VIEW_ZIP: FrontViewZipHandler,
    FileType.PRODUCTION_DRAWINGS: ProductionDrawingsHandler,
    FileType.MANUAL: ManualHandler,
    FileType.NESTINGS: NestingsHandler,
    FileType.CARTONER: CartonerHandler,
    FileType.BAR_NESTINGS: BarNestingsHandler,
    FileType.MASK_NESTINGS: MaskNestingsHandler,
    FileType.SYNCHRO_NESTINGS: SynchroNestingsHandler,
    FileType.HANG_SLAT_NESTINGS: HangSlatNestingsHandler,
    FileType.LED_PROFILE_NESTINGS: LedProfileNestingsHandler,
    FileType.BEAMS_LIST: BeamsListHandler,
    FileType.PACKAGING_ACCESSORIES: PackagingAccessoriesListHandler,
    FileType.LIGHTING_COMPLETION: LightingCompletionListHandler,
    FileType.LABELS_ELEMENTS: LabelsElementsHandler,
    FileType.LABELS_PACKAGING: LabelsPackagingHandler,
    FileType.LABELS_VERTICALS: LabelsVerticalHandler,
    FileType.LABELS_ADAPTERS: LabelsAdaptersHandler,
    FileType.ROTATED_ELEMENTS: RotatedElementsHandler,
}


def handle_ps_files_callback(item, file_type, file_content, name_from_ps):
    handler = PRODUCTION_FILE_HANDLERS.get(file_type)
    if not handler:
        logger.error(
            'Callback handler for %s does not exist. Item: %s',
            file_type,
            repr(item),
        )
        raise NotImplementedError(
            f'Callback handler for {file_type} does not exist. Item: {item}',
        )
    handler(item).handle_file(file_content, name_from_ps)
