import dataclasses
import os

from enum import Enum
from io import Bytes<PERSON>
from typing import Op<PERSON>
from zipfile import ZipFile

from custom.utils.report_file import ReportFile
from producers.batch_files_operations import (
    _get_merged_pdf_frontview,
    _get_zipped_frontview,
)
from producers.batch_files_status_updater import BatchFilesStatusUpdater
from producers.models import Manufactor


@dataclasses.dataclass
class FileForZip:
    name: str
    content: bytes


class BatchFileGetter:
    def __init__(self, batch):
        self.batch = batch
        self.details = batch.details

    def get_batch_file_field(self, field_name: str) -> list[FileForZip]:
        file_field = getattr(self.details, field_name, None)
        if file_field:
            file_for_zip = self._get_file_field_data(file_field)
            if file_for_zip:
                return [file_for_zip]
        return []

    def get_batch_file_field_unzipped(
        self, field_name: str, recursive: bool = False
    ) -> list[FileForZip]:
        file_field = getattr(self.details, field_name, None)
        if file_field and file_field.storage.exists(file_field.name):
            return self.unpack_zip_field(file_field, recursive)
        return []

    @classmethod
    def _get_file_field_data(cls, file_field) -> Optional[FileForZip]:
        if file_field and file_field.storage.exists(file_field.name):
            file_field.seek(0)
            data = file_field.read()
            file_field.seek(0)
            return FileForZip(content=data, name=os.path.basename(file_field.name))
        return None

    def get_products_details_files(self, field_name: str) -> list[FileForZip]:
        files = []
        for product in self.batch.batch_items.all():
            file_for_zip = self._get_file_field_data(
                getattr(product.details, field_name, None)
            )
            if file_for_zip:
                files.append(file_for_zip)
        return files

    @classmethod
    def unpack_zip_field(cls, field, recursive=False) -> list[FileForZip]:
        result = []
        if not field:
            return []
        with ZipFile(field) as zip_file:
            for file_name in zip_file.namelist():
                if recursive and file_name.endswith('.zip'):
                    with ZipFile(zip_file.open(file_name)) as inner_zip_file:
                        for inner_file_name in inner_zip_file.namelist():
                            result.append(
                                FileForZip(
                                    content=inner_zip_file.open(inner_file_name).read(),
                                    name=inner_file_name,
                                )
                            )
                else:
                    result.append(
                        FileForZip(
                            content=zip_file.open(file_name).read(), name=file_name
                        )
                    )
        return result

    def cnc_and_dxf(self) -> list[FileForZip]:
        result = []
        for product in self.batch.batch_items.all():
            result.extend(self.unpack_zip_field(product.details.cnc_connections_zip))
        return result

    def get_directory_from_connections_zip(self, directory):
        files = self.cnc_and_dxf()
        result_files = []
        for file in files:
            if directory in file.name.split('/'):
                file.name = file.name.split('/')[-1]
                result_files.append(file)
        return result_files

    def nesting(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting')

    def nesting_drawer_synchro(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_drawer_synchro')

    def nesting_hang_slat(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_hang_slat')

    def lighting_completion_list(self) -> list[FileForZip]:
        return self.get_batch_file_field('lighting_completion_list')

    def nameplate(self) -> list[FileForZip]:
        return self.get_batch_file_field('nameplate')

    def nesting_led_profile(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_led_profile')

    def nesting_zip(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_zip')

    def nesting_bar(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_bar')

    def rotated_elements(self) -> list[FileForZip]:
        return self.get_batch_file_field('rotated_elements')

    def nesting_backs(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_backs')

    def nesting_mask(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_mask')

    def nesting_drawers(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_drawers')

    def nesting_bottom_drawers(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_bottom_drawers')

    def nesting_front_drawers(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_front_drawers')

    def cardboard_beams_xls(self) -> list[FileForZip]:
        return self.get_batch_file_field('cardboard_beams_xls')

    def labels_elements(self) -> list[FileForZip]:
        return self.get_batch_file_field('labels_elements')

    def labels_packaging(self) -> list[FileForZip]:
        return self.get_batch_file_field('labels_packaging')

    def labels_verticals(self) -> list[FileForZip]:
        return self.get_batch_file_field('labels_verticals')

    def accessories_packaging_list(self) -> list[FileForZip]:
        return self.get_batch_file_field('accessories_packaging_list')

    def nesting_handle_blende(self) -> list[FileForZip]:
        return self.get_batch_file_field('nesting_handle_blende')

    def packaging_instruction(self) -> list[FileForZip]:
        return self.get_products_details_files('packaging_instruction')

    def packaging_instruction_unzipped(self):
        files = self.get_products_details_files('packaging_instruction')
        unpacked_files = []
        for _file in files:
            if _file.name.endswith('.zip'):
                unpacked_files.extend(self.unpack_zip_field(BytesIO(_file.content)))
            else:
                unpacked_files.append(_file)
        return unpacked_files

    def instruction(self) -> list[FileForZip]:
        return self.get_products_details_files('instruction')

    def front_view_merged_pdf(self) -> list[FileForZip]:
        front_view_file, front_view_filename = _get_merged_pdf_frontview(self.batch)
        return [
            FileForZip(content=front_view_file.getvalue(), name=front_view_filename)
        ]

    def front_view_merged_zip(self) -> list[FileForZip]:
        front_view_file, front_view_filename = _get_zipped_frontview(self.batch)
        return [
            FileForZip(content=front_view_file.getvalue(), name=front_view_filename)
        ]

    def cnc_connections_zip(self) -> list[FileForZip]:
        return self.get_products_details_files('cnc_connections_zip')

    def horizontals_pdf(self) -> list[FileForZip]:
        return self.get_products_details_files('horizontals_pdf')

    def production_drawings(self) -> list[FileForZip]:
        return self.get_products_details_files('production_drawings')

    def front_view_package(self) -> list[FileForZip]:
        return self.get_products_details_files('front_view')

    def vertical_labels(self) -> list[FileForZip]:
        return self.get_products_details_files('labels_verticals')

    def front_view_zip(self) -> list[FileForZip]:
        return self.get_products_details_files('front_view_zip')

    def cnc_s93(self):
        return self.get_directory_from_connections_zip('CNC')

    def abd(self):
        return self.get_directory_from_connections_zip('ABD')

    def bhx_abd(self):
        return self.get_directory_from_connections_zip('BHX_ABD')

    def bhx_cnc(self):
        return self.get_directory_from_connections_zip('BHX_CNC')

    def bhx(self):
        return self.get_directory_from_connections_zip('BHX')

    def s100(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('S100')

    def scx(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('scx')

    def blenda_cix(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('blenda_cix')

    def cix_horizontal(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('cix horizontal')

    def dxf(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('DXF')

    def cix(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('cix')

    def tcn_horizontals(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('TCN horizontals')

    def bpp(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('bpp')

    def mpr(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('mpr')

    def brema(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('BREMA')

    def brema_2_2(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('BREMA 2.2')

    def nanxing(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('NANXING')

    def insider(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('INSIDER')

    def rover(self) -> list[FileForZip]:
        return self.get_directory_from_connections_zip('ROVER')

    def horizontals_bpp(self) -> list[FileForZip]:
        files = self.cnc_and_dxf()
        cix_files = []
        for file in files:
            if file.name.endswith('.bpp') and 'S100' not in file.name:
                file.name = file.name.split('/')[-1]
                cix_files.append(file)
        return cix_files

    def horizontals_bpp_novum(self) -> list[FileForZip]:
        files = self.cnc_and_dxf()
        bpp_files = []
        for file in files:
            if (
                file.name.endswith('.bpp')
                and 'S100' not in file.name
                and 'bpp' not in file.name.split('/')
            ):
                file.name = file.name.split('/')[-1]
                bpp_files.append(file)
        return bpp_files

    def packaging_labels_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('labels_packaging')

    def nesting_drawers_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('nesting_drawers')

    def labels_elements_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('labels_elements')

    def labels_elements_unzipped_recursive(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('labels_elements', recursive=True)

    def vertical_labels_unzipped(self) -> list[FileForZip]:
        files = self.get_batch_file_field_unzipped('labels_verticals')
        for file in files:
            if file.name in [
                'nonstandard_vertical_labels.pdf',
                'standard_vertical_labels.pdf',
            ]:
                file.name = f'B{self.batch.id}_{file.name}'
        return files

    def adapter_labels_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('labels_adapters')

    def nesting_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('nesting_zip')

    def packaging_csvs_unzipped(self) -> list[FileForZip]:
        return self.get_batch_file_field_unzipped('packaging_csvs')


class FileTypesEnum(Enum):
    PACKAGING = 'packaging'
    PRODUCTION = 'production'
    CNC = 'cnc'

    @property
    def name_suffix(self):
        return {
            self.PACKAGING: 'pakowanie',
            self.PRODUCTION: 'pliki',
            self.CNC: 'cnc',
        }.get(self, '')

    @property
    def status_field_name(self):
        return {
            self.CNC: 'cnc_files_status',
            self.PRODUCTION: 'production_files_status',
            self.PACKAGING: 'packaging_files_status',
        }.get(self, '')


MANUFACTOR_FILES_CONFIG = {
    Manufactor.DREWTUR: {
        FileTypesEnum.PACKAGING: [
            ('cardboard_beams_xls', 'ceowniki-katowniki'),  # (func_name, file_name)
            ('labels_elements_unzipped', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('vertical_labels_unzipped', 'vertical labels'),
            ('adapter_labels_unzipped', 'adapters labels'),
            ('nameplate', 'tabliczki znamionowe'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('front_view_merged_pdf', 'front'),
            ('front_view_package', 'front/front view package'),
            ('front_view_zip', 'front/front view zip'),
            ('nesting_unzipped', 'rozkroje'),
            ('nesting_led_profile', 'rozkroje'),
            ('nesting_bar', 'rozkroje'),
            ('nesting_mask', 'rozkroje'),
            ('nesting_drawer_synchro', 'rozkroje'),
            ('nesting_hang_slat', 'rozkroje'),
            ('lighting_completion_list', 'rozkroje'),
        ],
        FileTypesEnum.CNC: [
            ('s100', 'CNC/horizontals-S100_{batch_range}'),
            ('cix', 'CNC/cix_{batch_range}'),
            ('scx', 'CNC/scx_{batch_range}'),
            ('blenda_cix', 'CNC/blenda_cix_{batch_range}'),
            ('cix_horizontal', 'CNC/cix-horizontals_{batch_range}'),
            ('horizontals_bpp', 'CNC/horizontals-bpp_{batch_range}'),
            ('dxf', 'DXF/horizontals-dxf_{batch_range}'),
        ],
    },
    Manufactor.NOVUM: {
        FileTypesEnum.PACKAGING: [
            ('cardboard_beams_xls', 'ceowniki-katowniki'),
            ('labels_elements_unzipped', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('accessories_packaging_list', 'lista z akcesoriami'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('cardboard_beams_xls', 'ceowniki-katowniki'),
            ('labels_elements_unzipped', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            (
                'accessories_packaging_list',
                'lista z akcesoriami',
            ),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('front_view_merged_pdf', 'front'),
            ('nesting', 'rozkroje/rozkroje elementy podstawowe'),
            ('nesting_handle_blende', 'rozkroje/rozkroje blendy uchwytu'),
            ('nesting_drawers_unzipped', 'rozkroje/rozkroje szuflad'),
            ('vertical_labels', 'vertical labels'),
            (
                'nesting_front_drawers',
                'rozkroje/rozkroje fronty szuflad',
            ),
            ('s100', 'horizontals-S100_{batch_range}'),
            ('tcn_horizontals', 'tcn-horizontals_{batch_range}'),
            ('horizontals_bpp_novum', 'horizontals-bpp_{batch_range}'),
            ('bpp', '{batch_range}_cnc/bpp_{batch_range}'),
            ('nameplate', 'tabliczki znamionowe'),
        ],
        FileTypesEnum.CNC: [
            ('s100', 'horizontals-S100_{batch_range}'),
            ('tcn_horizontals', 'tcn-horizontals_{batch_range}'),
            ('horizontals_bpp_novum', 'horizontals-bpp_{batch_range}'),
            ('bpp', 'bpp_{batch_range}'),
        ],
    },
    Manufactor.S93: {
        FileTypesEnum.PACKAGING: [
            ('labels_elements_unzipped_recursive', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('front_view_merged_pdf', 'front'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction_unzipped', 'instrukcje pakowania'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('nameplate', 'tabliczki znamionowe'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('nesting', None),
            ('nesting_drawers', None),
            ('nesting_front_drawers', None),
            ('nesting_bottom_drawers', None),
            ('nesting_backs', None),
        ],
        FileTypesEnum.CNC: [
            ('abd', 'cnc/ABD'),
            ('bhx_abd', 'cnc/BHX_ABD'),
            ('bhx_cnc', 'cnc/BHX_CNC'),
            ('bhx', 'cnc/BHX'),
            ('cnc_s93', 'cnc/CNC'),
            ('nesting_handle_blende', 'cnc/rozkroj blendy uchwytu'),
        ],
    },
    Manufactor.AMIR: {
        FileTypesEnum.PACKAGING: [
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('bpp', 'bpp'),
            ('labels_elements_unzipped', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('front_view_merged_pdf', 'front'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('mpr', 'mpr'),
            ('lighting_completion_list', 'pliki kompletacji oswietlenia'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('nesting_led_profile', 'rozkroje profili ledowych'),
            ('nesting_bar', 'rozkroje drazkow'),
            ('nesting_mask', 'rozkroje maskownic'),
            ('nesting_unzipped', 'rozkroj plyty/{batch_id}_nestings'),
            ('nesting_drawer_synchro', 'rozkroje synchronizatorow'),
            ('nesting_hang_slat', 'rozkroje listwa montazowa'),
            ('production_drawings', 'rysunki'),
        ],
        FileTypesEnum.CNC: [
            ('bpp', 'bpp'),
            ('mpr', 'mpr'),
            ('production_drawings', 'rysunki'),
        ],
    },
    Manufactor.CENTERMEBEL: {
        FileTypesEnum.CNC: [
            ('production_drawings', 'rysunki'),
            ('nesting_unzipped', 'PLIKI_LINIA'),
            ('brema', 'PLIKI_LINIA/BREMA'),
            ('brema_2_2', 'PLIKI_LINIA/BREMA_2_2'),
            ('nanxing', 'PLIKI_LINIA/NANXING'),
            ('insider', 'PLIKI_LINIA/INSIDER'),
            ('rover', 'PLIKI_LINIA/ROVER'),
        ],
        FileTypesEnum.PACKAGING: [
            ('labels_elements', 'etykiety elementy'),
            ('labels_packaging', 'etykiety pakowanie'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('labels_elements', 'etykiety elementy'),
            ('labels_packaging', 'etykiety pakowanie'),
            ('front_view_merged_pdf', 'front'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('lighting_completion_list', 'pliki kompletacji oswietlenia'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('nesting_led_profile', 'rozkroje profili ledowych'),
            ('nesting_bar', 'rozkroje drazkow'),
            ('nesting_mask', 'rozkroje maskownic'),
            ('nesting_drawer_synchro', 'rozkroje synchronizatorow'),
            ('production_drawings', 'rysunki'),
            ('nesting_unzipped', 'PLIKI_LINIA'),
            ('brema', 'PLIKI_LINIA/BREMA'),
            ('insider', 'PLIKI_LINIA/INSIDER'),
            ('rover', 'PLIKI_LINIA/ROVER'),
            ('brema_2_2', 'PLIKI_LINIA/BREMA_2_2'),
            ('nanxing', 'PLIKI_LINIA/NANXING'),
            ('rotated_elements', 'obracane elementy'),
        ],
    },
    Manufactor.TELMEX: {
        FileTypesEnum.PACKAGING: [
            ('cardboard_beams_xls', 'ceowniki-katowniki'),
            ('labels_elements_unzipped', 'etykiety elementy'),
            ('packaging_labels_unzipped', 'etykiety pakowanie'),
            ('instruction', 'instrukcje montazu'),
            ('packaging_instruction', 'instrukcje pakowania'),
            ('packaging_csvs_unzipped', 'rozkroj pakowania'),
            ('vertical_labels_unzipped', 'vertical labels'),
            ('nameplate', 'tabliczki znamionowe'),
        ],
        FileTypesEnum.PRODUCTION: [
            ('front_view_merged_pdf', 'front'),
            ('front_view_package', 'front/front view package'),
            ('front_view_zip', 'front/front view zip'),
            ('nesting_unzipped', 'rozkroje'),
            ('nesting_led_profile', 'rozkroje'),
            ('nesting_bar', 'rozkroje'),
            ('nesting_mask', 'rozkroje'),
            ('nesting_drawer_synchro', 'rozkroje'),
            ('nesting_hang_slat', 'rozkroje'),
            ('lighting_completion_list', 'rozkroje'),
        ],
        FileTypesEnum.CNC: [
            ('s100', 'CNC/horizontals-S100_{batch_range}'),
            ('cix', 'CNC/cix_{batch_range}'),
            ('scx', 'CNC/scx_{batch_range}'),
            ('blenda_cix', 'CNC/blenda_cix_{batch_range}'),
            ('cix_horizontal', 'CNC/cix-horizontals_{batch_range}'),
            ('horizontals_bpp', 'CNC/horizontals-bpp_{batch_range}'),
            ('dxf', 'DXF/horizontals-dxf_{batch_range}'),
            ('production_drawings', 'blenda_drawings'),
        ],
    },
}


class GenerateProducersFiles:
    def __init__(
        self, batches, file_type: FileTypesEnum, manufactor: Manufactor | None = None
    ):
        self.batches = batches
        self.file_type = file_type
        self.manufactor = manufactor or self.batches[0].manufactor

    def get_files_as_http_response(self):
        report_file = self.generate_zip()
        self.set_batches_file_status_downloaded()
        return report_file.get_as_http_response()

    def generate_zip(self):
        stream = BytesIO()
        with ZipFile(stream, 'w') as zipfile:
            for file in self.get_files_list():
                zipfile.writestr(file.name, file.content)
        return ReportFile(content=stream.getvalue(), name=self.get_zip_file_name())

    def get_files_list(self):
        files_list = []
        for batch in self.batches:
            batch_getter = BatchFileGetter(batch)
            conf = MANUFACTOR_FILES_CONFIG[self.manufactor.name][self.file_type]
            for file_type_details_field, directory in conf:
                list_of_files_getter = getattr(batch_getter, file_type_details_field)
                for file_zip in list_of_files_getter():
                    file_zip.name = self.get_file_name(
                        file_zip.name, directory, batch.id
                    )
                    files_list.append(file_zip)
        return files_list

    def batch_range(self):
        ids = [batch.id for batch in self.batches]
        return f'{min(ids)}_{max(ids)}'

    def get_zip_file_name(self):
        suffix = self.file_type.name_suffix
        if self.manufactor.name == Manufactor.S93 and self.batches[0].has_complaints:
            suffix += '_R'
        if len(self.batches) > 1:
            ids = [batch.id for batch in self.batches]
            return (
                f'{self.batches[0].get_batch_type()}_'
                f'B{min(ids)}_B{max(ids)}_{suffix}.zip'
            )
        elif len(self.batches) == 1:
            return (
                f'{self.batches[0].get_batch_type()}_'
                f'B{self.batches[0].id}_{suffix}.zip'
            )

    def get_file_name(self, file_name, directory, batch_id):
        if directory:
            directory = directory.format(
                batch_range=self.batch_range(),
                batch_id=batch_id,
            )
            file_name = f'{directory}/{file_name}'
        return file_name

    def set_batches_file_status_downloaded(self):
        for batch in self.batches:
            BatchFilesStatusUpdater(batch).set_status_after_download(
                self.file_type.status_field_name
            )
