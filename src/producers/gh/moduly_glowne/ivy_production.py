import copy
import math

from decimal import Decimal
from operator import itemgetter

from django.db.models import F
from django.utils import timezone

from past.utils import old_div

from custom.enums import (
    PhysicalProductVersion,
    ShelfType,
    Type02Color,
)
from producers.enums import (
    Manufacturers,
    ShelfElemTypeEnum,
)
from producers.gh.moduly_export.Ivy_export_SVG import ExportSVG as SVG
from producers.gh.moduly_export.Ivy_export_SVG_tables import SVGTable
from producers.gh.moduly_glowne import Ivy_Settings
from producers.gh.moduly_glowne import ivy_elements as E
from producers.gh.moduly_glowne.Ivy_Settings import (
    material_names_polish_by_shelf_type,
    material_names_polish_by_shelf_type_short,
)
from producers.gh.moduly_glowne.production_helpers import ProductionHelpers
from producers.gh.moduly_instrukcja.enums import AssemblyStyle
from production_margins.utils import build_codename_based_dict

# because PhysicalProductVersion is way too long
TREX = PhysicalProductVersion.TREX.value
RAPTOR = PhysicalProductVersion.RAPTOR.value


class IvyProduction(ProductionHelpers):
    mpl_boards = Ivy_Settings.meblepl_boards_codenames
    mpl_banding = Ivy_Settings.meblepl_banding_codenames
    mpl_banding_drawer = Ivy_Settings.meblepl_drawer_banding_codenames

    def __init__(self, production_data):

        # Dane zserializowane
        self.serialized_ivy = production_data.get('item')
        if not self.serialized_ivy:
            raise ValueError(
                'Error during IvyProduction init! '
                'Missing serialization in Product: {} with errors: {}'.format(
                    production_data.get('id_production'),
                    str(production_data.get('errors', 'no errors')),
                )
            )
        self.gallery_parameters = production_data['gallery_parameters']
        self.physical_product_version = production_data.get(
            'physical_product_version',
            TREX,
        )

        # Dane z production_data nie wymagajace deserializacji
        self.id_production = production_data['id_production']
        self.id_gallery = production_data['id_gallery']
        self.id_manufactor = production_data['id_manufactor']
        # Domyślny producent na potrzeby plików produkcyjnych to Drewtur (id=1)
        if self.id_manufactor == -1:
            self.id_manufactor = 1
        self.shelf_type = production_data['shelf_type']
        # TODO: looks unused
        self.rows_coor = production_data['item']['rows_coor']
        self.weight = production_data['item']['weight']  # Obliczona waga w kg
        self.height = production_data['item'][
            'height'
        ]  # Obliczona wysokość bez nóżek w tm
        self.width = production_data['item']['width']  # Obliczona szerokość w tm
        self.depth = production_data['item']['depth']  # Obliczona głębokość w tm
        self.row_amount = production_data['item']['row_amount']
        self.material = production_data['item']['material']
        self.compartments = production_data['item']['compartments']
        self.usage = production_data['materials']

        # Dane uzupełniane przez funkcje get_complete_element_list()
        self.elements = None  # Kompletna lista obiektów szafki

        # Dane uzupełniane przez funkcje get_packaging()
        self.elements_packaging = (
            None  # Lista obiektów pakowania (np. wypełnienia, akcesoria itp)
        )
        self.packs = None  # Obiekty paczek
        self.id_complaint_product = production_data.get('id_complaint_product')
        # assembly style for shelf - connecting modules laying (1) or standing (2)
        self.assembly_style = (
            self.serialized_ivy.get('assembly_style') or AssemblyStyle.DORIC
        )
        self.previous_only_elements_to_be_produced = None

    @property
    def is_complaint(self) -> bool:
        return bool(self.id_complaint_product)

    @property
    def is_extended(self):
        all_horizontals = self.get_elements(
            type_filter='H',
            get_flat_element_list=True,
            only_elements_to_be_produced=False,
        )
        return any(
            horizontal.joint_left + horizontal.joint_right
            for horizontal in all_horizontals
        )

    @property
    def has_horizontal_inserts(self):
        return any(
            elem.subtype == 't'
            for elem in self.get_elements(
                type_filter='I',
                get_flat_element_list=True,
            )
        )

    def _gen_elements(self):
        """Funkcja nadpisuje funkcję w klasie bazowej -
        nie należy korzystać z niej bezpośrednio!"""

        elem_class_table = {
            ShelfElemTypeEnum.HORIZONTAL: E.Horizontal,
            ShelfElemTypeEnum.VERTICAL: E.Vertical,
            ShelfElemTypeEnum.VERTICAL_SOLO: E.Vertical,
            ShelfElemTypeEnum.SUPPORT: E.Support,
            ShelfElemTypeEnum.DOOR: E.Doors,
            ShelfElemTypeEnum.BACK: E.Backs,
            ShelfElemTypeEnum.BACK_WITH_GROMMET: E.Backs,
            ShelfElemTypeEnum.BACK_DESK: E.Backs,
            ShelfElemTypeEnum.SUPPORTING_BACK: E.Backs,
            ShelfElemTypeEnum.DRAWER: E.Drawers,
            ShelfElemTypeEnum.LEG: E.Legs,
            ShelfElemTypeEnum.LONG_LEG: E.Legs,
            ShelfElemTypeEnum.JOINT: E.Joints,
            ShelfElemTypeEnum.INSERT_HORIZONTAL: E.Inserts,
            ShelfElemTypeEnum.INSERT_VERTICAL: E.Inserts,
            ShelfElemTypeEnum.INSERT_DRAWER: E.Inserts,
            ShelfElemTypeEnum.INSERT_BACK: E.Backs,
            ShelfElemTypeEnum.PLINTH_END: E.Plinth,
            ShelfElemTypeEnum.PLINTH_BEAM: E.Plinth,
            ShelfElemTypeEnum.PLINTH_BAR: E.Plinth,
            ShelfElemTypeEnum.PLINTH_BAR_BACK: E.Plinth,
            ShelfElemTypeEnum.PLINTH_BAR_FRONT: E.Plinth,
        }

        elem_list = []
        for element_type, element_class in elem_class_table.items():
            elements_of_type = [
                element
                for element in self.serialized_ivy['elements']
                if element['elem_type'] == element_type
            ]
            # in Production system elements sometimes have 2 letters
            # but they're not supportedd by cstm leftovers
            elem_list.extend(
                [
                    element_class(
                        serialized_element,
                        self.id_manufactor,
                        self.id_production,
                        self.shelf_type,
                        self.physical_product_version,
                    )
                    for serialized_element in elements_of_type
                ]
            )
        return elem_list

    def _gen_elements_packaging(self):
        return [
            E.ElementProduction(serialized, self.id_manufactor, self.id_production)
            for serialized in self.serialized_ivy['elements_packaging']
        ]

    def get_elements(
        self,
        type_filter=None,
        row_filter=None,
        module_filter=None,
        get_flat_element_list=False,
        elements_to_generate=None,
        only_elements_to_be_produced=True,
    ):
        """Zwraca listę obiektow ivy.
        Przy pierwszym zapytaniu obiekty sa deserializowane w tle.

        :param type_filter: Filtruje zwracane obiekty wg typu (string: HVSDBTLJ)
        :param row_filter: Filtruje zwracane obiekty wg NAJNIZSZEGO rzedu
        na ktorym wystepują (lista lub int)
        :param module_filter: Filtruje obiekty wg modulu na którym występują (
        lista lub int)
        :param elements_to_generate: W celu zachowania zgodnosci wstecz
        - nadpisuje type_filter
        :param get_flat_element_list: False rozbija obiekty na dict wg typu
        :param only_elements_to_be_produced: True limit elements list to elements
        actually produced (used in complaints)
        :return: Zbior obiektow posortowanych wg wystepowania w szafce
        wg założeń numeracji name obiektów
        """

        # --- Funkcje pomocnicze
        def iter_check(val):
            """Jesli val nie jest zbiorem (lista, tuple ipt.) to zmien na liste"""
            return val if hasattr(val, '__iter__') else [val]

        def sort_by_name(elem_list):
            return sorted(elem_list, key=lambda e: e.name[2:])

        # Deserialize elements if it's the first call
        #  or the previous call broke components and we need them all now
        if self.elements is None or (
            self.previous_only_elements_to_be_produced
            and not only_elements_to_be_produced
        ):
            # TODO: Zmiana do zgodności z serializacją bez rozróżniania typów
            self.elements = self._gen_elements()

        self.previous_only_elements_to_be_produced = only_elements_to_be_produced

        # --- Przygotowanie zamowionych elementow
        elements = self.elements
        if only_elements_to_be_produced:
            elements = [
                e.overwrite_components_to_be_produced()
                for e in elements
                if e.is_to_be_produced
            ]

        type_filter = elements_to_generate or type_filter
        if type_filter:
            elements = [e for e in elements if e.ELEM_TYPE in type_filter]

        if module_filter:
            module_filter = iter_check(module_filter)
            elements = [e for e in elements if e.module in module_filter]

        if row_filter:
            row_filter = iter_check(row_filter)
            elements = [e for e in elements if e.row in row_filter]

        if get_flat_element_list:
            return sort_by_name(elements)

        return {
            k: sort_by_name([e for e in elements if e.ELEM_TYPE == k])
            for k in {e.ELEM_TYPE for e in elements}
        }

    @property
    def is_thick_black_mat(self):
        return (
            self.physical_product_version >= PhysicalProductVersion.BRACHIO
            and self.shelf_type == ShelfType.TYPE02
            and self.material == Type02Color.MATTE_BLACK
        )

    def get_elements_csv_meble_pl(self, batch=None, is_new_plywood=False):
        """
        Zwraca CSV do wgrywania na meble.pl w celu przeliczania rozkrojow
        :return: [String] zawartosc pliku CSV dzielonego srednikami
        """

        elements_list = self.get_elements(get_flat_element_list=True)
        unique_elements = self.get_elements_unique_surnames(
            elements_list,
            types='HSVBDTI',
        )

        # TODO: move to producers.production_files, refactor using csv module
        csv = (
            'Nazwa;Szerokość;Oklejanie szerokości;Wysokość;Oklejanie wysokość;'
            'Grubość płyty;Ilość sztuk;Słoje;55;055 2;560;C;C 2;Obrzeże TOP;'
            'Obrzeże RIGHT;Obrzeże BOTTOM;Obrzeże LEFT;Kod plyty;ETYKIETY;'
            'Oklejanie - kryjące krótkie'
        )
        line = (
            "\n{name};{width};'=;{height};'=;{thickness};{count};{grain};"
            '{mpr_support};{mpr_bottom};{mpr_main};{mpr_lamello};;{edge_top};'
            '{edge_right};{edge_bottom};{edge_left};{board};{label};{banding_order}'
        )

        # NOTE: kolejność obrzeżowania – domyślnie po dłuższej krawędzi,
        # w przeciwnym wypadku banding_order = 1
        def banding_order_lambda(longer, shorter):
            return (
                lambda c: ''
                if abs(c[longer][1] - c[longer][0]) > abs(c[shorter][1] - c[shorter][0])
                else '1'
            )

        banding_order_by_type = {
            'Horizontal': banding_order_lambda('x_domain', 'z_domain'),
            'Vertical': banding_order_lambda('y_domain', 'z_domain'),
            'Support': banding_order_lambda('y_domain', 'x_domain'),
            'Back': banding_order_lambda('x_domain', 'y_domain'),
            'Insert': banding_order_lambda('x_domain', 'y_domain'),
            'Door': banding_order_lambda('x_domain', 'y_domain'),
            'Drawer': banding_order_lambda('x_domain', 'y_domain'),
        }
        polish_mpl_name_by_element_type = {
            'Horizontal': 'Poziom',
            'Vertical': 'Pion',
            'Support': 'Support',
            'Door': 'Drzwi',
            'Drawer': 'Drzwi',
            'Back': 'Support',
            'Insert': 'Insert',
        }
        for element_type, elements in sorted(
            unique_elements.items(), key=itemgetter(0)
        ):
            for e in sorted(elements, key=itemgetter('name')):
                count = e['count']
                name = polish_mpl_name_by_element_type[element_type]
                element = next(
                    element for element in elements_list if element.surname == e['name']
                )
                main_comp = next(
                    (c for c in element.components if c.get('main', False)), None
                )
                main_codename = element.additional['main_codename']
                board = self.mpl_boards.get(main_codename, 'missing')
                thickness = element.thickness_mm
                if main_comp:
                    x_domain = (main_comp['x_domain'][1], main_comp['x_domain'][0])
                    y_domain = (main_comp['y_domain'][1], main_comp['y_domain'][0])
                    z_domain = (main_comp['z_domain'][1], main_comp['z_domain'][0])
                    banding_order = banding_order_by_type[element_type](main_comp)
                mpr_name = element.get_gcode_name().replace('mpr/', 'MPR/')
                mpr_lamello = ''
                short_support_mpr = ''

                if element_type == 'Horizontal':
                    width = (max(x_domain) - min(x_domain)) / 100.0
                    height = (max(z_domain) - min(z_domain)) / 100.0
                    edge = self.mpl_banding[main_codename][0]
                    if element.joint_left + element.joint_right:
                        name = 'Poziom_lamello'
                        mpr_lamello = (
                            element.get_gcode_name(key='lamello')
                            .replace('mpr/', 'MPR/')
                            .replace('.mpr', '-CNC.mpr')
                        )
                    if element.production_name == 'l':
                        name = f'{name}_L'
                elif element_type == 'Vertical':
                    production_name = element.production_name.replace('=', '')
                    has_tylkliks = '+' in production_name
                    production_name = production_name.replace('+', '')
                    drilled_old_naming = (
                        self.physical_product_version == TREX
                        and len(element.surname) > 1
                    )
                    drilled_new_naming = (
                        self.physical_product_version >= RAPTOR
                        and len(production_name) > 1
                    )
                    if drilled_old_naming or drilled_new_naming:
                        name = f'{name}_w'
                    elif not has_tylkliks:
                        mpr_name = ''  # verticals without drilling: no gcode
                    width = (max(z_domain) - min(z_domain)) / 100.0
                    height = (max(y_domain) - min(y_domain)) / 100.0
                    edge = self.mpl_banding[main_codename][0]
                    if height > width:
                        height, width = width, height
                else:
                    if main_comp:
                        width = (max(x_domain) - min(x_domain)) / 100.0
                        height = (max(y_domain) - min(y_domain)) / 100.0
                    edge = self.mpl_banding[main_codename][1]
                if element_type == 'Support':
                    mpr_name = self.get_support_gcode_name(height)
                elif element_type == 'Back':
                    width = (max(x_domain) - min(x_domain)) / 100.0
                    height = (max(y_domain) - min(y_domain)) / 100.0
                    if height > width:
                        height, width = width, height
                    if element.production_name == '+':
                        name = f'{name}_wp'
                elif element_type == 'Insert':
                    if height == thickness:
                        # horizontal insert
                        height = (max(z_domain) - min(z_domain)) / 100.0
                    if width == thickness:
                        # vertical insert
                        width = (max(z_domain) - min(z_domain)) / 100.0
                    if height > width:
                        height, width = width, height
                label = (
                    'ETYKIETY/'
                    + element.get_element_label_name(
                        element.full_name,
                        prod_id=self.id_production,
                        id_manufactor=Manufacturers.MEBLE_PL,
                        shelf_type=self.shelf_type,
                    )[0]
                    + '.pdf'
                )
                # TODO: cleanup – suffixy nazwy etykiet szuflad powinny się robić
                #  w jednym miejscu (pliki je robią same)
                if element_type == 'Drawer':
                    main_component = next(
                        (
                            component
                            for component in element.components
                            if component.get('main')
                        ),
                        element.components[0],
                    )
                    label = (
                        'ETYKIETY/'
                        + element.get_element_label_name(
                            main_component['surname'] + element.production_name,
                            prod_id=self.id_production,
                            id_manufactor=Manufacturers.MEBLE_PL,
                            shelf_type=self.shelf_type,
                        )[0]
                        + '.pdf'
                    )
                if main_comp:
                    if self.is_thick_black_mat:
                        thickness = 18.5
                    csv += line.format(
                        name=name,
                        width=width,
                        height=height,
                        thickness=thickness,
                        count=count,
                        mpr_support=short_support_mpr,
                        grain=0,
                        mpr_main=mpr_name,
                        mpr_bottom='',
                        mpr_lamello=mpr_lamello,
                        edge_top=edge,
                        edge_right=edge,
                        edge_bottom=edge,
                        edge_left=edge,
                        board=board,
                        label=label,
                        banding_order=banding_order,
                    )
                if element_type == 'Drawer':
                    csv += self.get_elements_csv_drawer_lines_without_front(
                        element,
                        is_new_plywood,
                        line,
                        count,
                    )
        # Zużycia z kategorii fitting
        usage = {
            name: value.get('usage', 0)
            for name, value in (
                list(self.usage['material_data'].items())
                + list(self.usage['services_data'].items())
                + list(self.usage['packaging_data'].items())
                + list(self.usage['semiproducts_data'].items())
            )
            if value.get('usage', 0) > 0
        }

        (
            custom_pricing_factors_dict,
            element_managed_info_dict,
            material_management_cost,
        ) = self.get_settings_for_financial_report(
            batch=batch,
            manufactor_id=Manufacturers.MEBLE_PL,
            used_code_names_set=set(usage.keys()),
        )

        for codename, usage in list(usage.items()):
            manage = 'TYLKO'
            if (
                codename in element_managed_info_dict
                and not element_managed_info_dict[codename]['managed_by_us']
            ):
                manage = 'MEBLE'

            csv += '\nAKC;{codename};{usage};{manage}'.format(
                codename=codename,
                usage=usage,
                manage=manage,
            )
        if self.id_complaint_product:
            csv += '\nORIGINALORDER;{}'.format(self.id_complaint_product)
        return csv

    def get_support_gcode_name(self, height):
        gcode_name_suffix = ''
        if self.physical_product_version >= PhysicalProductVersion.BRONTO:
            gcode_name_suffix = 'x'
        return 'MPR/support{suffix}-{height}.mpr'.format(
            suffix=gcode_name_suffix,
            height=int(height),
        )

    def get_settings_for_financial_report(
        self,
        manufactor_id,
        used_code_names_set,
        batch=None,
    ):
        from production_margins.models import (
            CustomPricingFactor,
            ElementManagedInfo,
            MaterialManagementCost,
        )

        batch_date = batch.created_at if batch else timezone.now()

        custom_pricing_factors = (
            CustomPricingFactor.objects.valid_for_date(batch_date)
            .annotate(codename=F('pricing_factor_item__codename'))
            .filter(
                codename__in=used_code_names_set,
                manufactor__id=manufactor_id,
            )
            .values()
        )
        element_managed_info = (
            ElementManagedInfo.objects.valid_for_date(batch_date)
            .annotate(codename=F('pricing_factor_item__codename'))
            .filter(
                codename__in=used_code_names_set,
                manufactor__id=manufactor_id,
            )
            .values()
        )
        material_management_cost = (
            MaterialManagementCost.objects.valid_for_date(batch_date)
            .filter(manufactor_id=manufactor_id)
            .first()
        )
        if not material_management_cost:
            material_management_cost = Decimal(5.0)
        else:
            material_management_cost = Decimal(
                material_management_cost.management_cost / 100,
            )
        custom_pricing_factors_dict = build_codename_based_dict(custom_pricing_factors)
        element_managed_info_dict = build_codename_based_dict(element_managed_info)
        return (
            custom_pricing_factors_dict,
            element_managed_info_dict,
            material_management_cost,
        )

    def get_surname_with_removed_height_name_suffix(self, component):
        """
        Remove height name suffix from sides' surnames in new naming.

        For example: 'd1-2A' -> 'd1-2' to match label names.
        """
        part_surname = component.get('surname', '')
        if part_surname.startswith('d') and part_surname[-1] in {'A', 'B', 'C'}:
            part_surname = part_surname[:-1]
        return part_surname

    def get_component_label_name(self, component, element, manufactor_id):
        part_surname = self.get_surname_with_removed_height_name_suffix(
            component,
        )
        element_label_name = element.get_element_label_name(
            part_surname + element.production_name,
            prod_id=self.id_production,
            id_manufactor=manufactor_id,
            shelf_type=self.shelf_type,
        )[0]
        if 'surname' in component:
            return f'ETYKIETY/{element_label_name}.pdf'
        return "'="

    def get_gcodes_names(self, component, element):
        # mpr_support -> column I
        # mpr_bottom -> column J
        # mpr_main -> column K
        mpr_main = element.get_gcode_name(key=component['type']).replace('mpr/', 'MPR/')
        mpr_support = ''
        mpr_bottom = ''
        if 'bottom' in component['type']:
            ppv = self.physical_product_version
            if ppv < PhysicalProductVersion.PTERO:
                mpr_main = ''
                # drawer bottom g-code for MPL is split into two parts
                mpr_support = element.get_gcode_name(
                    key='T_bottom1',
                ).replace('mpr/', 'MPR/')
                mpr_bottom = element.get_gcode_name(
                    key='T_bottom2',
                ).replace('mpr/', 'MPR/')

        return mpr_main, mpr_support, mpr_bottom

    def get_elements_csv_drawer_lines_without_front(
        self,
        element,
        is_new_plywood,
        line,
        count,
    ):
        csv = ''
        for component in element.components:
            comp_type = component['type']
            if comp_type not in {'T_side1', 'T_side2', 'T_back', 'T_bottom'}:
                continue
            c_label = self.get_component_label_name(
                component,
                element,
                Manufacturers.MEBLE_PL,
            )
            height, thickness, width = self.get_drawer_dimensions(component)

            grain = {
                'T_side1': 2 if is_new_plywood else 1,
                'T_side2': 2 if is_new_plywood else 1,
                'T_back': 1 if is_new_plywood else 2,
                'T_bottom': 2 if is_new_plywood else 1,
            }[comp_type]

            board, edge = self.get_drawer_board_and_edge_code(
                component,
                is_new_plywood,
            )

            mpr_main, mpr_support, mpr_bottom = self.get_gcodes_names(
                component,
                element,
            )

            csv += line.format(
                name='Element_szuflady',
                width=width,
                height=height,
                thickness=thickness,
                count=count,
                mpr_support=mpr_support,
                grain=grain,
                mpr_main=mpr_main,
                mpr_bottom=mpr_bottom,
                mpr_lamello='',
                edge_top=edge,
                edge_right=edge,
                edge_bottom=edge,
                edge_left=edge,
                board=board,
                label=c_label,
                banding_order='',
            )
        return csv

    def get_drawer_board_and_edge_code(self, component, is_new_plywood):
        board = self.mpl_boards.get('material_plywood_clear-overlay_12', 'missing')
        if is_new_plywood:
            board = 'EDITO_S12ST01BRZ22.ME.SP'
        codename = component.get('material_codename', 'missing_codename')
        edge = ''
        if 'chipboard' in codename and '12' in codename:
            board = self.mpl_boards.get(codename, 'missing')
            banding_codenames = self.mpl_banding_drawer.keys()
            for material in self.usage['material_data'].keys():
                if material in banding_codenames:
                    edge = self.mpl_banding_drawer[material][0]
                    break
        return board, edge

    def get_dim_size(self, component, dimension):
        domain = component[f'{dimension}_domain']
        return (max(domain) - min(domain)) / 100.0

    def get_drawer_dimensions(self, component):
        # comp_type: width_dim, height_dim, thickness_dim
        dimensions = {
            'T_side1': 'yzx',
            'T_side2': 'yzx',
            'T_back': 'yxz',
            'T_bottom': 'xzy',
        }[component['type']]
        width = self.get_dim_size(component, dimensions[0])
        height = self.get_dim_size(component, dimensions[1])
        thickness = self.get_dim_size(component, dimensions[2])
        return height, thickness, width

    def get_packs(self, product=None):
        if not self.packs:
            # -- Listy elementów - podstawowych oraz pa
            self.elements_packaging = (
                self._gen_elements_packaging()
            )  # Zawsze generowane na bazie serializacji
            all_elements = (
                self.get_elements(get_flat_element_list=True) + self.elements_packaging
            )
            outer_packs = self.serialized_ivy.get('outer_packs', [])
            outer_packs_by_id = {pack['pack_id']: pack for pack in outer_packs}
            # -- Lista paczek
            self.packs = [
                E.Pack(
                    p,
                    all_elements,
                    self.id_production,
                    self.id_manufactor,
                    self.shelf_type,
                    is_complaint=self.is_complaint,
                    outer_pack=outer_packs_by_id.get(p['pack_id']),
                    product=product,
                )
                for p in self.serialized_ivy['packs']
            ]

        return self.packs

    def final_packaging_logistic_labels_svg(
        self, packaging_data=None, user_name=None, address=None
    ):
        """
        zwraca svg z etykietami logistyki na paczki, do zlozenia w jeden PDF
        :param packaging_data: pakowanie
        :param user_name: pelna nazwa klienta
        :param address: [] str,
        :return:
        """
        if packaging_data == {}:
            return None, None

        # Spakuj regal:
        packs = self.get_packs()
        drawings_data = []
        for number, pack in enumerate(packs):
            _pack_num = str(number + 1) + '/' + str(len(packs))
            paczka_opis = 'BOX_' + _pack_num
            _box_num = 'BOX ' + _pack_num
            drawings_data.append(
                pack.get_label_data(
                    user_name=user_name,
                    address=address,
                    paczka=paczka_opis,
                    pack_num=_box_num,
                )
            )
        pack_svg = SVG.gen_drawings(drawings_data)
        return pack_svg

    def final_connections_drawings_BE_svg(self):
        def translate_point_2d(point2d, translation):
            return round(point2d[0] + translation[0], 1), round(
                point2d[1] + translation[1], 1
            )

        def translate_layer(layer, translation):

            params_to_translate = {
                'line': ['start', 'end'],
                'rect': ['insert'],
                'text': ['insert'],
                'circle': ['center'],
            }

            geoms_to_translate = list(params_to_translate.keys())

            for geom_type, geoms in list(layer.items()):

                if geom_type in geoms_to_translate:

                    ps_to_translate = params_to_translate[geom_type]

                    for g_index, geom in enumerate(geoms):
                        geom_update = {
                            p_name: translate_point_2d(geom[p_name], translation)
                            for p_name in ps_to_translate
                        }

                        layer[geom_type][g_index].update(geom_update)

            return layer

        def format_layer_info(layer_info, translation):

            l_text = [
                {'text': t_obj['text'], 'insert': t_obj['insert']}
                for t_obj in layer_info['text']
                if t_obj['text'] not in ['Top', 'Bottom']
            ]

            formatted_layer = {
                'group_data': {
                    'layer': 'info',
                    'font-size': 10.0,
                    'font-family': 'arial',
                    'text-anchor': 'start',
                    'fill': 'black',
                    'stroke': 'none',
                    'layer_index': layer_info['group_data']['layer_index'],
                },
                'text': l_text,
            }

            return translate_layer(formatted_layer, translation)

        def format_text_on_layer(layer_to_format):

            formatted_text = []

            for t_index, _text in enumerate(layer_to_format['text']):

                if _text['text'] == 'FL':
                    continue
                else:
                    _text['text_size'] = 10.0
                    _text['stroke'] = 'none'
                    _text['fill'] = 'black'
                    formatted_text.append(_text)

            layer_to_format['text'] = formatted_text

            return layer_to_format

        def get_circle_label_placement(circles, circle_index):

            min_distance = 90

            collisions = {'right': 0, 'left': 0, 'top': 0, 'bottom': 0}

            circle_to_check = circles[circle_index]['center']

            for c_index, circle in enumerate(circles):

                c_center = circle['center']

                if c_index == circle_index:
                    continue

                else:
                    diff_x = circle_to_check[0] - c_center[0]
                    diff_y = circle_to_check[1] - c_center[1]

                    distance = math.sqrt((diff_x * diff_x) + (diff_y * diff_y))

                    if distance < min_distance:
                        if 0 < diff_x < min_distance:
                            collisions['left'] = 1

                        if min_distance * (-1) < diff_x < 0:
                            collisions['right'] = 1

                        if 0 < diff_y < min_distance:
                            collisions['bottom'] = 1

                        if min_distance * (-1) < diff_y < 0:
                            collisions['top'] = 1

            if not collisions['right']:
                return 'right'

            elif not collisions['left']:
                return 'left'

            elif not collisions['top']:
                return 'top'

            else:
                return 'bottom'

        def get_circle_location_on_hori(circle_center, hori_start):
            translation = (hori_start[0] * (-1), hori_start[1] * (-1))
            return translate_point_2d(circle_center, translation)

        def get_circle_label(label_text, circle_center, placement='right'):

            if placement == 'right':
                dim_translation = (10, -10)

            elif placement == 'top':
                dim_translation = (-20, 30)

            elif placement == 'left':
                dim_translation = (-60, -10)

            elif placement == 'bottom':
                dim_translation = (-20, -30)

            return {
                'text': label_text,
                'insert': translate_point_2d(circle_center, dim_translation),
            }

        def get_drilling_info(circles, hori_insert, bottom=0):

            label_prefix = 'T' if not bottom else 'B'

            labels_list = []
            info_list = []

            for c_index, circle in enumerate(circles):
                drilling_depth = None

                label_text = '{}{}'.format(label_prefix, str(c_index + 1))
                label_placement = get_circle_label_placement(circles, c_index)
                circle_label = get_circle_label(
                    label_text, circle['center'], label_placement
                )
                labels_list.append(circle_label)

                c_location_on_hori = get_circle_location_on_hori(
                    circle['center'], hori_insert
                )

                # nazwa, X, Y, glebokosc, srednica
                circle_info = (
                    label_text,
                    '{0:.1f}'.format(c_location_on_hori[0]),
                    '{0:.1f}'.format(c_location_on_hori[1]),
                    circle['radius'] * 2,
                    drilling_depth,
                )
                info_list.append(circle_info)

            return labels_list, info_list

        def get_drilling_dims_table_layers(drilling_info, hori_insert, bottom=0):

            row_height = 40
            col_names = ('nazwa', 'X', 'Y', 'd', 'gl')
            col_widths = (70, 90, 80, 60, 60)

            table_translation = [(2700, 650), (2700, -700)][bottom]

            table_insert = translate_point_2d(hori_insert, table_translation)

            hori_side = ['top', 'bottom'][bottom]
            vert_layer_name = 'table-{}-v-lines'.format(hori_side)
            hori_layer_name = 'table-{}-h-lines'.format(hori_side)
            text_layer_name = 'table-info-{}'.format(hori_side)
            insert_direction = 1 if not bottom else -1

            return SVGTable.get_table_layers(
                table_insert,
                drilling_info,
                col_widths,
                row_height,
                col_headers=col_names,
                vert_lines_layer_name=vert_layer_name,
                hori_lines_layer_name=hori_layer_name,
                text_layer_name=text_layer_name,
                insert_direction=insert_direction,
            )

        def get_drilling_labels_layer(labels_list, hori_insert, bottom=0):

            labels_layer_name = 'dim-labels-top' if not bottom else 'dim-labels-bottom'

            labels_layer = {
                'group_data': {
                    'layer': labels_layer_name,
                    'font-family': 'arial',
                    'font-size': 5.0,
                },
                'circle': [
                    {
                        'center': hori_insert,
                        'radius': 5.0,
                        'fill': 'black',
                        'stroke': 'none',
                    }
                ],
                'text': labels_list,
            }

            return labels_layer

        def get_layer_hori_labels(top_hori_insert, bottom_hori_insert):
            hori_label_location = (50, -150)
            font_size_hori_labels = 10.0

            layer_hori_labels = {
                'group_data': {
                    'stroke': 'none',
                    'fill': 'black',
                    'font-size': font_size_hori_labels,
                    'layer': 'hori-labels',
                },
            }

            # DODAJE PODPIS GORNEJ STRONY HORI
            top_hori_label_location = translate_point_2d(
                top_hori_insert, hori_label_location
            )

            layer_hori_labels['text'] = [
                {
                    'text': 'Top',
                    'text_size': font_size_hori_labels,
                    'insert': top_hori_label_location,
                    'font-family': 'arial',
                }
            ]

            # DODAJE PODPIS DOLNEJ STRONY HORI
            bottom_hori_label_location = translate_point_2d(
                bottom_hori_insert, hori_label_location
            )

            layer_hori_labels['text'].append(
                {
                    'text': 'Bottom',
                    'text_size': font_size_hori_labels,
                    'insert': bottom_hori_label_location,
                    'font-family': 'arial',
                }
            )

            return layer_hori_labels

        def get_adjusted_drawing_as_dict(drawing_to_format):

            layer_info_translation = (-3000, -600)
            bottom_hori_translation = (0, 900)

            layers_to_copy = ['markers', 'horizontal_boundary', 'lamello']
            layers_to_move = ['bg_top']

            # ZAPISUJE KOLEJNOSC WARSTW
            for l_index, l in enumerate(drawing_to_format['graphic']):
                drawing_to_format['graphic'][l_index]['group_data'][
                    'layer_index'
                ] = l_index

            # TWORZE SLOWNIK Z WARSTWAMI - edycja bedzie na podstawie kluczy
            layers_dict = {
                layer['group_data']['layer']: layer
                for layer in drawing_to_format['graphic']
            }

            current_layer_index = len(drawing_to_format['graphic'])

            # EDYTUJE I SPRAWDZAM POTRZEBE SKOPIOWANIA WARSTW
            for layer_name, layer in list(layers_dict.items()):

                if layer_name not in ['info', 'hori-labels']:
                    layers_dict[layer_name]['group_data'].update(default_styling_dict)

                else:
                    layers_dict[layer_name] = format_layer_info(
                        layers_dict['info'], layer_info_translation
                    )

                # jesli na warstwie jest teks, dopasuj formatowanie
                if 'text' in list(layer.keys()):
                    layer = format_text_on_layer(layer)

                # jesli warstwa pojawia sie na gornej i dolnej stronie horizontala,
                # skopiuj nizej
                if layer_name in layers_to_copy:

                    layer_to_add = copy.deepcopy(layer)
                    new_layer_name = layer_name + '__translated'

                    layer_to_add['group_data'].update(
                        {'layer': new_layer_name, 'layer_index': current_layer_index}
                    )
                    current_layer_index += 1

                    layers_dict[new_layer_name] = translate_layer(
                        layer_to_add, translation=bottom_hori_translation
                    )

                # jesli warstwa pojawia sie tylko na dolnej stronie horizontala,
                # przesun nizej i podmien
                elif layer_name in layers_to_move:
                    layers_dict[layer_name] = translate_layer(
                        layer, translation=bottom_hori_translation
                    )

            layers_dict['info']['rect'] = []

            return layers_dict

        def get_hori_dim_layers(layers_dict):

            layers_to_add = []

            # ZNAJDUJE POCZATKI RYSUNKOW
            top_hori_insert = layers_dict['horizontal_boundary__translated']['rect'][0][
                'insert'
            ]
            bottom_hori_insert = layers_dict['horizontal_boundary']['rect'][0]['insert']

            # ZNAJDUJE WIERCENIA
            circles_top = sorted(
                layers_dict['bg_top']['circle'], key=lambda c: c['center'][0]
            )

            circles_bottom = layers_dict['bg_bottom']['circle'] + (
                layers_dict['bg_bumper']['circle']
                if 'bg_bumper' in list(layers_dict.keys())
                else []
            )
            circles_bottom = sorted(circles_bottom, key=lambda c: c['center'][0])

            labels_list_top, drilling_info_list_top = get_drilling_info(
                circles_top, top_hori_insert
            )

            if drilling_info_list_top:
                [
                    layers_to_add.append(x)
                    for x in get_drilling_dims_table_layers(
                        drilling_info_list_top, top_hori_insert, bottom=0
                    )
                ]

                layers_to_add.append(
                    get_drilling_labels_layer(
                        labels_list_top, top_hori_insert, bottom=0
                    )
                )

            labels_list_bottom, drilling_info_list_bottom = get_drilling_info(
                circles_bottom, bottom_hori_insert, bottom=1
            )
            if drilling_info_list_bottom:
                [
                    layers_to_add.append(x)
                    for x in get_drilling_dims_table_layers(
                        drilling_info_list_bottom, bottom_hori_insert, bottom=1
                    )
                ]
                layers_to_add.append(
                    get_drilling_labels_layer(
                        labels_list_bottom, bottom_hori_insert, bottom=1
                    )
                )

            # TWORZE WARSTWE Z OPISAMI STRON HORI I DODAJE
            layers_to_add.append(
                get_layer_hori_labels(top_hori_insert, bottom_hori_insert)
            )

            return layers_to_add

        def get_formatted_drawing(drawing_to_format):

            # DOSTOSOWUJE DANE WEJSCIOWE DO POTRZEBNEGO FORMATU
            adjusted_layers_dict = get_adjusted_drawing_as_dict(drawing_to_format)

            # UKLADAM WARSTWY WG KOLEJNOSCI
            layers_list = sorted(
                list(adjusted_layers_dict.values()),
                key=lambda x: x['group_data']['layer_index'],
            )

            # DODAJE WARSTWY Z WYMIAROWANIEM HORI
            drawing_to_format['graphic'] = layers_list + get_hori_dim_layers(
                adjusted_layers_dict
            )

            return drawing_to_format

        def get_drawings_as_svg(connections):

            # INFO DO DODANIA DO PLIKU SVG
            to_add = '<?xml version="1.0" encoding="utf-8" ?>'

            canvas = (0, -800, 297 * 6.2, 210 * 6.2)

            final_svgs = []

            for c in connections:
                c['drawing']['canvas'] = canvas

                scaled_svg = SVG.get_drawing_scaled_on_page(
                    [c],
                    page_size=(297, 210),
                    scale=4.5,
                    align='top_left',
                    scale_by='drawing',
                )
                svg = SVG.gen_drawings(scaled_svg)
                svg = (to_add + svg[0][0].tostring(), svg[0][1])
                final_svgs.append(svg)

            return final_svgs

        legs_diameter = (
            Ivy_Settings.wied_num_legs
            if self.id_manufactor not in [23, 24]
            else Ivy_Settings.wied_num_legs_mpr
        )

        connections = self.get_connection_drawing_data(
            Ivy_Settings.element_nawiert_srednica, legs_diameter, transform=True
        )

        default_styling_dict = {
            'stroke': 'black',
            'fill': 'none',
            'stroke-width': 0.25,
            'font-family': 'arial',
            'stroke-linecap': 'square',
        }

        formatted_connections = [
            get_formatted_drawing(drawing)
            for drawing in sorted(
                connections,
                key=lambda x: int(x['drawing']['horizontal_number'])
                if x['drawing']['horizontal_number'].isdigit()
                else int(x['drawing']['horizontal_number'][1:]),
            )
        ]

        svg_drawings = get_drawings_as_svg(formatted_connections)

        return svg_drawings

    def get_connections_network(self):
        """funkcja zwraca dictionary network:
        "numer horizontala":{
            [0] - obiekt horizontala 'horizontal'
            [1] - liste obiektow vert i supp sasiadujacych z hori od gory 'parts_top'
            [2] - liste obiektow vert i supp sasiadujacych z hori od dolu 'parts_bottom'
        }
        tzn:
        network = {
                    "horizontal.name":
                                        {'horizontal':
                                                horizontal_object,
                                        'parts_top':
                                                parts_top,
                                        'parts_bottom':
                                                parts_bottom}}
        """
        network = {}
        horizontals = self.get_elements(
            elements_to_generate='H',
            get_flat_element_list=True,
            only_elements_to_be_produced=False,
        )
        parts = self.get_elements(
            elements_to_generate='VSDB',
            get_flat_element_list=True,
            only_elements_to_be_produced=False,
        )

        for i, hori in enumerate(horizontals):
            horizontal_name = hori.name
            hori_surname = hori.surname
            # dodaj do slownika network pod kluczem = nr horizontala ten
            # obiekt horizontala
            network[str(horizontal_name)] = {}
            network[str(horizontal_name)]['horizontal'] = hori
            network[str(horizontal_name)]['parts_top'] = []
            network[str(horizontal_name)]['parts_bottom'] = []
            for j, part in enumerate(parts):

                if (
                    (hori.level == part.level - 1)
                    or (
                        hori.level == part.level - 2 and hori.level == part.bottom_level
                    )
                    or (hori.level == 0 and part.level == 2 and part.bottom_level == 0)
                ) and (
                    (hori.x1_tm <= part.x1_tm <= hori.x2_tm)
                    or (hori.x1_tm <= part.x2_tm <= hori.x2_tm)
                ):

                    part.horizontal_above[hori_surname] = dict(
                        x_domain=[hori.x1_tm, hori.x2_tm], z_domain=hori.z_domain
                    )

                    network[str(horizontal_name)]['parts_top'].append(part)
                    if part.name not in [x.name for x in hori.parts_above]:
                        hori.parts_above.append(part)

                # elementy sasiadujace z hori od DOLU
                if (hori.level == part.level) and (
                    (hori.x1_tm <= part.x1_tm <= hori.x2_tm)
                    or (hori.x1_tm <= part.x2_tm <= hori.x2_tm)
                ):

                    part.horizontal_below[hori_surname] = dict(
                        x_domain=[hori.x1_tm, hori.x2_tm], z_domain=hori.z_domain
                    )

                    network[str(horizontal_name)]['parts_bottom'].append(part)
                    if part.name not in [x.name for x in hori.parts_below]:
                        hori.parts_below.append(part)
            # sortowanie elementow po wsp x do np generowania nozek w gcode
            hori.parts_above.sort(key=lambda n: n.x1_tm)
            hori.parts_below.sort(key=lambda n: n.x1_tm)

        return network

    def get_unique_connections_network(self):
        """funkcja filtruje network z powtarzajaych sie horizontali
        czyli takich ktore maja ten sam numer surname
        zwraca network ale tylko unikatowych horizontali
        oraz dodatkowo dict z ilosciami dla kazdego horizontala
        """
        network = self.get_connections_network()
        network_all_surname = []  # lista nazw prod horizontali w tym powtarzajace sie
        network_filter_name = []  # lista tylko nazw wlasnych unikatowych hori
        network_filter_surname = []  # lista nazw prod tylko unikatowych hori
        network_horizontals_quantity = {}  # ilosci powtarzajacych sie horizontali

        for i in network:
            network_all_surname.append(network[i]['horizontal'].surname)

        # filtrowanie network po unikatowych nazwach surname horizontali
        for i in network:
            if network[i]['horizontal'].surname not in network_filter_surname:
                network_filter_surname.append(network[i]['horizontal'].surname)
                network_filter_name.append(network[i]['horizontal'].name)
        network_filter_surname.sort()

        for i in network_filter_surname:
            _count = network_all_surname.count(i)
            network_horizontals_quantity[str(i)] = _count

        network_unique = {k: network[k] for k in network_filter_name}

        return network_unique, network_horizontals_quantity

    def get_bg_id_pid(self, bg_count):
        """zwraca listy unikatowych losowych numerow id i pid."""
        return list(range(666000, 666000 + bg_count)), list(
            range(1100, 1100 + bg_count)
        )

    def get_connections_drawings_texts(
        self, horizontal_number, horizontal_quantity, depth, length, rotation
    ):
        """
        get all texts used in connections drawings
        :param horizontal_number:
        :param horizontal_quantity:
        :param depth:
        :return:
        """
        text_frame_descript1 = {  # tekst "Element"
            'text': Ivy_Settings.text_frame_descript[0],
            'insert': (
                Ivy_Settings.text_frame_descript_insert[0],
                Ivy_Settings.text_frame_descript_insert[1],
            ),
            'text_size': Ivy_Settings.text_frame_descript_insert[2],
            'fill': 'black',
        }
        text_frame_descript2 = {  # tekst "Quantity"
            'text': Ivy_Settings.text_frame_descript[1],
            'insert': (
                Ivy_Settings.text_frame_descript_insert[3],
                Ivy_Settings.text_frame_descript_insert[4],
            ),
            'text_size': Ivy_Settings.text_frame_descript_insert[5],
            'fill': 'black',
        }
        text_frame_descript3 = {  # tekst "Shelf ID"
            'text': Ivy_Settings.text_frame_descript[2],
            'insert': (
                Ivy_Settings.text_frame_descript_insert[6],
                Ivy_Settings.text_frame_descript_insert[7],
            ),
            'text_size': Ivy_Settings.text_frame_descript_insert[8],
            'fill': 'black',
        }
        text_hori_num = {  # tekst nr horizontala np 1
            'text': str(horizontal_number),
            'insert': (
                Ivy_Settings.text_hori_num_insert[0],
                Ivy_Settings.text_hori_num_insert[1],
            ),
            'text_size': Ivy_Settings.text_hori_num_insert[2],
            'fill': 'black',
        }
        text_hori_quantity = {  # tekst ilosc horizotnali o danym numerze
            'text': horizontal_quantity,
            'insert': (
                Ivy_Settings.text_hori_quantity_insert[0],
                Ivy_Settings.text_hori_quantity_insert[1],
            ),
            'text_size': Ivy_Settings.text_hori_quantity_insert[2],
            'fill': 'black',
        }
        text_prod_id = {  # tekst nr prod id szafki
            'text': self.id_production,
            'insert': (
                Ivy_Settings.text_prod_id_insert[0],
                Ivy_Settings.text_prod_id_insert[1],
            ),
            'text_size': Ivy_Settings.text_prod_id_insert[2],
            'fill': 'black',
        }
        text_top = {  # tekst "Top"
            'text': Ivy_Settings.text_hori_side[0],
            'insert': (Ivy_Settings.frame_insert_X - 400, old_div(depth, 2)),
            'text_size': Ivy_Settings.text_frame_size_big,
            'fill': 'black',
        }
        text_bottom = {  # tekst "Bottom"
            'text': Ivy_Settings.text_hori_side[1],
            'insert': (Ivy_Settings.frame_insert_X - 400, 900),
            'text_size': Ivy_Settings.text_frame_size_big,
            'fill': 'black',
        }

        if not rotation:
            text_fl_bottom = [
                {  # tekst "FL" front left oznacza przod horizontala
                    'text': Ivy_Settings.text_fl,
                    'insert': (
                        old_div(-Ivy_Settings.markers_length, 3),
                        depth + old_div(Ivy_Settings.markers_length, 3),
                    ),
                    'text_size': old_div(Ivy_Settings.text_frame_size_big, 2),
                    'fill': 'black',
                }
            ]
            text_fl_top = [
                {
                    'text': Ivy_Settings.text_fl,
                    'insert': (
                        old_div(-Ivy_Settings.markers_length, 3),
                        old_div(-Ivy_Settings.markers_length, 3),
                    ),
                    'text_size': old_div(Ivy_Settings.text_frame_size_big, 2),
                    'fill': 'black',
                }
            ]

        else:
            text_fl_bottom = [
                {  # tekst "FL" front left oznacza przod horizontala
                    'text': Ivy_Settings.text_fl,
                    'insert': (
                        length + old_div(Ivy_Settings.markers_length, 3),
                        old_div(-Ivy_Settings.markers_length, 3),
                    ),
                    'text_size': old_div(Ivy_Settings.text_frame_size_big, 2),
                    'fill': 'black',
                }
            ]

            text_fl_top = [
                {
                    'text': Ivy_Settings.text_fl,
                    'insert': (
                        length + old_div(Ivy_Settings.markers_length, 3),
                        depth + old_div(Ivy_Settings.markers_length, 3),
                    ),
                    'text_size': old_div(Ivy_Settings.text_frame_size_big, 2),
                    'fill': 'black',
                }
            ]

        texts = {
            'text_frame_descript1': text_frame_descript1,
            'text_frame_descript2': text_frame_descript2,
            'text_frame_descript3': text_frame_descript3,
            'text_hori_num': text_hori_num,
            'text_hori_quantity': text_hori_quantity,
            'text_prod_id': text_prod_id,
            'text_top': text_top,
            'text_bottom': text_bottom,
            'text_fl_bottom': text_fl_bottom,
            'text_fl_top': text_fl_top,
        }

        return texts

    def get_connection_drawing_data(
        self, radius_bg_part, diameter_bg_leg, transform=False
    ):
        """funkcja zbiera z get_connections_network dane do nawiertow w horizontalu
        transform = True przesuwa calosc rysunku do pkt XY0
        """
        connection_drawing = (
            []
        )  # zbiorcza lista danych do rys dla wszystkich horizotali
        # z network wybierz tylko unikatowe zbiory
        # tzn bez powtarzajacych sie horizontali
        (
            network_unique,
            network_horizontals_quantity,
        ) = self.get_unique_connections_network()
        network_surname = []
        radius_bumper = Ivy_Settings.wied_num_bumper[self.shelf_type]
        # stworz liste wszystkich surname horizontali w network_unique
        for i in network_unique:
            network_surname.append(network_unique[i]['horizontal'].surname)
        network_surname.sort()
        legs_objects = self.get_elements(
            type_filter='L',
            get_flat_element_list=True,
            only_elements_to_be_produced=False,
        )

        # store shelf material to pick proper cnc operations of edges
        # in natural wood shelf
        material = self.material

        # if manufactor is drewtur, meblepl or inex,
        # horizontal sides cnc milled in 2 steps, rotated in X between milling
        mirror_bottom = True if self.id_manufactor in {1, 25, 31} else False
        # check row heights old/new (different offset for doors bumper cnc
        rows_type = self.serialized_ivy.get('rows_type', 0)

        # dla kazdego horizontala w network_unique zbierz dane od sasiadow:
        for key in network_unique:
            horizontal_and_parts = network_unique[key]
            horizontal = horizontal_and_parts[
                'horizontal'
            ]  # wybranie obiektu horizontala z networku
            hori_rectangle = []  # lista roznych stylow zapisu obrysow horizontala
            hori_rect_dict = {}  # dane obrysu horizontala zapis w stylu insert/size
            text_info = []
            circles_top = []  # nawierty bg na gorze horizontala
            circles_bottom = []  # nawierty bg na dole horizontala
            circles_bumper = []
            horizontal_number = (
                horizontal.surname
            )  # nr horizontala wg numeracji produkcyjnej
            horizontal_level = horizontal.level
            file_name = (
                '%s' % horizontal_number
            )  # stworz nazwe pliku dla pojedynczego horizontala
            # obrys horizontala
            hori_rect_dict['insert'] = (0, 0)
            # hori dimensions converted to mm
            lenght = self.unit_converter(
                horizontal.get_lenght(), to_unit='mm', to_int=False
            )
            depth = self.unit_converter(horizontal.depth, to_unit='mm', to_int=False)
            hori_rect_dict['size'] = (lenght, depth)
            # print horizontal.get_lenght()
            # dodanie tzw "wasow" na rysunku connections (znaczniki do pozycjonowania)
            markers = [
                {'start': (-Ivy_Settings.markers_length, depth), 'end': (0, depth)},
                {'start': (0, depth), 'end': (0, depth + Ivy_Settings.markers_length)},
                {
                    'start': (lenght, 0),
                    'end': (lenght, 0 - Ivy_Settings.markers_length),
                },
                {
                    'start': (lenght, 0),
                    'end': (lenght + Ivy_Settings.markers_length, 0),
                },
            ]
            # info ramka dla opisu pojeydnczych horizotali do rys connections zbiorczy
            frames = Ivy_Settings.frame

            hori_rectangle.append(hori_rect_dict)
            # text.append((horizontal, '50%', '50%'))
            # Dodaj nawierty z goryz wyjatkiem ostatniego horizontala
            _h_name = horizontal.surname
            # NOTE rotate horizontal by X axis to have lamello always on left side
            # (drewtur only)
            flip_xy = (
                [
                    horizontal.x_domain[1] - horizontal.x_domain[0],
                    horizontal.z_domain[1] - horizontal.z_domain[0],
                ]
                if horizontal.joint_right and not horizontal.joint_left
                else [0, 0]
            )

            # Sprawdz laczanie

            lamello_lines = []
            lamello_offset_from_edge = 100.0

            # Jesli horizontal JEST obrocony i ma lamello TYLKO na prawej
            # krawedzi
            # - rysuje na lewej krawedzi
            rotation = 0
            if flip_xy != [0, 0]:

                rotation = 1

                markers = [
                    {'start': (-Ivy_Settings.markers_length, 0), 'end': (0, 0)},
                    {'start': (0, -Ivy_Settings.markers_length), 'end': (0, 0)},
                    {
                        'start': (lenght, depth),
                        'end': (lenght, depth + Ivy_Settings.markers_length),
                    },
                    {
                        'start': (lenght, depth),
                        'end': (lenght + Ivy_Settings.markers_length, depth),
                    },
                ]

                lamello_lines.append(
                    {
                        'start': (-50, lamello_offset_from_edge),
                        'end': (50, lamello_offset_from_edge),
                    }
                )

                lamello_lines.append(
                    {
                        'start': (-50, depth - lamello_offset_from_edge),
                        'end': (50, depth - lamello_offset_from_edge),
                    }
                )

            # horizontal nie jest obrocony,
            # ma lamello na lewej krawedzi - rysuje na lewej
            elif horizontal.joint_left:

                lamello_lines.append(
                    {
                        'start': (-50, lamello_offset_from_edge),
                        'end': (50, lamello_offset_from_edge),
                    }
                )

                lamello_lines.append(
                    {
                        'start': (-50, depth - lamello_offset_from_edge),
                        'end': (50, depth - lamello_offset_from_edge),
                    }
                )

                # jest tez lamello na prawej krawedzi - rysuje na prawej
                if horizontal.joint_right:
                    lamello_lines.append(
                        {
                            'start': (lenght - 50, lamello_offset_from_edge),
                            'end': (lenght + 50, lamello_offset_from_edge),
                        }
                    )

                    lamello_lines.append(
                        {
                            'start': (lenght - 50, depth - lamello_offset_from_edge),
                            'end': (lenght + 50, depth - lamello_offset_from_edge),
                        }
                    )

            texts_dict = self.get_connections_drawings_texts(
                str(horizontal_number),
                str(network_horizontals_quantity[str(horizontal_number)]),
                depth,
                length=lenght,
                rotation=rotation,
            )
            text_info.extend(
                (
                    texts_dict['text_hori_num'],
                    texts_dict['text_hori_quantity'],
                    texts_dict['text_frame_descript1'],
                    texts_dict['text_frame_descript2'],
                    texts_dict['text_frame_descript3'],
                    texts_dict['text_prod_id'],
                    texts_dict['text_top'],
                    texts_dict['text_bottom'],
                )
            )

            for element in horizontal_and_parts.get('parts_top', []):
                for j in element.get_bg(
                    self.depth,
                    transform_to_XY0=transform,
                    hori_to_pick=_h_name,
                    mirror_bottom=mirror_bottom,
                    rows_type=rows_type,
                    flip_xy=flip_xy,
                )['bg_top']:
                    bg_top = {}
                    bg_top['ELEM_TYPE'] = element.ELEM_TYPE
                    if element.ELEM_TYPE == 'D':
                        # dodaj nawierty pod odbojniki do warstwy
                        # "layer": "bg_bumper"
                        # wykorzystywanej tylko w gcode template
                        bg_top['center'] = j
                        bg_top['radius'] = radius_bumper / 2.0
                        circles_bumper.append(bg_top)
                    else:
                        bg_top['center'] = j
                        bg_top['radius'] = radius_bg_part
                        circles_top.append(bg_top)
            # Jesli horizontal NIE JEST z poziomu 0
            if horizontal.level != 0:
                for element in horizontal_and_parts.get('parts_bottom', []):
                    for j in element.get_bg(
                        self.depth,
                        transform_to_XY0=transform,
                        hori_to_pick=_h_name,
                        mirror_bottom=mirror_bottom,
                        rows_type=rows_type,
                        flip_xy=flip_xy,
                    )['bg_bottom']:
                        bg_bottom = {}
                        bg_bottom['ELEM_TYPE'] = element.ELEM_TYPE
                        if element.ELEM_TYPE == 'D':
                            # dodaj naweirty pod odbojniki do warstwy
                            # "layer": "bg_bumper"
                            # wykorzystywanej tylko w gcode template
                            bg_bottom['center'] = j
                            bg_bottom['radius'] = radius_bumper / 2.0
                            circles_bumper.append(bg_bottom)
                            # circles_bottom.append(bg_bottom)
                        else:
                            bg_bottom['center'] = j
                            bg_bottom['radius'] = radius_bg_part
                            circles_bottom.append(bg_bottom)

            # Jesli horizontal JEST z poziomu 0 dodaj nozki od spodu
            elif horizontal.level == 0:
                _legs = horizontal.get_horizontal_legs_bg(
                    legs_objects, transform=transform, flip_xy=flip_xy
                )

                for leg in _legs:
                    bg_bottom = {'center': leg, 'radius': diameter_bg_leg / 2.0}
                    circles_bottom.append(bg_bottom)
            # generowanie unikatowych nr id i pid dla nawiertow
            bg_id, bg_pid = self.get_bg_id_pid(
                len(circles_bottom + circles_top + circles_bumper)
            )
            for i, item in enumerate(circles_bottom + circles_top + circles_bumper):
                item['id'] = bg_id[i]
                item['pid'] = bg_pid[i]

            joint_left = horizontal.joint_left
            joint_right = horizontal.joint_right
            mpr_name = horizontal.get_gcode_name()
            if '/' in mpr_name:
                mpr_name = mpr_name.split('/')[1]
            # Utworz slownik z danymi rysunku:
            drawing_data = {
                'drawing': {
                    'file_name': file_name,
                    'layers_list': [
                        'markers',
                        'bg_top',
                        'bg_bottom',
                        'horizontal_boundary',
                        'info',
                        'bg_bumper',
                        'lamello',
                    ],
                    'horizontal_number': horizontal_number,
                    'horizontal_level': horizontal_level,
                    # rotate horizontal when lamello only on right side
                    # for 0,0 base for drewtur
                    'horizontal_joint_left': joint_left
                    if not flip_xy[0]
                    else joint_right,
                    'horizontal_joint_right': joint_right
                    if not flip_xy[0]
                    else joint_left,
                    'horizontal_material': material,
                    'mpr_name': mpr_name,
                    'mpr_lamello_name': mpr_name.replace('.mpr', '-CNC.mpr'),
                    'id_manufactor': self.id_manufactor,
                },
                'graphic': [
                    {
                        'group_data': {
                            'id': 'Obrys',
                            'class': 'default',
                            'layer': 'markers',
                        },
                        'line': markers,
                    },
                    {
                        'group_data': {
                            'id': 'Obrys',
                            'class': 'default',
                            'layer': 'horizontal_boundary',
                        },
                        'rect': hori_rectangle,
                    },
                    {
                        'group_data': {'horizontal_side': 'top', 'layer': 'bg_top'},
                        'circle': circles_top,
                        'text': texts_dict['text_fl_top'],
                    },
                    {
                        'group_data': {
                            'horizontal_side': 'bottom',
                            'layer': 'bg_bottom',
                        },
                        'circle': circles_bottom,
                        'text': texts_dict['text_fl_bottom'],
                    },
                    {
                        'group_data': {'layer': 'info', 'class': 'default'},
                        'rect': frames,
                        'text': text_info,
                    },
                    {
                        'group_data': {
                            'horizontal_side': 'bottom',
                            'layer': 'bg_bumper',
                        },
                        'circle': circles_bumper,
                        'text': texts_dict['text_fl_bottom'],
                    },
                    {
                        'group_data': {
                            'layer': 'lamello',
                            'class': 'default',
                            'id': 'Lamello',
                        },
                        'line': lamello_lines,
                    },
                ],
            }
            connection_drawing.append(drawing_data)
        return connection_drawing

    @classmethod
    def get_elements_unique_surnames(cls, elements, types='HSV'):
        """
        zwraca dict z listami unikatowych nazw elementow w szafce i ich ilosciami
        :param:
        :return:
        """
        type_full_names = {
            'H': 'Horizontal',
            'V': 'Vertical',
            'S': 'Support',
            'D': 'Door',
            'T': 'Drawer',
            'B': 'Back',
            'I': 'Insert',
            'P': 'Plinth',
        }
        type_full_names = {k: v for k, v in list(type_full_names.items()) if k in types}
        types_unique = {v: {} for v in list(type_full_names.values())}

        for t, type_name in list(type_full_names.items()):
            unique_names = set(e.surname for e in elements if e.ELEM_TYPE == t)
            types_unique[type_name] = [
                dict(name=n, count=[e.surname for e in elements].count(n))
                for n in unique_names
            ]
        return types_unique

    def get_modules(self):
        return list({x.module for x in self.get_elements(get_flat_element_list=True)})

    @classmethod
    def get_elements_bounding_rect(cls, elements):
        """
        dlugosci i szerokosc dowolnej grupy elementow, bouding rectangle
        :param elements:
        :return: dlugosc i wysokosc w jednostkach TM
        """
        d_x = []
        d_y = []
        for e in elements:
            d_x.append(e.x_domain[0])
            d_x.append(e.x_domain[1])
            d_y.append(e.y_domain[0])
            d_y.append(e.y_domain[1])
        min_x = min(d_x)
        max_x = max(d_x)
        min_y = min(d_y)
        max_y = max(d_y)
        length = abs(max_x - min_x)
        height = abs(max_y - min_y)
        return length, height

    @property
    def depth_mm(self):
        """
        depth in MM units, not precise, DO NOT USE IN MANUFACTURING
        :return:
        """
        return self.unit_converter(self.depth, to_unit='mm', to_int=False)

    # ==== PAKOWANIE ===================================================================

    @property
    def formatted_id(self):
        return f'{self.id_production:_}'.replace('_', ' ')

    # BE_UPDATE: TODO - FINAL 2.2 - OK
    def final_packaging_drawings_BE(
        self,
        one_pack_per_pdf=False,
        country_pl='',
    ):
        """Zwraca rysunki paczek."""
        # -- Rysunki

        packs = self.get_packs()
        pack_types = sorted(list({pack.typ[-2] for pack in packs}))
        drawingdata = [[] for _ in pack_types]
        paczki = [[] for _ in pack_types]
        _material_types = {
            # Type 01
            0: [
                'white',
                'black',
                'yellow',
                'grey',
                'aubergine',
                'beige',
                'red',
                'yellow',
                'dusty-pink',
                'cobalt-blue',
                'dark-brown',
                'green-agava',
            ],
            # Type 02
            1: [
                'white',
                'red',
                'midnight-blue',
                'beige',
                'green',
                None,
                'mat-black',
                'blue',
                'burgundy',
                'cotton',
                'gray',
                'dark-gray',
                'yellow-mustard',
                'forest-green',
                'lilac',
                'reisinger-pink',
                'sage-green',
                'stone-gray',
                'walnut',
            ],
            # Veneer Type 01
            2: ['ash', 'oak', 'dark-oak'],
        }
        material = _material_types[self.shelf_type][self.gallery_parameters['material']]
        material_pl = material_names_polish_by_shelf_type.get(self.shelf_type, {}).get(
            self.gallery_parameters['material'], ''
        )
        material_pl_short = material_names_polish_by_shelf_type_short.get(
            self.shelf_type, {}
        ).get(self.gallery_parameters['material'], '')
        for number, pack in enumerate(packs, start=1):
            pack_description = f'{number}/{len(packs)}'
            pack_type = pack_types.index(pack.typ[-2])
            table_data = pack.get_table_data(
                self.id_production,
                paczka=pack_description,
                color=material,
                color_pl=material_pl_short,
                country_pl=country_pl,
                shelf_type=self.shelf_type,
                ppv=self.physical_product_version,
            )
            if one_pack_per_pdf:
                # We don't want packs sorted by types when we have single pack per pdf
                paczki[0].append(pack)
                drawingdata[0].append(table_data)
            else:
                paczki[pack_type].append(pack)
                drawingdata[pack_type].append(table_data)

        footer = 'Shelf ID: {} - Mat: {}'.format(self.formatted_id, material_pl)
        svg_kwargs = {
            'scale': 0.328,
            'pagex': 210,
            'pagey': 297,
            'frame': True,
            'footer': True,
            'footerstr': footer,
            'tryb': 0 if one_pack_per_pdf else 1,
        }

        if self.id_manufactor == Manufacturers.DREWTUR and one_pack_per_pdf:
            pack_svg = SVG.get_arranged_drawings(
                drawingdata,
                footersize=24,
                sort_packs_by_type=True,
                **svg_kwargs,
            )
        else:
            pack_svg = SVG.get_arranged_drawings(
                drawingdata,
                footersize=8,
                sort_packs_by_type=True,
                **svg_kwargs,
            )
            pack_svg = SVG.gen_names(
                pack_svg,
                name='T1_' + str(self.id_production) + '_pakowanie',
                extension='.svg',
            )
        return [(x[0].tostring(), x[1]) for x in pack_svg]

    def new_packaging_box_files(self):
        """
        Cartoner input files for new Drewtur and Novum machines.

        Used for shelves batched with DTR, NOV and S93.
        """
        cartoner_nesting_strings = []
        fields = [
            '{shelf_id}-{pack_id}',
            '{pack_inner_length}',
            '{pack_inner_width}',
            '{pack_inner_height}',
            '{cardboard_shape}',
            '99|99',
            '4|4',  # cardboard thickness
            '4|4',
            '{label_path}',
            '0',
            '*|*',
            '*|*',
            '*|*',
            '*|*',
            '0|0',
            '{shape_parameters}',
            '1',
            '1',
            '1',
        ]
        template_line = ';'.join(fields)
        for pack in self.get_packs():
            pack_inner_length = int(pack.dim_x)
            pack_inner_width = int(pack.dim_y)
            pack_inner_height = int(math.ceil(pack.dim_z))
            pack_contains_horizontals = any(
                element.ELEM_TYPE == 'H' for element in pack.all_elements
            )
            pack_is_long_legs_pack = all(
                element.ELEM_TYPE in {'L', 'LEG_BOX'} for element in pack.all_elements
            )

            label_path = '0#ETICH#D:\\AUTOIMPORT\\LABEL\\{label_filename}#0#0#'.format(
                label_filename=pack.pack_label_file_name,
            )

            if self.shelf_type in (1, 2) and pack_contains_horizontals:
                # shape with additional bumpers for Type 02 and Veneer
                shape = 'P300DW2'
                k1 = 18
                k2 = pack_inner_height
                k3 = 10
                k4 = pack_inner_height
                k5 = 50
                shape_parameters = f'{k1}#*#{k3}#{k4}#{k5}|*#{k2}#*#*'
            else:
                shape = 'P300DW'
                k1 = 50
                k2 = min(
                    math.ceil(pack_inner_length / 2) - 25,
                    pack_inner_height,
                )
                k3 = 10
                k4 = k2
                shape_parameters = f'{k1}#*#{k3}#{k4}|*#{k2}#*#*'
            if pack_is_long_legs_pack:
                shape = '0454R'
                shape_parameters = '*|*'

            cartoner_nesting_strings.append(
                template_line.format(
                    shelf_id=self.id_production,
                    pack_id=pack.pack_id,
                    pack_inner_width=pack_inner_width,
                    pack_inner_length=pack_inner_length,
                    pack_inner_height=pack_inner_height,
                    cardboard_shape=shape,
                    label_path=label_path,
                    shape_parameters=shape_parameters,
                )
            )
        return cartoner_nesting_strings

    def final_packaging_box_files(self, jezyk_dystans_h=28, jezyk_dystans_x=40):
        """
        Pliki do produkcji kartonów. Dla producentów poza Meble.pl
        :return:
        """
        # https://docs.google.com/spreadsheets/d/1Vu0fCzLWK6voaPcxTnqW7wtqiyUvlghFtS3KxhqJwsU/edit?usp=sharing
        txt = []
        # NA PRZYSZLOSC: fajnie by bylo moc osobno obracac wieka i spody,
        # ale produkcja mowi, ze na razie sie nie da
        for pack in self.get_packs():
            a = int(pack.dim_x)  # if not pack.rotated else int(pack.dim_y)
            b = int(pack.dim_y)  # if not pack.rotated else int(pack.dim_x)
            c = int(math.ceil(pack.dim_z))
            k = int(min(c - jezyk_dystans_h, old_div((a - jezyk_dystans_x), 2)))
            k = k if c - k > jezyk_dystans_h else c
            k1 = '*'
            shape = 'P300DW'
            # NOTE: Type 02 and Veneer 01 shelves have
            # additional 10 mm
            # honeycomb bumpers in packs with horizontals
            if self.shelf_type in {1, 2} and any(
                e.ELEM_TYPE == 'H' for e in pack.all_elements
            ):
                k1 = '20'
                shape = 'P300DW2'
                # kolejność:  1; 2;  3;  4;  5; 6; 7; 8;9;0;1;2;3;14;  15;6; 17;8;9;20
            txt.append(
                '{0}-{1};{2};{3};{4};{7};0;{5};*;0;*;*;*;*;{8};{6};*;{6};0;1;1;'.format(
                    self.id_production,  # 0 - np. 10707 - id_production
                    pack.pack_id,  # 1 - np. 1 - numer paczki
                    a,  # 2 - np. 320 - Większy wymiar poziomy paczki
                    b,  # 3 - np. 260 - Mniejszy wymiar poziomy paczki
                    c,  # 4 - np. 486 - wysokość paczki
                    'ETICH#D:\\AUTOIMPORT\\LABEL\\{pack_filename}#0#0#'.format(
                        pack_filename=pack.pack_label_file_name
                    ),
                    # 6 - Jezyk ograniczony wyększym wymiarem poziomym lub wysokością
                    k,
                    shape,  # 7 - wykrój
                    k1,  # 8 - przestrzeń na tekturę na końcach
                )
            )
        return txt

    def get_max_fill_amount(self):
        """
        dimensions in tm
        :return:
        """
        doors_count = 0
        drawers_count = 0
        drawers_max_height = 157800
        doors_breakpoint = 50000
        for c in self.compartments:
            # if comp width more than 260 mm and is not a doouble opening
            if c['x_domain'][1] - c['x_domain'][0] > 26000 and c['y_domain'][1] - c[
                'y_domain'
            ][0] in [19000, 26000, 38000]:
                # if comp height is one of two regular heights
                if c['y_domain'][1] - c['y_domain'][0] in [26000, 38000]:
                    if c['y_domain'][1] <= drawers_max_height:
                        if c['x_domain'][1] - c['x_domain'][0] > doors_breakpoint:
                            doors_count += 2
                        else:
                            doors_count += 1
                        drawers_count += 1
                    else:
                        if c['x_domain'][1] - c['x_domain'][0] > doors_breakpoint:
                            doors_count += 2
                        else:
                            doors_count += 1
                else:  # drawers are possible in smallest row height
                    if c['y_domain'][0] <= drawers_max_height:
                        drawers_count += 1

        return dict(
            max_number_of_doors=doors_count, max_number_of_drawers=drawers_count
        )
