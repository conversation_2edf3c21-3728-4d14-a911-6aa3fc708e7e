import string


class Code39(object):
    """Initializes a new Code39 instance."""

    EDGE = '100010111011101'
    CODES = (
        '101000111011101',
        '111010001010111',
        '101110001010111',
        '111011100010101',
        '101000111010111',
        '111010001110101',
        '101110001110101',
        '101000101110111',
        '111010001011101',
        '101110001011101',
        '111010100010111',
        '101110100010111',
        '111011101000101',
        '101011100010111',
        '111010111000101',
        '101110111000101',
        '101010001110111',
        '111010100011101',
        '101110100011101',
        '101011100011101',
        '111010101000111',
        '101110101000111',
        '111011101010001',
        '101011101000111',
        '111010111010001',
        '101110111010001',
        '101010111000111',
        '111010101110001',
        '101110101110001',
        '101011101110001',
        '111000101010111',
        '100011101010111',
        '111000111010101',
        '100010111010111',
        '111000101110101',
        '100011101110101',
        '100010101110111',
        '111000101011101',
        '100011101011101',
        '100010001000101',
        '100010001010001',
        '100010100010001',
        '101000100010001',
    )
    REF = (
        tuple(string.digits)
        + tuple(string.ascii_uppercase)
        + ('-', '.', ' ', '$', '/', '+', '%')
    )
    # MAP for assigning every symbol (REF) to (reference number, barcode)
    MAP = dict(list(zip(REF, enumerate(CODES))))

    def __init__(self, code, movex=0, movey=0, sizex=100, sizey=20):
        self.code = code.upper()  # Zmienia wszystkie litery na litery drukowane
        # Ustawienia paskow
        self.background = 'white'
        self.foreground = 'black'
        self.quiet_zone = None  # Pusta przerwa
        self.module_width = None
        self.set_size(sizex, code)
        # Ustalenie wspolrzednych
        self.movex = movex
        self.movey = movey
        self.sizey = sizey

    def set_size(self, sizex, code):
        """Oblicza szerokosc jednego paska na podstawie podanych danych"""
        ilosc_modulow = len(code) * 15  # Ilosc znakow * 15 modulow na litere
        ilosc_modulow += len(code) + 1  # Moduly przystankowe pomiedzy kazdym znakiem
        ilosc_modulow += len(self.EDGE) * 2  # Znaki poczatku i konca
        ilosc_modulow += 10  # Po piec na kazda quiet_zone
        self.module_width = float(sizex) / ilosc_modulow
        self.quiet_zone = 5 * self.module_width

    def build(self):
        """Buduje kod binarny w systemie 39 dla podanego ciagu znakow"""
        chars = [self.EDGE]  # Dodaj krawedz (poczatek)
        for char in self.code:  # Dla kazdego znaku dodaj kod binarny:
            chars.append(self.MAP[char][1])
        chars.append(self.EDGE)  # Dodaj krawedz (koniec)
        return '0'.join(chars)  # Polacz w jeden string ze znakiem lacznika

    def get_barcode_dict(self):
        code = self.build()
        # Slownik EXPORT
        barcode = []
        data = {'group_data': {'name': 'BARCODE'}, 'rect': barcode}

        ypos = self.movey
        xpos = self.movex
        if self.quiet_zone != 0:
            barcode.append(
                {
                    'insert': (xpos, ypos),
                    'size': (self.quiet_zone, self.sizey),
                    'fill': self.background,
                }
            )
            xpos += self.quiet_zone

        for mod in code:
            color = self.background if mod == '0' else self.foreground
            barcode.append(
                {
                    'insert': (xpos, self.movey),
                    'size': (self.module_width * 1.2, self.sizey),
                    'fill': color,
                }
            )
            xpos += self.module_width

        # Add right quiet zone to every line
        if self.quiet_zone != 0:
            barcode.append(
                {
                    'insert': (xpos, ypos),
                    'size': (self.quiet_zone, self.sizey),
                    'fill': self.background,
                }
            )
        return data
