from datetime import datetime

from django.contrib.auth.models import User

import pytest

from freezegun import freeze_time

from orders.enums import OrderStatus
from orders.models import Order
from orders.tests.factories import NO_CART
from user_profile.choices import UserType
from user_profile.models import UserProfile
from user_profile.services.user_profile_cleaner import UserProfileCleaner


@pytest.mark.django_db
class TestUserProfileCleaner:
    @pytest.fixture()
    def old_empty_order(self, order_factory):
        with freeze_time(datetime(2019, 7, 1)):
            return order_factory(
                items=None,
                status=OrderStatus.CART,
                owner__email='',
                owner__profile__user_type=UserType.GUEST_CUSTOMER,
                cart=NO_CART,
            )

    @pytest.fixture()
    def old_not_empty_order(self, order_factory):
        with freeze_time(datetime(2019, 7, 1)):
            return order_factory(status=OrderStatus.CART)

    @pytest.fixture()
    def new_empty_order(self, order_factory):
        return order_factory(
            items=None,
            status=OrderStatus.CART,
            owner__email='',
            owner__profile__user_type=UserType.GUEST_CUSTOMER,
        )

    @pytest.fixture()
    def new_not_empty_order(self, order_factory):
        return order_factory(status=OrderStatus.CART)

    @pytest.fixture()
    def user_with_two_orders(self, order_factory):
        order1 = order_factory(status=OrderStatus.CART)
        with freeze_time(datetime(2019, 7, 1)):
            order_factory(owner=order1.owner, items=None, status=OrderStatus.CART)

    def test_separate_orders(
        self,
        old_empty_order,
        old_not_empty_order,
        new_empty_order,
        new_not_empty_order,
    ):
        order_count_before = Order.objects.count()
        user_count_before = User.objects.count()
        user_profile_count_before = UserProfile.objects.count()
        cleaner = UserProfileCleaner()
        cleaner()
        order_count_after = Order.objects.count()
        user_count_after = User.objects.count()
        user_profile_count_after = UserProfile.objects.count()
        assert order_count_before - order_count_after == 1
        assert user_count_before - user_count_after == 1
        assert user_profile_count_before - user_profile_count_after == 1

    def test_one_user_two_orders(self, user_with_two_orders):
        order_count_before = Order.objects.count()
        user_count_before = User.objects.count()
        user_profile_count_before = UserProfile.objects.count()
        cleaner = UserProfileCleaner()
        cleaner()
        order_count_after = Order.objects.count()
        user_count_after = User.objects.count()
        user_profile_count_after = UserProfile.objects.count()
        assert order_count_before - order_count_after == 1
        assert user_count_before == user_count_after
        assert user_profile_count_before == user_profile_count_after
