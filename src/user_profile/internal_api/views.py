import logging

from django.contrib.auth import get_user_model

from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView

from custom import permissions
from user_profile.models import (
    LoginAccessToken,
    LoginAccessTokenEmail24,
)

User = get_user_model()

logger = logging.getLogger('cstm')


class LoginAccessTokenCreateForUserView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        user_id = request.data['user_id']
        user = User.objects.get(id=user_id)
        login_access_token = LoginAccessToken.create_for_user(user)
        return Response(
            data={'login_access_token': login_access_token},
            status=status.HTTP_201_CREATED,
        )


class LoginAccessTokenEmail24CreateForUserView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    def post(self, request, *args, **kwargs):
        user_id = request.data['user_id']
        user = User.objects.get(id=user_id)
        login_access_token = LoginAccessTokenEmail24.create_for_user(user)
        return Response(
            data={'login_access_token': login_access_token},
            status=status.HTTP_201_CREATED,
        )
