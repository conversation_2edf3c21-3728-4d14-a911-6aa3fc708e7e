from django.contrib.admin.sites import AdminSite

import pytest

from rating_tool.admin import BoardAdmin
from rating_tool.models import Board


@pytest.mark.django_db
class TestBoardAdmin:
    admin = BoardAdmin(Board, AdminSite())

    @pytest.mark.parametrize(
        'furniture_category, furniture_type, features, additional_descr, result',
        [
            ('cat_all', 'type_all', '', '', 'cat_all__type_all'),
            ('cat_1', 'type_2', 'f_doors', 'var_1', 'cat_1__f_doors__type_2__var_1'),
        ],
    )
    def test_get_filter_descr(
        self,
        furniture_category,
        furniture_type,
        features,
        additional_descr,
        result,
    ):
        filter_descr = self.admin._get_filter_descr(
            furniture_category,
            furniture_type,
            features,
            additional_descr,
        )
        assert filter_descr == result
