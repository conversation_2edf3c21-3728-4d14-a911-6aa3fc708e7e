# EMAILS
from cstm_be.settings.base import env

EMAIL_BACKEND = 'django_mailer.smtp_queue.EmailBackend'
EMAIL_HOST = env.str('DJANGO_EMAIL_HOST', default='localhost')
EMAIL_PORT = env.int('DJANGO_EMAIL_PORT', default=587)
EMAIL_HOST_USER = env.str('DJANGO_EMAIL_HOST_USER', default='<EMAIL>')
EMAIL_SUBJECT_PREFIX = env.str(
    'DJANGO_EMAIL_SUBJECT_PREFIX',
    default='[DJANGO] ',
)
EMAIL_SPECIAL_PREFIX = env.str('DJANGO_EMAIL_SPECIAL_PREFIX', default='')
DEFAULT_FROM_EMAIL = env.str(
    'DJANGO_DEFAULT_FROM_EMAIL',
    default='<EMAIL>',
)

# MAILING LISTS DEFAULTS
SERVER_EMAIL = '<EMAIL>'
CUSTOMER_SERVICE_TEAM_LEADER_EMAIL_ALIAS = '<EMAIL>'

ADMINS = []
MANAGERS = []

ACCOUNTING_RECIPIENTS = []
INVOICE_BACKUP_RECIPIENTS = []
LOGISTIC_RECIPIENTS = []
LOGISTIC_RECIPIENTS_DAMAGE_FORM = []
LOGISTIC_RECIPIENTS_MANAGER = []
MANDRILL_REPORT_SENDER_LIST = []
MANUFACTURERS_RECIPIENTS = {
    0: (('UNKNOWN', '<EMAIL>'),),  # UNKNOWN
    1: (('DREWTUR', '<EMAIL>'),),  # DREWTUR
    24: (('MEBLE_PL', '<EMAIL>'),),  # MEBLE_PL
    25: (('NOVUM', '<EMAIL>'),),  # NOVUM
    29: (('STUDIO_93', '<EMAIL>'),),  # STUDIO_93
    30: (('AMIR', '<EMAIL>'),),  # AMIR
    31: (('INEX', '<EMAIL>'),),  # INEX
    33: (('CENTER_MEBEL', '<EMAIL>'),),  # CENTER_MEBEL
}
MEBLEPL_FTP_ISSUE_CONTACTS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
]
ORDER_REPORT_RECIPIENTS = []
PAYMENT_FAILED_RECIPIENTS = []
PAYMENT_CHARGEBACK_RECIPIENTS = []
PRODUCTION_RECIPIENTS = []
PRODUCTION_RECIPIENTS_ON_ABORT = (('Test recipient', '<EMAIL>'),)
SHELF_MARKET_REPORT_CREATED_RECIPIENTS = []
STOCKS_RECIPIENTS = []
QUALITY_CONTROL_EMAILS = []

# MAILCHIMP
MAILCHIMP_API_KEY = env.str('MAILCHIMP_API_KEY', default='')

MAILCHIMP_EMAIL_PROVIDER_SETTINGS = {
    'host': EMAIL_HOST,
    'port': EMAIL_PORT,
    'username': EMAIL_HOST_USER,
    'password': env.str('DJANGO_EMAIL_HOST_PASSWORD', default=''),
}

# BRAZE
BRAZE_COLLECT_EVENTS = env.bool('BRAZE_COLLECT_EVENTS', False)
BRAZE_API_KEY = env.str('BRAZE_API_KEY', default='')
BRAZE_API_URL = env.str('BRAZE_API_URL', default='https://rest.fra-01.braze.eu/')
BRAZE_APP_ID = env.str('BRAZE_APP_ID', default='')
BRAZE_MAX_EVENT_RETRIES = env.int('BRAZE_MAX_EVENT_RETRIES', default=5)
BRAZE_EMAIL_SUBSCRIPTION_GROUPS = {
    'newsletter': env.str('BRAZE_SUBSCRIPTION_NEWSLETTER', default=''),
    'save_for_later': env.str('BRAZE_SUBSCRIPTION_SAVE_FOR_LATER', default=''),
}

BRAZE_CANVASES = {
    'assembly_service_notification': (
        env.str('BRAZE_CANVAS_ASSEMBLY_SERVICE_NOTIFICATION', default='')
    ),
    'complaint_service_notification': (
        env.str('BRAZE_CANVAS_COMPLAINT_SERVICE_NOTIFICATION', default='')
    ),
    'logistic_date_change_notification': (
        env.str('BRAZE_CANVAS_LOGISTIC_DATE_CHANGE_NOTIFICATION', default='')
    ),
    'logistic_new_dates_notification': (
        env.str('BRAZE_CANVAS_LOGISTIC_NEW_DATES_NOTIFICATION', default='')
    ),
    'email24_logistic_notification': (
        env.str('BRAZE_CANVAS_EMAIL24_LOGISTIC_CONFIRMATION', default='')
    ),
}

# MAILING
MAILING_FLOW_PERIOD = env.int('MAILING_FLOW_PERIOD', default=2)
MAILING_FLOW_THRESHOLD = env.int('MAILING_FLOW_THRESHOLD', default=8000)

MANDRILL_API_URL = 'https://mandrillapp.com/api/1.0'
MANDRILL_REPORT_CHECK_DAYS_BACK = env.int(
    'MANDRILL_REPORT_CHECK_DAYS_BACK',
    default=30,
)
BQ_MAILING_REPORT_DATASET = 'mandrill'
BQ_MAILING_REPORT_TABLE = 'mailing_report'
BQ_MAILING_REJECTS_REPORT_TABLE = 'rejects_report'
BQ_MAILING_TAG_REPORT_TABLE = 'tags_report'

# FLOWS
PROCESS_MARKETING_FLOWS_ON_BE = env.bool('PROCESS_MARKETING_FLOWS_ON_BE', default=True)
PROCESS_LOGISTIC_FLOWS_ON_BE = env.bool('PROCESS_LOGISTIC_FLOWS_ON_BE', default=True)
PROCESS_TRANSACT_FLOWS_ON_BE = env.bool('PROCESS_TRANSACT_FLOWS_ON_BE', default=True)
BRAZE_TRANSACT_FLOW_SWITCHING_DATE = env.str(
    'BRAZE_TRANSACT_FLOW_SWITCHING_TIME',
    default='',
)
