from datetime import datetime
from unittest.mock import patch

import pytest

from orders.services.nps_generator import (
    get_month_date_range,
    get_nps_for_constant_periods,
)


@pytest.mark.parametrize(
    'year, month, expected_start, expected_end',
    [
        (2024, 1, datetime(2024, 1, 1), datetime(2024, 1, 31)),
        (2024, 2, datetime(2024, 2, 1), datetime(2024, 2, 29)),  # Leap year
        (2023, 2, datetime(2023, 2, 1), datetime(2023, 2, 28)),  # Non-leap year
        (2024, 6, datetime(2024, 6, 1), datetime(2024, 6, 30)),  # Normal case
        (2024, 12, datetime(2024, 12, 1), datetime(2024, 12, 31)),  # End of year
    ],
)
def test_get_month_date_range(year, month, expected_start, expected_end):
    start_date, end_date = get_month_date_range(year, month)
    assert start_date == expected_start
    assert end_date == expected_end


@patch('orders.services.nps_generator.import_nps_data_for_period')
@patch('django.utils.timezone.now')
@pytest.mark.parametrize(
    'now_datetime, expected_calls',
    [
        (
            datetime(2025, 3, 15),
            [
                (
                    datetime(2025, 2, 1),
                    datetime(2025, 2, 28),
                ),  # Previous month this year
                (
                    datetime(2024, 2, 1),
                    datetime(2024, 2, 29),
                ),  # Previous month last year (leap)
                (datetime(2023, 2, 1), datetime(2023, 2, 28)),  # Two years ago
                (datetime(2021, 2, 1), datetime(2021, 2, 28)),  # Four years ago
            ],
        ),
        (
            datetime(2025, 1, 10),
            [
                (datetime(2024, 12, 1), datetime(2024, 12, 31)),  # December last year
                (datetime(2023, 12, 1), datetime(2023, 12, 31)),  # One year ago
                (datetime(2022, 12, 1), datetime(2022, 12, 31)),  # Two years ago
                (datetime(2020, 12, 1), datetime(2020, 12, 31)),  # Four years ago
            ],
        ),
    ],
)
def test_get_nps_for_constant_periods(
    mock_now, mock_import_nps, now_datetime, expected_calls
):
    mock_now.return_value = now_datetime

    get_nps_for_constant_periods('<EMAIL>')

    assert mock_import_nps.call_count == 4
    for call, (start_date, end_date) in zip(
        mock_import_nps.call_args_list, expected_calls
    ):
        args = call.args
        assert args[0] == '<EMAIL>'
        assert args[1] == start_date
        assert args[2] == end_date
