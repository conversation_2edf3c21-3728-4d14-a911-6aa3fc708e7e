import functools
import logging
import operator

from datetime import timedelta
from typing import (
    TYPE_CHECKING,
    Optional,
    Union,
)

from django.db.models import Q

from custom.internal_api.enums import AssemblyTypeChoices
from mailing.models import Customer
from orders.exceptions import InvalidOrderStatusChangeError
from orders.order_notes import append_order_note
from user_profile.validators import BusinessEmailValidator

if TYPE_CHECKING:
    from carts.models import CartItem
    from orders.models import OrderItem
    from regions.models import Region
    from vouchers.models import VoucherGroup


logger = logging.getLogger('orders')


def log_unexpected_status_change(order, status_to_change):
    """We want to have traceback in sentry."""
    try:
        raise InvalidOrderStatusChangeError('Unexpected status change')
    except InvalidOrderStatusChangeError:
        logger.exception(
            add_order_context(
                'from={} to={}'.format(order.status, status_to_change),
                order=order,
            ),
            stack_info=True,
            extra={'order_id': order.id},
        )


def add_order_context(message, order):
    return f'[order_id:{order.id}] {message}'


def sanitise_postal_code(postal_code):
    return postal_code.replace(' ', '').upper()


def get_voucher_group_for_region(
    promo_text: str, region: 'Region'
) -> Optional['VoucherGroup']:
    from vouchers.models import VoucherGroup

    voucher_groups = (
        VoucherGroup.objects.filter(code__iexact=promo_text)
        .prefetch_related(
            'voucher_set',
            'voucher_set__discounts',
        )
        .all()
    )
    if not voucher_groups:
        return None
    return voucher_groups.filter(region=region).first() or voucher_groups.first()


def has_free_assembly_service(item: Union['OrderItem', 'CartItem']) -> bool:
    """Determine if given order item has free assembly service."""
    return item.sellable_item.is_t03_wardrobe


def check_if_customer_is_returning_and_create(order):
    if Customer.can_be_cs_customer(order):
        customer, created = Customer.objects.update_or_create_customer_and_set_type(
            order=order,
        )
        return True, customer
    return False, None


def is_business_order_or_email(order):
    order_emails = [order.email, order.owner.email, order.owner.profile.email]
    email_validator = BusinessEmailValidator()

    return order.is_business_order() or any(
        [email_validator(email) for email in order_emails]
    )


def find_next_tuesday(date):
    return date + timedelta((1 - date.weekday()) % 7)


def build_or_query_nullable_excluded(kwargs):
    q_objects = [
        Q(**{field_name: value}) for field_name, value in kwargs.items() if value
    ]
    return functools.reduce(operator.or_, q_objects)


def get_assembly_type(order, order_item):
    if getattr(order_item, 'is_t03_wardrobe', False):
        return AssemblyTypeChoices.ASSEMBLY_INCLUDED

    if order.assembly:
        return AssemblyTypeChoices.ASSEMBLY_PAID


def update_notes(notes_by_order_id):
    updated_orders = []
    for item in notes_by_order_id:
        order, note = item['order_id'], item['note']
        append_order_note(order, note)
        updated_orders.append(order)
    return updated_orders
