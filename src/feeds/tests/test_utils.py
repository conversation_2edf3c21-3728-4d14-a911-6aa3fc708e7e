import pytest

from feeds.utils import truncate_sentence


@pytest.mark.parametrize(
    'input_sentence, max_characters, expected_output',
    [
        ('This is a test sentence', 10, 'This is a'),
        ('This is a test sentence', 25, 'This is a test sentence'),
        ('', 10, ''),
        ('Short', 10, 'Short'),
        ('Short sentence here', 6, 'Short'),
        ('Several words here', 7, 'Several'),
        ('Several words here', 8, 'Several'),
    ],
)
def test_truncate_sentence(input_sentence, max_characters, expected_output):
    assert truncate_sentence(input_sentence, max_characters) == expected_output
