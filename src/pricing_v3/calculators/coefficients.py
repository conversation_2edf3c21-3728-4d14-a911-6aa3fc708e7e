from decimal import Decimal

from .jetty.coefficients import get_coefficients as jetty_get_coefficients
from .s01.coefficients import get_coefficients as s01_get_coefficients
from .t03.coefficients import get_coefficients as t03_get_coefficients
from .t13.coefficients import get_coefficients as t13_get_coefficients
from .t13_veneer.coefficients import get_coefficients as t13_veneer_get_coefficients
from .t23.coefficients import get_coefficients as t23_get_coefficients
from .t24.coefficients import get_coefficients as t24_get_coefficients
from .t25.coefficients import get_coefficients as t25_get_coefficients


def get_coefficients(region_name: str | None) -> dict[str, Decimal]:
    return (
        jetty_get_coefficients(region_name)
        | t13_get_coefficients(region_name)
        | t13_veneer_get_coefficients(region_name)
        | t03_get_coefficients(region_name)
        | t23_get_coefficients(region_name)
        | t24_get_coefficients(region_name)
        | t25_get_coefficients(region_name)
        | s01_get_coefficients(region_name)
    )
