from decimal import Decimal

from custom.enums import (
    ColorEnum,
    ShelfType,
    Type02Color,
)


def calculate_shelf_type_price(
    price_sum: Decimal,
    shelf_type: ShelfType,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on the shelf type.
    Always 0 for Type01 (the default type).
    Always a negative value for Type02 (cheaper than T01).
    Always a positive value for Type01Veneer (more expensive than T01).
    """
    if shelf_type == ShelfType.TYPE02:
        return price_sum * price_coefficients['t02_factor']
    if shelf_type == ShelfType.VENEER_TYPE01:
        return price_sum * price_coefficients['t01v_factor']
    return Decimal('0')


def calculate_material_price(
    price_sum: Decimal,
    shelf_type: ShelfType,
    material: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on non-standard colors.
    Almost always 0, the only exception is T02 Midnight Blue.
    """
    if shelf_type == ShelfType.TYPE02 and material in {
        Type02Color.MIDNIGHT_BLUE,
        Type02Color.SKY_BLUE,
    }:
        return price_sum * price_coefficients['midnight_blue_factor']
    elif shelf_type == ShelfType.TYPE02 and material == Type02Color.MATTE_BLACK:
        return price_sum * price_coefficients['black_matte_factor']
    elif shelf_type == ShelfType.TYPE02 and material == Type02Color.COTTON:
        return price_sum * price_coefficients['cotton_factor']
    return Decimal('0')


def calculate_depth_price(
    price_sum: Decimal, depth: int, price_coefficients: dict[str, Decimal]
) -> Decimal:
    """Additional price increase for shelves by depth.

    We assume anything between 320 and 400 is depth == 400.
    We assume anything larger than 400 is depth == 500.
    """
    if depth > 320 and depth <= 400:
        return price_coefficients['depth_factor'] * price_sum
    if depth > 400:
        return price_coefficients['depth_factor_500'] * price_sum
    return Decimal('0')


def calculate_watty_material_price(
    price_sum: Decimal,
    color: ColorEnum,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    if color.is_multicolor:
        return price_sum * price_coefficients['duotone_factor']
    return Decimal('0')


def calculate_watty_depth_price(
    price_sum: Decimal,
    depth: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    depth_m = Decimal(depth) / Decimal(1000)
    first_standard_depth = Decimal('0.53')
    second_standard_depth = Decimal('0.63')
    base_depth_price = (depth_m - first_standard_depth) * price_coefficients[
        'watty_base_depth_factor'
    ]
    if depth_m < first_standard_depth:
        depth_price = (
            base_depth_price
            + (Decimal('0.52') - depth_m) * price_coefficients['watty_depth_400_factor']
        )
    elif depth_m <= second_standard_depth:
        depth_price = base_depth_price
    else:
        depth_price = base_depth_price + (
            (depth_m - second_standard_depth)
            * price_coefficients['watty_depth_800_factor']
        )
    return depth_price * price_sum


def calculate_category_price(
    price_sum: Decimal,
    category: str,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on the shelf category."""
    return price_sum * price_coefficients[f'category_{category}_factor']
