from pricing_v3.calculators.jetty import calculate_price as jetty_calculator
from pricing_v3.calculators.t03 import calculate_price as t03_calculator
from pricing_v3.calculators.t13 import calculate_price as t13_calculator
from pricing_v3.calculators.t13_veneer import calculate_price as f13_calculator
from pricing_v3.calculators.t23 import calculate_price as t23_calculator
from pricing_v3.calculators.t24 import calculate_price as t24_calculator
from pricing_v3.calculators.t25 import calculate_price as t25_calculator
from pricing_v3.history.unified_interface import UnifiedPricingInterface


class PricingCalculator(UnifiedPricingInterface):
    def jetty_calculator(self, geometry):
        return jetty_calculator(geometry, self.region_name, self.coefficients)

    def t03_calculator(self, geometry):
        return t03_calculator(geometry, self.region_name, self.coefficients)

    def t13_calculator(self, geometry):
        return t13_calculator(geometry, self.region_name, self.coefficients)

    def f13_calculator(self, geometry):
        return f13_calculator(geometry, self.region_name, self.coefficients)

    def t23_calculator(self, geometry):
        return t23_calculator(geometry, self.region_name, self.coefficients)

    def t24_calculator(self, geometry):
        return t24_calculator(geometry, self.region_name, self.coefficients)

    def t25_calculator(self, geometry):
        return t25_calculator(geometry, self.region_name, self.coefficients)
