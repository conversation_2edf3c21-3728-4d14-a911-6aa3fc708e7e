import typing

from decimal import Decimal

from custom.enums import ShelfType

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_value_based_increase(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    all_vbps = {
        'width_300': _calculate_width_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.width,
            price_coefficients,
        ),
        'height_250': _calculate_height_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.height,
            price_coefficients,
        ),
        'depth_400': _calculate_depth_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.depth,
            price_coefficients,
        ),
    }
    # If the shelf is wide, high and deep, it would have a 50% price increase
    # - let's prohibit that and only take the biggest value based increase.
    corrected_vbps = {key: 0 for key in all_vbps.keys()}
    max_key = max(all_vbps, key=all_vbps.get)
    corrected_vbps[max_key] = all_vbps[max_key]
    return corrected_vbps


def _calculate_width_value_increase(
    price_sum: Decimal,
    shelf_type: int,
    width_mm: int,
    price_coefficients: typing.Dict[str, Decimal],
) -> Decimal:
    """T01 & T02 wider than 300cm has an increase.
    In order to avoid a price jump at 300cm, the increase is linear -
        starting at 295cm, reaching the max increase at 310cm.
    """

    increase_start = 2950
    increase_end = 3100

    if shelf_type in {ShelfType.TYPE01, ShelfType.TYPE02}:
        increase_factor = Decimal(
            (width_mm - increase_start) / (increase_end - increase_start)
        )
        increase_factor = max(min(increase_factor, Decimal('1')), Decimal('0'))
        return increase_factor * price_coefficients['width_300'] * price_sum
    return Decimal('0')


def _calculate_height_value_increase(
    price_sum: Decimal,
    shelf_type: int,
    height_mm: int,
    price_coefficients: typing.Dict[str, Decimal],
) -> Decimal:
    """T01 & T02 TREX higher or equal 250cm has an increase."""
    if shelf_type in {ShelfType.TYPE01, ShelfType.TYPE02} and height_mm >= 2500:
        return price_coefficients['height_250'] * price_sum
    return Decimal('0')


def _calculate_depth_value_increase(
    price_sum: Decimal,
    shelf_type: int,
    depth_mm: int,
    price_coefficients: typing.Dict[str, Decimal],
):
    """T01 & T02 TREX with depth 40cm has an increase."""
    if shelf_type in {ShelfType.TYPE01, ShelfType.TYPE02} and depth_mm == 400:
        return price_coefficients['depth_400'] * price_sum
    if shelf_type in {ShelfType.TYPE01, ShelfType.TYPE02} and depth_mm == 500:
        return price_coefficients['depth_500'] * price_sum
    return Decimal('0')
