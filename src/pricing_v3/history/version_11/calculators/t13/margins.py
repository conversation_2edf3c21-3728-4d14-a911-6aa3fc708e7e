import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_margins(
    watty: 'Watty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    category = watty.furniture_category or 'wardrobe'
    return {
        'margin_base': price_sum * price_coefficients[f'type13_margin_{category}'],
    }
