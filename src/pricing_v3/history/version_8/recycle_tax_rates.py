import bisect

from collections.abc import Sequence

# First value is weight range, second is tax value in EUR
FRANCE_RECYCLE_TAX_RATES = [
    (0.5, 0.02),
    (1, 0.06),
    (2, 0.1),
    (5, 0.18),
    (10, 0.47),
    (20, 1.08),
    (30, 1.67),
    (40, 2.33),
    (60, 3.08),
    (100, 5.17),
    (150, 9.17),
    (200, 11.67),
    (250, 15),
    (300, 20),
    (400, 25),
    (500, 31.25),
    (600, 37.5),
    (700, 43.75),
    (800, 50),
    (900, 56.25),
    (1000, 62.5),
    (1100, 68.75),
    (1200, 75),
    (1300, 81.25),
    (1400, 87.5),
    (1500, 93.75),
    (1600, 100),
    (1700, 106.25),
    (1800, 112.5),
    (1900, 118.75),
    (2000, 125),
    (2100, 131.25),
]


class KeyWrapper(Sequence):
    """
    Abstraction to help bisect iterable by key.

    Might be removed for Python 3.10.
    """

    def __init__(self, iterable, key):
        self.iterable = iterable
        self.key = key

    def __getitem__(self, i):
        return self.key(self.iterable[i])

    def __len__(self):
        return len(self.iterable)


def get_recycle_tax_value(weight):
    rate_index = bisect.bisect_left(
        KeyWrapper(
            FRANCE_RECYCLE_TAX_RATES,
            key=lambda x: x[0],
        ),
        weight,
    )
    return FRANCE_RECYCLE_TAX_RATES[rate_index][1]
