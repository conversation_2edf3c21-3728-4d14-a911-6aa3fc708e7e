import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_base_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like frame."""
    width_m = Decimal(watty.width) / 1000
    return {
        'base': price_coefficients['type13_veneer_base_unit']
        + (width_m * price_coefficients['type13_veneer_base_area'])
    }


def calculate_logistics_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    width_m = Decimal(watty.width) / 1000
    height_m = Decimal(watty.height) / 1000
    depth_m = Decimal(watty.depth) / 1000
    volume = width_m * height_m * depth_m
    drawers_count = len(watty.drawers)
    return {
        'logistic': (
            volume * price_coefficients['type13_veneer_logs_vol']
            + (drawers_count * price_coefficients['type13_veneer_logs_drawers'])
            + price_coefficients['type13_veneer_logs_base']
        )
    }


def calculate_additional_increase(
    price: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'regional_increase': price
        * price_coefficients['type_13_veneer_additional_increase']
    }
