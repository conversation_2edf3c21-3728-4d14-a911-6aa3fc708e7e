from decimal import Decimal

BUCKET_ROUNDING_REGIONS = {'united_kingdom'}


def round_price_to_bucket(price: Decimal) -> int:
    return min(bucket for bucket in buckets() if bucket >= price)


def buckets() -> list[int]:
    buckets_list = []
    # no rounding for prices < 100 (for now)
    for one in range(0, 100):
        buckets_list.append(one)
    for hundred in range(100, 500, 100):
        buckets_list.extend([hundred + x for x in {29, 49, 79, 99}])
    for hundred in range(500, 1000, 100):
        buckets_list.extend([hundred + x for x in {49, 79, 99}])
    for hundreds in range(1000, 10000, 100):
        buckets_list.append(hundreds + 99)
    for thousand in range(10000, 100000, 1000):
        buckets_list.append(thousand + 999)
    for ten_thousands in range(100000, 1000000, 10000):
        buckets_list.append(ten_thousands + 9999)
    return sorted(buckets_list)
