# Generated by Django 3.2.16 on 2023-01-26 12:46
from datetime import datetime

from django.db import migrations

from pricing_v3.calculators.coefficients import (
    get_coefficients as get_coefficients_current,
)
from pricing_v3.history.version_1.coefficients import (
    get_coefficients as get_coefficients_legacy,
)

REGIONS = {'switzerland', 'germany', 'united_kingdom'}
VERSION_1_DATETIME = datetime(2022, 9, 13, 10, 26)
CURRENT_VERSION = datetime(2023, 1, 10, 10, 26)


def create_legacy_pricing_version(apps, schema_editor):
    PricingVersion = apps.get_model('pricing_v3', 'PricingVersion')
    Region = apps.get_model('regions', 'Region')
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    for region in Region.objects.filter(name__in=REGIONS):
        pv = PricingVersion.objects.create(
            coefficients=get_coefficients_legacy(region), region=region
        )
        pv.created_at = VERSION_1_DATETIME
        pv.save()
    pv = PricingVersion.objects.create(
        coefficients=get_coefficients_legacy(region_name=None)
    )
    pv.created_at = VERSION_1_DATETIME
    pv.save()
    PricingHistoryEntry.objects.create(
        start_date=datetime(2022, 9, 13),
        end_date=datetime(2023, 1, 9),
        calculator_version='version_1.calculator',
    )


def create_current_pricing_version(apps, schema_editor):
    PricingVersion = apps.get_model('pricing_v3', 'PricingVersion')
    Region = apps.get_model('regions', 'Region')
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    for region in Region.objects.filter(
        name__in={
            'switzerland',
            'germany',
            'united_kingdom',
        }
    ):
        pv = PricingVersion.objects.create(
            coefficients=get_coefficients_current(region), region=region
        )
        pv.created_at = CURRENT_VERSION
        pv.save()
    pv = PricingVersion.objects.create(
        coefficients=get_coefficients_current(region_name=None)
    )
    pv.created_at = CURRENT_VERSION
    pv.save()

    PricingHistoryEntry.objects.create(
        start_date=datetime(2023, 1, 10), end_date=None, calculator_version='current'
    )


def delete_legacy_pricing_history(apps, schema_editor):
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    try:
        PricingHistoryEntry.objects.get(
            start_date=datetime(2022, 9, 13),
            end_date=datetime(2023, 1, 9),
        ).delete()
    except PricingHistoryEntry.DoesNotExist:
        pass


def delete_current_pricing_history(apps, schema_editor):
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    try:
        PricingHistoryEntry.objects.get(
            start_date=datetime(2023, 1, 10),
        ).delete()
    except PricingHistoryEntry.DoesNotExist:
        pass


class Migration(migrations.Migration):

    dependencies = [
        ('pricing_v3', '0006_pricinghistoryentry'),
    ]

    operations = [
        migrations.RunPython(
            create_legacy_pricing_version,
            delete_legacy_pricing_history,
            elidable=True,
        ),
        migrations.RunPython(
            create_current_pricing_version,
            delete_current_pricing_history,
            elidable=True,
        ),
    ]
