from django.db import migrations


def unbind_region_default(apps, schema_editor):
    VoucherRegionEntry = apps.get_model('vouchers', 'VoucherRegionEntry')
    Region = apps.get_model('regions', 'Region')

    try:
        default_region = Region.objects.get(name='_default')
        VoucherRegionEntry.objects.filter(region=default_region).delete()
    except Region.DoesNotExist:
        # test migration, do nothing
        pass


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0044_rebind_voucher_group_default_region'),
    ]

    operations = [
        migrations.RunPython(
            unbind_region_default,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
