from typing import (
    TYPE_CHECKING,
    Optional,
    Union,
)

from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from orders.utils import get_voucher_group_for_region
from vouchers.enums import VoucherStatusMessages
from vouchers.models import Voucher

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


class VoucherService:
    def __init__(self, instance: Union['Order', 'Cart'], code: Optional[str]):
        self.code = code
        self.instance = instance
        self.amount_after_voucher = None

        self.region = instance.get_region()
        self.given_value = instance.get_total_price_number_before_discount()
        self.voucher_group = get_voucher_group_for_region(code, self.region)
        self.voucher = self._get_voucher()

    def process_voucher(self) -> dict[str, str]:
        if not self.voucher:
            return {
                'is_active': False,
                'status': VoucherStatusMessages.VOUCHER_CONDITIONS_ERROR,
                'message': f'There is no voucher with {self.code} code',
            }
        now = timezone.now()

        filter_query = Q(active=True, start_date__lte=now, end_date__gte=now)
        if self.region:
            filter_query &= Q(configs__enabled_regions__id=self.region.id)

        voucher_promotions = self.voucher.promotion_set.all()
        voucher_active_promotions = voucher_promotions.filter(filter_query)
        if voucher_promotions and not voucher_active_promotions.exists():
            return {
                'is_active': False,
                'status': VoucherStatusMessages.VOUCHER_CONDITIONS_ERROR,
                'message': f'Promotion {self.code} is not active',
            }

        self._apply_voucher()
        return {'is_active': True, 'status': VoucherStatusMessages.OK, 'message': _('Promo Code accepted.')}

    def _apply_voucher(self):
        from carts.services.cart_service import CartService

        cart_service = CartService(self.instance)
        cart_service.add_voucher(voucher=self.voucher, check_vat=True)

    def _get_voucher(self) -> Optional[Voucher]:
        if self.voucher_group is None:
            return self._get_default_voucher()
        for voucher_candidate in sorted(
            self.voucher_group.voucher_set.all(),
            key=lambda x: x.value,
            reverse=True,
        ):
            if self._check_if_voucher_applies(voucher_candidate):
                return voucher_candidate
        return self._get_default_voucher()

    def _get_default_voucher(self) -> Optional[Voucher]:
        voucher = (
            Voucher.objects.prefetch_related('discounts')
            .filter(code__iexact=self.code)
            .first()
        )
        if voucher and self._check_if_voucher_applies(voucher):
            return voucher

    def _check_if_voucher_applies(self, voucher: Voucher) -> bool:
        if not voucher.is_active():
            return False

        try:
            total_price_for_items = self.instance.calculate_total_price_for_items(
                items=voucher.get_promotion_affected_items(self.instance),
            )
        except AttributeError:
            return False

        if not total_price_for_items:
            return False

        if self.instance.is_order and self.instance.is_split():
            return self.instance.check_if_voucher_applies_for_split()

        region_voucher = voucher.get_region_entry(self.region)
        if (
            region_voucher.amount_starts
            <= total_price_for_items
            <= region_voucher.amount_limit
        ):
            return True

        return False
