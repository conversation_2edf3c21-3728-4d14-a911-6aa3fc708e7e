# Generated by Django 1.11.29 on 2021-03-22 15:49
from __future__ import unicode_literals

from django.db import (
    migrations,
    models,
)

from custom.enums import PhysicalProductVersion
from gallery.enums import ConfiguratorTypeEnum


def assign_digital_product_version_and_configurator(apps, schema_editor):
    Review = apps.get_model('reviews', 'Review')
    # RAPTORs and DIPLOs are S+, so should be GANDALFs
    Review.objects.filter(
        physical_product_version__in={
            PhysicalProductVersion.RAPTOR,
            PhysicalProductVersion.DIPLO,
        }
    ).update(
        digital_product_version=2,
        configurator_type=ConfiguratorTypeEnum.COLUMN,
    )
    Review.objects.filter(
        physical_product_version__in={PhysicalProductVersion.TREX}
    ).update(
        digital_product_version=1,
        configurator_type=ConfiguratorTypeEnum.ROW,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0007_enum_changes'),
    ]

    operations = [
        migrations.AddField(
            model_name='review',
            name='digital_product_version',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(1, 'BILBO'), (2, 'GANDALF')],
                help_text='BILBO - old shelves/shoeracks, GANDALF - crow/c+',
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='review',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(1, 'ROW'), (2, 'COLUMN'), (3, 'MIXED_ROW_COLUMN')],
                help_text=(
                    'MIXED - Wardrobe, '
                    'COLUMN - column configurator (i.e. for S+), '
                    'ROW - everything else,',
                ),
                null=True,
            ),
        ),
        migrations.RunPython(
            assign_digital_product_version_and_configurator,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
