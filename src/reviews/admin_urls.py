from django.contrib.admin import AdminSite
from django.urls import path

from reviews.views import (
    ReviewTagsApiView,
    ReviewToolView,
    ReviewToolViewSet,
)


class ReviewAdminPage(AdminSite):
    def get_review_admin_urls(self):
        return [
            path(
                'api/review_tag/',
                self.admin_view(ReviewTagsApiView.as_view()),
                name='review_tags_admin_api',
            ),
            path(
                'review_tool/',
                self.admin_view(ReviewToolView.as_view()),
                name='review_tool',
            ),
            path(
                'api/review/<int:pk>/',
                self.admin_view(
                    ReviewToolViewSet.as_view(
                        {
                            'get': 'retrieve',
                            'put': 'update',
                            'patch': 'partial_update',
                            'delete': 'destroy',
                        }
                    ),
                ),
                name='review_admin_api',
            ),
            path(
                'api/review/',
                self.admin_view(
                    ReviewToolViewSet.as_view(
                        {
                            'get': 'list',
                        }
                    ),
                ),
                name='review_admin_api',
            ),
        ]


urlpatterns = ReviewAdminPage().get_review_admin_urls()
