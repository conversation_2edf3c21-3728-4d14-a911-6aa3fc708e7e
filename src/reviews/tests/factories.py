import factory

from factory import fuzzy


class ReviewFactory(factory.django.DjangoModelFactory):
    name = factory.Faker('first_name')
    email = factory.Faker('email')
    country = factory.Faker('country')
    score = fuzzy.FuzzyInteger(0, 5)
    order = factory.SubFactory('orders.tests.factories.OrderFactory')

    class Meta:
        model = 'reviews.Review'


class ReviewPhotosFactory(factory.django.DjangoModelFactory):
    review = factory.SubFactory('reviews.tests.factories.ReviewFactory')
    image = factory.django.FileField(filename='image.png')

    class Meta:
        model = 'reviews.ReviewPhotos'


class ReviewScoreFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'reviews.ReviewScore'


class ReviewTranslationFactory(factory.django.DjangoModelFactory):
    review = factory.SubFactory('reviews.tests.factories.ReviewFactory')

    class Meta:
        model = 'reviews.ReviewTranslation'
