DB_HOST="primary.alloydb.prod"
DB_USER="logistic"
DB_NAME="logistic"
DB_PASS=""

PGPASSWORD=$DB_PASS psql -h $DB_HOST -p 5432 -U $DB_USER -d $DB_NAME -c \
"\COPY auth_user(id,password,last_login,is_superuser,username,first_name,last_name,email,is_staff,is_active,date_joined) FROM 'dumps/auth_user_additional.csv' WITH CSV HEADER"

PGPASSWORD=$DB_PASS  psql -h $DB_HOST -p 5432 -U $DB_USER -d $DB_NAME -c \
"SELECT setval('auth_user_id_seq', (SELECT MAX(id) FROM auth_user));"
