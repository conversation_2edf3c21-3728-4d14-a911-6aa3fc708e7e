"use strict";(self["webpackChunkcustom_audiences"]=self["webpackChunkcustom_audiences"]||[]).push([[359],{1494:(e,l,a)=>{a.d(l,{Y8:()=>t,R1:()=>n,XY:()=>u,in:()=>s,Uu:()=>i,hg:()=>r});const t=[{label:"Austria",value:"austria"},{label:"Belgium",value:"belgium"},{label:"Bulgaria",value:"bulgaria"},{label:"Croatia",value:"croatia"},{label:"Czech",value:"czech"},{label:"Denmark",value:"denmark"},{label:"Estonia",value:"estonia"},{label:"Finland",value:"finland"},{label:"France",value:"france"},{label:"Germany",value:"germany"},{label:"Greece",value:"greece"},{label:"Hungary",value:"hungary"},{label:"Ireland",value:"ireland"},{label:"Italy",value:"italy"},{label:"Latvia",value:"latvia"},{label:"Lithuania",value:"lithuania"},{label:"Luxembourg",value:"luxembourg"},{label:"Netherlands",value:"netherlands"},{label:"Norway",value:"norway"},{label:"Poland",value:"poland"},{label:"Portugal",value:"portugal"},{label:"Romania",value:"romania"},{label:"Slovakia",value:"slovakia"},{label:"Slovenia",value:"slovenia"},{label:"Spain",value:"spain"},{label:"Sweden",value:"sweden"},{label:"Switzerland",value:"switzerland"},{label:"United Kingdom",value:"united_kingdom"}],n=[{label:"Web desktop",value:0},{label:"Web mobile",value:1},{label:"Mobile Iphone",value:2},{label:"Mobile Ipad",value:3},{label:"Mobile Android",value:4},{label:"Mobile Android Tablet",value:5},{label:"Mobile Native IOS",value:8},{label:"Remote Api",value:6},{label:"Unknown source",value:15},{label:"Internal",value:16},{label:"Crow",value:99}],u=[{label:"EN",value:0},{label:"DE",value:1},{label:"FR",value:3}],s=[{label:"Normal",value:"STATUS_ENABLED"},{label:"Normal - region after correction",value:"STATUS_ENABLED_VAT_REGION_CORRECTION"},{label:"Disabled",value:"STATUS_DISABLED"},{label:"Pro forma",value:"STATUS_PROFORMA"},{label:"Cancelled",value:"STATUS_CANCELLED"},{label:"Correcting",value:"STATUS_CORRECTING"},{label:"Blank",value:"STATUS_BLANK"},{label:"External ES(sumup,cash)",value:"STATUS_ADDITIONAL_ES"},{label:"Correcting draft",value:"STATUS_CORRECTING_DRAFT"}],i=[{label:"Shelves (jetty)",value:"jetty"},{label:"Wardrobes (watty)",value:"watty"}],r=[{label:"Type01",value:0},{label:"Type01 - veneer",value:2},{label:"Type02",value:1},{label:"Type03",value:3}]},6430:(e,l,a)=>{a.d(l,{Z:()=>d});var t=a(3673),n=a(2323);function u(e,l,a,u,s,i){const r=(0,t.up)("q-input");return(0,t.wg)(),(0,t.j4)(r,{modelValue:u.vValue,"onUpdate:modelValue":[l[0]||(l[0]=e=>u.vValue=e),u.updateValue],class:(0,n.C_)(["q-px-md q-pt-lg q-mb-md",a.extraClass]),disable:a.disable,hint:a.hint,label:a.label,type:a.inputType,autogrow:"textarea"===a.inputType},null,8,["modelValue","class","disable","hint","label","type","autogrow","onUpdate:modelValue"])}var s=a(1959);const i={name:"BaseInput",props:{valueIn:{type:[String,Number],default:""},disable:{type:Boolean,default:!1},extraClass:{type:[String,Array],default:""},hint:{type:String,default:""},inputType:{type:String,default:"text"},label:{type:String,default:""}},emits:["input"],setup(e,{emit:l}){const a=(0,s.iH)(e.valueIn),t=()=>{l("input",a.value)};return{vValue:a,updateValue:t}}};var r=a(4842),o=a(7518),p=a.n(o);i.render=u;const d=i;p()(i,"components",{QInput:r.Z})},2479:(e,l,a)=>{a.d(l,{Z:()=>b});var t=a(3673),n=a(2323);const u={class:"q-mx-md q-mt-xl position-relative q-field--float q-field--toggle"},s={class:"q-field__label absolute"};function i(e,l,a,i,r,o){const p=(0,t.up)("q-toggle");return(0,t.wg)(),(0,t.iD)("div",u,[(0,t._)("label",s,(0,n.zw)(a.label),1),(0,t.Wm)(p,{modelValue:i.vValue,"onUpdate:modelValue":[l[0]||(l[0]=e=>i.vValue=e),i.updateValue],color:"teal",class:"q-pt-lg",disable:a.disable},null,8,["modelValue","disable","onUpdate:modelValue"])])}var r=a(1959);const o={name:"BaseToggle",props:{valueIn:{type:Boolean,default:!1},disable:{type:Boolean,default:!1},label:{type:String,default:""}},emits:["input"],setup(e,{emit:l}){const a=(0,r.iH)(e.valueIn),t=()=>{l("input",a.value)};return{vValue:a,updateValue:t}}};var p=a(6115),d=a(2582),v=a(7518),m=a.n(v);o.render=i;const b=o;m()(o,"components",{QField:p.Z,QToggle:d.Z})},6377:(e,l,a)=>{a.d(l,{Z:()=>r});var t=a(3673);(0,t.dD)("data-v-46c0153c");const n={class:"heading q-ma-md flex"},u=(0,t.Uk)(" ← Go back ");function s(e,l,a,s,i,r){const o=(0,t.up)("router-link");return(0,t.wg)(),(0,t.iD)("div",n,[(0,t.Wm)(o,{to:{name:"Home"},class:"text-overline link flex q-pr-md q-py-sm"},{default:(0,t.w5)((()=>[u])),_:1})])}(0,t.Cn)();const i={name:"LinkToHome"};i.render=s,i.__scopeId="data-v-46c0153c";const r=i},359:(e,l,a)=>{a.r(l),a.d(l,{default:()=>j});var t=a(3673);const n={key:0,class:"q-px-md q-pb-xl single-audience"},u={class:"grid content-start q-pb-l"},s={class:"q-px-md q-py-lg flex column"},i=(0,t._)("span",{class:"text-grey-8 q-pt-xs"},"Download file",-1),r={key:0},o=["href"],p={key:1,class:"text-grey-6"},d={key:0,class:"grid content-start q-pb-l"},v={key:1,class:"q-py-xl flex justify-center items-center"};function m(e,l,a,m,b,c){const f=(0,t.up)("LinkToHome"),_=(0,t.up)("BaseInput"),g=(0,t.up)("q-icon"),y=(0,t.up)("BaseInputPresentation"),I=(0,t.up)("BaseSelectPresentation"),S=(0,t.up)("BaseToggle"),O=(0,t.up)("BaseCalendarPresentation"),T=(0,t.up)("q-spinner"),h=(0,t.up)("q-page");return(0,t.wg)(),(0,t.j4)(h,null,{default:(0,t.w5)((()=>[m.isLoaded?((0,t.wg)(),(0,t.iD)("div",n,[(0,t.Wm)(f),(0,t._)("section",u,[(0,t.Wm)(_,{label:"Title",valueIn:m.item.name,disable:!0},null,8,["valueIn"]),(0,t.Wm)(_,{label:"Status",valueIn:m.item.status,disable:!0,extraClass:["text-capitalize",m.item.status]},null,8,["valueIn","extraClass"]),(0,t._)("p",s,[i,m.item.csv_file&&"Download file"!==m.item.csv_file?((0,t.wg)(),(0,t.iD)("span",r,[(0,t._)("a",{href:m.item.csv_file,class:"q-px-lg q-py-sm flex download-link",download:""},[(0,t.Wm)(g,{name:"download",size:"sm",color:"primary"})],8,o)])):"Download file"!==m.item.csv_file?((0,t.wg)(),(0,t.iD)("span",p," Not ready ")):(0,t.kq)("",!0)])]),m.item.filters?((0,t.wg)(),(0,t.iD)("section",d,[(0,t.Wm)(y,{label:"Minimum value in €",valueIn:m.item.filters.value_min},null,8,["valueIn"]),(0,t.Wm)(y,{label:"Maximum value in €",valueIn:m.item.filters.value_max},null,8,["valueIn"]),(0,t.Wm)(I,{label:"Language",valueIn:m.item.filters.use_languages,selectOptions:m.selectOptionsLang},null,8,["valueIn","selectOptions"]),(0,t.Wm)(I,{label:"Country",valueIn:m.item.filters.country,selectOptions:m.selectOptionsCountry},null,8,["valueIn","selectOptions"]),(0,t.Wm)(I,{label:"Furniture type",valueIn:m.item.filters.furniture_type,selectOptions:m.selectOptionsFurnitureType},null,8,["valueIn","selectOptions"]),(0,t.Wm)(I,{label:"Shelf type",valueIn:m.item.filters.shelf_type,selectOptions:m.selectOptionsShelfType},null,8,["valueIn","selectOptions"]),(0,t.Wm)(I,{label:"Order source",valueIn:m.item.filters.order_source,selectOptions:m.selectOptionOrderSource,disable:!0},null,8,["valueIn","selectOptions"]),(0,t.Wm)(I,{label:"Order status",valueIn:m.item.filters.status,selectOptions:m.selectOptionOrderStatus},null,8,["valueIn","selectOptions"]),(0,t.Wm)(_,{label:"Promo code",class:"q-pt-xl q-mb-0",valueIn:m.item.filters.use_promo_codes,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Delays",valueIn:m.item.filters.has_delays,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Was free return used",valueIn:m.item.filters.has_free_return,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Has complaints",value:m.item.filters.has_complaints,disable:!0},null,8,["value"]),(0,t.Wm)(S,{label:"Has vouchers",valueIn:m.item.filters.has_vouchers,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Returning customer",valueIn:m.item.filters.is_returning_user,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Owner has reviews",valueIn:m.item.filters.owner_has_reviews,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Only samples",valueIn:m.item.filters.contains_only_samples,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Has save for later",valueIn:m.item.filters.has_save_for_later,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Double-opt-in",valueIn:m.item.filters.has_double_opt_in,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Has phone number",valueIn:m.item.filters.has_contact_phone_number,disable:!0},null,8,["valueIn"]),(0,t.Wm)(S,{label:"Has email",valueIn:m.item.filters.has_email,disable:!0},null,8,["valueIn"]),(0,t.Wm)(O,{label:"Order placed at:",before:m.item.filters.placed_at_before,after:m.item.filters.placed_at_after},null,8,["before","after"]),(0,t.Wm)(O,{label:"Order paid at:",before:m.item.filters.paid_at_before,after:m.item.filters.paid_at_after},null,8,["before","after"]),(0,t.Wm)(O,{label:"Order updated at:",before:m.item.filters.updated_at_before,after:m.item.filters.updated_at_after},null,8,["before","after"])])):(0,t.kq)("",!0)])):((0,t.wg)(),(0,t.iD)("div",v,[(0,t.Wm)(T,{color:"primary",size:"3em"})]))])),_:1})}a(7768);var b=a(3553),c=a(1959),f=a(8825),_=a(9582),g=a(6430);function y(e,l,a,n,u,s){const i=(0,t.up)("q-input");return(0,t.wg)(),(0,t.j4)(i,{modelValue:n.vValue,"onUpdate:modelValue":l[0]||(l[0]=e=>n.vValue=e),class:"q-px-md q-py-lg",disable:!0,label:a.label},null,8,["modelValue","label"])}const I={name:"BaseInputPresentation",props:{valueIn:{type:[String,Number],default:"None"},label:{type:String,default:""}},setup(e){const l=(0,c.iH)(e.valueIn);return{vValue:l}}};var S=a(4842),O=a(7518),T=a.n(O);I.render=y;const h=I;T()(I,"components",{QInput:S.Z});var q=a(2479);function w(e,l,a,n,u,s){const i=(0,t.up)("q-select");return(0,t.wg)(),(0,t.j4)(i,{modelValue:n.vValue,"onUpdate:modelValue":l[0]||(l[0]=e=>n.vValue=e),class:"q-px-md q-pt-lg",disable:!0,label:a.label,multiple:!0,options:a.selectOptions,"emit-value":"","map-options":""},null,8,["modelValue","label","options"])}const x={name:"BaseSelectPresentation",props:{valueIn:{type:[String,Number,Array],default:"All"},label:{type:String,default:""},selectOptions:{type:Array,required:!0}},setup(e){const l=(0,c.iH)(e.valueIn);return{vValue:l}}};var W=a(9896);x.render=w;const V=x;T()(x,"components",{QSelect:W.Z});var k=a(2323);const A={class:"q-mx-md q-mt-xl q-pt-lg position-relative"},C={class:"q-field__label absolute"},B=(0,t._)("span",{class:"text-grey-8"},"From:",-1),D=(0,t._)("br",null,null,-1),U=(0,t._)("span",{class:"text-grey-8"},"To:",-1);function N(e,l,a,n,u,s){return(0,t.wg)(),(0,t.iD)("div",A,[(0,t._)("p",C,(0,k.zw)(a.label),1),(0,t._)("p",null,[B,(0,t.Uk)(" "+(0,k.zw)(a.before),1),D,U,(0,t.Uk)(" "+(0,k.zw)(a.after),1)])])}const R={name:"BaseCalendarPresentation",props:{before:{type:String,default:""},after:{type:String,default:""},label:{type:String,default:""}}};var E=a(6115);R.render=N;const L=R;T()(R,"components",{QField:E.Z});var Z=a(6377),H=a(1494);const P={name:"SinglePage",components:{LinkToHome:Z.Z,BaseCalendarPresentation:L,BaseInput:g.Z,BaseInputPresentation:h,BaseToggle:q.Z,BaseSelectPresentation:V},setup(){const e=(0,_.yj)(),l=(0,f.Z)(),a=(0,c.iH)({}),n=(0,c.iH)(!1),u=e=>{a.value.filters[e]=a.value.filters[e]&&a.value.filters[e].split(",").map((e=>Number(e)))},s=()=>{b.api.get(`${e.params.id}`).then((e=>{a.value=e.data,a.value.filters&&(u("use_languages"),u("shelf_type"),u("order_source"),u("furniture_type"),a.value.filters.country=a.value.filters.country&&a.value.filters.country.split(","),a.value.filters.status=a.value.filters.status&&a.value.filters.status.split(","),a.value.filters.use_promo_codes=a.value.filters.use_promo_codes?a.value.filters.use_promo_codes.replaceAll(",",", "):"All"),n.value=!0}),50).catch((e=>l.notify({message:`ERROR: ${e}`,type:"error"})))};return(0,t.bv)((()=>{s()})),{isLoaded:n,item:a,selectOptionsLang:H.XY,selectOptionOrderSource:H.R1,selectOptionsCountry:H.Y8,selectOptionOrderStatus:H["in"],selectOptionsFurnitureType:H.Uu,selectOptionsShelfType:H.hg}}};var F=a(4379),z=a(4554),Q=a(9754);P.render=m;const j=P;T()(P,"components",{QPage:F.Z,QIcon:z.Z,QSpinner:Q.Z,QField:E.Z})}}]);