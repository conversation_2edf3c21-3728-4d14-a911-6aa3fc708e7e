import datetime
import operator

import pytest

from custom.enums import ShelfType
from custom.models import Countries
from custom_audiences.filters.custom_audience import CustomAudienceFilterSet
from orders.choices import OrderSource
from orders.enums import OrderStatus
from orders.tests.factories import (
    OrderFactory,
    OrderItemFactory,
)
from reviews.tests.factories import ReviewFactory
from vouchers.tests.factories import VoucherFactory


@pytest.mark.django_db
class TestCustomAudienceFilterSet:
    def test_has_vouchers(self):
        voucher = VoucherFactory.create()
        orders = [
            OrderFactory.create(used_promo=voucher),
            OrderFactory.create(),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'has_vouchers': True,
            },
        )
        filtered_orders = list(filter_set.qs)
        assert filtered_orders == [orders[0]]

    def test_use_promo_codes(self):
        bf_voucher = VoucherFactory.create(code='BF2020')
        ref_voucher = VoucherFactory.create(code='RAFTEST')
        voucher = VoucherFactory.create()
        orders = [
            OrderFactory.create(used_promo=voucher),
            OrderFactory.create(used_promo=bf_voucher),
            OrderFactory.create(used_promo=ref_voucher),
            OrderFactory.create(used_promo=None),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'use_promo_codes': 'BF2020,RAFTEST',
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == orders[1:3]

    @pytest.mark.parametrize(
        ('has_delays', 'expected_result'),
        [
            (True, 0),
            (False, 1),
        ],
    )
    def test_has_delays(
        self, has_delays, expected_result, order_factory, logistic_order_dto_factory
    ):
        delayed_delivery_date = datetime.date(year=2020, month=10, day=1)
        delivery_date = datetime.date(year=2020, month=9, day=10)
        estimated_delivery_time = datetime.datetime(year=2020, month=9, day=1)

        orders = [
            order_factory(
                estimated_delivery_time=estimated_delivery_time,
                logistic_info=[
                    logistic_order_dto_factory(delivered_date=delayed_delivery_date)
                ],
            ),
            order_factory(
                estimated_delivery_time=estimated_delivery_time,
                logistic_info=[
                    logistic_order_dto_factory(delivered_date=delivery_date)
                ],
            ),
        ]

        filter_set = CustomAudienceFilterSet(
            data={
                'has_delays': has_delays,
            },
        )
        filtered_orders = list(filter_set.qs)
        assert filtered_orders == [orders[expected_result]]

    @pytest.mark.parametrize(
        ('user_language', 'expected_idxs'),
        [('en', [0]), ('fr', [1]), ('de', [2]), ('de,fr', [1, 2])],
    )
    def test_use_language(self, user_language, expected_idxs):
        orders = [
            OrderFactory.create(owner__profile__language='en'),
            OrderFactory.create(owner__profile__language='fr'),
            OrderFactory.create(owner__profile__language='de'),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'use_languages': user_language,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.nbp
    @pytest.mark.parametrize(
        ('has_valid_complaints', 'paid_at', 'reported_at', 'expected_idxs'),
        [
            (True, '2020-10-11', '2020-11-11', [0]),
            (True, '2020-11-11', '2020-10-11', []),
            (False, '2020-11-11', '2020-10-11', [0, 1, 2, 3]),
            (False, '2020-10-11', '2020-11-11', [1, 2, 3]),
        ],
    )
    def test_has_complaints(
        self,
        has_valid_complaints,
        paid_at,
        reported_at,
        expected_idxs,
        order_factory,
        product_factory,
        complaint_factory,
    ):
        orders = [
            order_factory(
                email='<EMAIL>',
                paid_at=paid_at,
            ),
            order_factory(email='<EMAIL>'),
            order_factory(email='<EMAIL>'),
            order_factory(email=''),
        ]
        product = product_factory(
            order=orders[0],
        )
        complaint_factory(
            product=product,
            reported_date=reported_at,
        )
        filter_set = CustomAudienceFilterSet(
            data={
                'has_complaints': has_valid_complaints,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))

        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('is_returning_user', 'expected_idxs'),
        [(True, [2]), (False, [0, 1])],
    )
    def test_is_returning_user(self, is_returning_user, expected_idxs):
        orders = [
            OrderFactory.create(email='<EMAIL>'),
            OrderFactory.create(
                email='<EMAIL>',
                status=OrderStatus.DELIVERED,
            ),
            OrderFactory.create(email='<EMAIL>', status=OrderStatus.CART),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'is_returning_user': is_returning_user,
            },
        )

        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('has_reviews', 'expected_idxs'),
        [(True, [0, 2]), (False, [1, 3])],
    )
    def test_owner_has_reviews(self, has_reviews, expected_idxs):
        orders = [
            OrderFactory.create(
                paid_at='2020-10-11',
                email='<EMAIL>',
                status=OrderStatus.DELIVERED,
            ),
            OrderFactory.create(
                paid_at='2020-12-11',
                email='<EMAIL>',
                status=OrderStatus.DELIVERED,
            ),
            OrderFactory.create(
                paid_at='2019-12-11',
                email='',
                owner__email='<EMAIL>',
            ),
            OrderFactory.create(email='', owner__email=''),
        ]
        ReviewFactory.create(
            email='<EMAIL>',
            created_at='2020-11-11',
            order=None,
        )
        ReviewFactory.create(
            email='<EMAIL>',
            created_at='2020-01-11',
            order=orders[2],
        )
        filter_set = CustomAudienceFilterSet(
            data={
                'owner_has_reviews': has_reviews,
            },
        )

        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        ('order_source', 'expected_idxs'),
        [
            (f'{OrderSource.WEB_DESKTOP}', [0]),
            (f'{OrderSource.WEB_MOBILE}', [1]),
            (f'{OrderSource.MOBILE_IPHONE}', [2]),
            (f'{OrderSource.WEB_DESKTOP},{OrderSource.MOBILE_IPHONE}', [0, 2]),
        ],
    )
    def test_order_source(self, order_source, expected_idxs):
        orders = [
            OrderFactory.create(order_source=OrderSource.WEB_DESKTOP),
            OrderFactory.create(order_source=OrderSource.WEB_MOBILE),
            OrderFactory.create(order_source=OrderSource.MOBILE_IPHONE),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'order_source': order_source,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('value_range', 'expected_idxs'),
        [
            ('0,520', [1]),
            ('320.10,1800.00', [2]),
            ('0,2000', [0, 1, 2]),
        ],
    )
    def test_value(self, value_range, expected_idxs):
        orders = [
            OrderFactory.create(total_price=1800.67),
            OrderFactory.create(total_price=320.01),
            OrderFactory.create(total_price=1000),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'value': value_range,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('after', 'before', 'expected_idxs'),
        [
            ('2020-02-12', '2020-10-11', [0, 2]),
            ('2020-10-12', '2020-12-11', []),
        ],
    )
    def test_placed_at(self, after, before, expected_idxs):
        orders = [
            OrderFactory.create(placed_at='2020-10-11'),
            OrderFactory.create(placed_at='2020-02-11'),
            OrderFactory.create(placed_at='2020-06-06'),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'placed_at_after': after,
                'placed_at_before': before,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('has_phone', 'expected_idxs'),
        [
            (False, [2, 3, 4]),
            (True, [0, 1]),
        ],
    )
    def test_has_contact_phone_number(self, has_phone, expected_idxs):
        orders = [
            OrderFactory.create(phone='666666666'),
            OrderFactory.create(phone='123456789'),
            OrderFactory.create(phone=''),
            OrderFactory.create(phone=None),
            OrderFactory.create(phone=''),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'has_contact_phone_number': has_phone,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('has_email', 'expected_idxs'),
        [
            (True, [0, 1]),
            (False, [2]),
        ],
    )
    def test_has_email(self, has_email, expected_idxs):
        orders = [
            OrderFactory.create(email='<EMAIL>'),
            OrderFactory.create(email='', owner__email='<EMAIL>'),
            OrderFactory.create(email='', owner__email=''),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'has_email': has_email,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('country', 'expected_idxs'),
        [
            (f'{Countries.belgium.name}', [0]),
            (f'{Countries.estonia.name},{Countries.germany.name}', [1, 2]),
        ],
    )
    def test_country(self, country, expected_idxs):
        orders = [
            OrderFactory.create(country=Countries.belgium.name),
            OrderFactory.create(country=Countries.estonia.name),
            OrderFactory.create(country=Countries.germany.name),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'country': country,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('furniture_types', 'expected_idxs'),
        [
            ('jetty', [0, 1]),
            ('watty', [1]),
            ('watty,samplebox', [0, 1]),
            ('watty,jetty', [0, 1]),
        ],
    )
    def test_contains_furniture_types(self, furniture_types, expected_idxs):
        orders = [
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
        ]
        OrderItemFactory.create(is_jetty=True, order=orders[0])
        OrderItemFactory.create(is_jetty=True, order=orders[0])
        OrderItemFactory.create(is_sample_box=True, order=orders[0])
        OrderItemFactory.create(is_jetty=True, order=orders[1])
        OrderItemFactory.create(is_watty=True, order=orders[1])

        filter_set = CustomAudienceFilterSet(
            data={
                'furniture_type': furniture_types,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('order_status', 'expected_idxs'),
        [
            (str(OrderStatus.DELIVERED), [0]),
            (str(OrderStatus.CART), [3, 5]),
            (f'{OrderStatus.PAYMENT_PENDING},{OrderStatus.CART}', [2, 3, 5]),
            (str(OrderStatus.SHIPPED), []),
        ],
    )
    def test_order_status(self, order_status, expected_idxs):
        orders = [
            OrderFactory.create(status=OrderStatus.DELIVERED),
            OrderFactory.create(status=OrderStatus.IN_PRODUCTION),
            OrderFactory.create(status=OrderStatus.PAYMENT_PENDING),
            OrderFactory.create(status=OrderStatus.CART),
            OrderFactory.create(status=OrderStatus.CANCELLED),
            OrderFactory.create(status=OrderStatus.CART),
        ]
        filter_set = CustomAudienceFilterSet(
            data={
                'status': order_status,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('shelf_types', 'expected_idxs'),
        [
            (f'{ShelfType.TYPE01.value}', [0]),
            (f'{ShelfType.TYPE02.value}', [0, 2]),
            (f'{ShelfType.VENEER_TYPE01.value}', [1]),
            (f'{ShelfType.TYPE01.value},{ShelfType.TYPE02.value}', [0, 2]),
        ],
    )
    def test_contains_shelf_types(self, shelf_types, expected_idxs):
        orders = [
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
        ]
        OrderItemFactory.create(
            is_jetty=True,
            order_item__shelf_type=0,
            order=orders[0],
        )
        OrderItemFactory.create(
            is_jetty=True,
            order_item__shelf_type=1,
            order=orders[0],
        )

        OrderItemFactory.create(
            is_jetty=True,
            order_item__shelf_type=2,
            order=orders[1],
        )
        OrderItemFactory.create(is_watty=True, order=orders[1])

        OrderItemFactory.create(is_sample_box=True, order=orders[2])
        OrderItemFactory.create(
            is_jetty=True,
            order_item__shelf_type=1,
            order=orders[2],
        )

        filter_set = CustomAudienceFilterSet(
            data={
                'shelf_type': shelf_types,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.skip('rewrite this using new filter')
    @pytest.mark.django_db
    @pytest.mark.parametrize(
        ('has_free_return', 'expected_idxs'),
        [
            (True, [0]),
            (False, [1, 2, 3, 4]),
        ],
    )
    def test_has_free_return(
        self, has_free_return, expected_idxs, logistic_order_dto_factory, order_factory
    ):
        now = datetime.date.today()
        month_ago = now - datetime.timedelta(days=30)
        two_month_ago = now - datetime.timedelta(days=60)
        six_month_ago = now - datetime.timedelta(days=120)

        orders = [
            order_factory(
                status=OrderStatus.DELIVERED,
                logistic_info=[logistic_order_dto_factory(sent_to_customer=month_ago)],
            ),
            order_factory(
                status=OrderStatus.DELIVERED,
                logistic_info=[
                    logistic_order_dto_factory(
                        sent_to_customer=two_month_ago, mailing_disabled=True
                    )
                ],
            ),
            order_factory(
                status=OrderStatus.DELIVERED,
                logistic_info=[
                    logistic_order_dto_factory(sent_to_customer=six_month_ago)
                ],
            ),
            order_factory(status=OrderStatus.CART),
            order_factory(status=OrderStatus.CANCELLED),
        ]

        filter_set = CustomAudienceFilterSet(
            data={
                'has_free_return': has_free_return,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]

    @pytest.mark.parametrize(
        ('contains_only_samples', 'expected_idxs'),
        [
            (True, [0]),
            (False, [1, 2]),
        ],
    )
    def test_contains_only_samples(self, contains_only_samples, expected_idxs):
        orders = [
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
            OrderFactory.create(
                email='<EMAIL>',
                owner__email='<EMAIL>',
                items=[],
            ),
        ]
        OrderItemFactory.create(is_sample_box=True, order=orders[0])
        OrderItemFactory.create(is_jetty=True, order=orders[1])
        OrderItemFactory.create(is_sample_box=True, order=orders[1])
        OrderItemFactory.create(is_jetty=True, order=orders[2])

        filter_set = CustomAudienceFilterSet(
            data={
                'contains_only_samples': contains_only_samples,
            },
        )
        filtered_orders = sorted(filter_set.qs, key=operator.attrgetter('id'))
        assert filtered_orders == [orders[index] for index in expected_idxs]
