from collections import defaultdict
from typing import Optional

from django.db.models import QuerySet

from automatic_batching import models
from complaints.utils import generate_production_order_for_complaint
from custom.utils.report_file import ReportFile
from producers.choices import (
    BatchStatus,
    BatchType,
    ProductStatus,
)
from producers.enums import FeatureEnum
from producers.models import (
    Manufactor,
    Product,
    ProductBatch,
)
from producers.production_files.summary_usage import (
    get_batch_filename,
    get_usage_for_batches_raw,
)
from producers.reports.order_xlsx_report import generate_report_order_summary_xls
from producers.services.create_batch import ProductBatchService


class ComplaintBatching:
    @classmethod
    def create_batches_and_send_mail(cls):
        batches_group_by_ordering = cls.create_batches()
        for manufactor, batches in batches_group_by_ordering.items():
            cls.send_mail_to_producers(manufactor, batches)

    @classmethod
    def create_batches(
        cls,
        product_queryset: Optional[QuerySet[Product]] = None,
    ):
        product_queryset = (
            product_queryset if product_queryset is not None else cls.get_queryset()
        )
        product_queryset = cls.exclude_express_replacement_products(product_queryset)
        grouped_products = cls.get_grouped_products(product_queryset)
        batches_group_by_ordering = defaultdict(list)
        for ordering, batch_group in grouped_products.items():
            for batch_group_name, products in batch_group.items():
                batch = cls.create_batch(ordering, products)
                batches_group_by_ordering[ordering.manufactor].append(batch)
        return batches_group_by_ordering

    @classmethod
    def exclude_express_replacement_products(
        cls, product_queryset: QuerySet[Product]
    ) -> QuerySet[Product]:
        express_replacement_product_ids = [
            product.id
            for product in product_queryset
            if product.order.is_to_be_shipped_complaint_express_replacement()
        ]
        return product_queryset.exclude(id__in=express_replacement_product_ids)

    @classmethod
    def create_batch(
        cls, ordering: models.BatchingOrder, products: list[Product]
    ) -> ProductBatch:
        product_batch_service = ProductBatchService()
        batch = product_batch_service.create(
            products,
            ordering.manufactor,
            generate_files=True,
            batch_type=BatchType.COMPLAINTS,
            status=BatchStatus.IN_PRODUCTION,
        )
        batch.is_auto_generated = True
        batch.save(update_fields=['is_auto_generated'])
        return batch

    @classmethod
    def send_mail_to_producers(
        cls, manufactor: Manufactor, batches: list[ProductBatch]
    ):
        queryset = ProductBatch.objects.filter(id__in=[batch.id for batch in batches])
        files = cls.get_files(queryset)
        mail_receiver = models.AutoBatchMailReceiver.objects.get(manufactor=manufactor)
        setting = models.ComplaintBatchingSetting.objects.filter(is_active=True).first()
        ReportFile.send_files_as_email_attachment(
            files,
            emails=mail_receiver.email_list,
            cc_emails=mail_receiver.cc_email_list,
            subject='Zamowienie reklamacji',
            body=setting.mail_body,
        )
        queryset.update(auto_mail_was_sent=True)

    @classmethod
    def get_files(cls, queryset: QuerySet[ProductBatch]) -> list[ReportFile]:
        _, order_xls_report = generate_report_order_summary_xls(queryset)
        usage_report = ReportFile.load_list_as_csv_file(
            get_usage_for_batches_raw(queryset)['content'],
            get_batch_filename(queryset),
        )
        production_order = generate_production_order_for_complaint(queryset)
        return [order_xls_report, usage_report, production_order]

    @classmethod
    def get_grouped_products(
        cls, product_queryset: Optional[QuerySet[Product]] = None
    ) -> dict[models.BatchingOrder, dict[str, list[Product]]]:
        batching_order = models.BatchingOrder.objects.filter(is_active=True).order_by(
            'ordinal_number'
        )
        products_for_batching = (
            product_queryset if product_queryset is not None else cls.get_queryset()
        )
        grouped_products = defaultdict(lambda: defaultdict(list))
        for product in products_for_batching:
            for ordering in batching_order:
                if cls.does_product_match_ordering(ordering, product):
                    batch_connection = models.BatchConnection.objects.get(
                        manufactor=ordering.manufactor
                    )
                    batch_group_name = cls.get_batch_group_name(
                        batch_connection, product
                    )
                    grouped_products[ordering][batch_group_name].append(product)
                    break
        return grouped_products

    @classmethod
    def get_queryset(cls):
        product_queryset = Product.objects.filter(
            status=ProductStatus.NEW,
            copy_of__isnull=False,
            batch__isnull=True,
        ).exclude(
            complaint__express_replacement=True,
        )
        return cls.exclude_express_replacement_products(product_queryset)

    @classmethod
    def does_product_match_ordering(
        cls, ordering: models.BatchingOrder, product: Product
    ) -> bool:
        if not ordering.is_active:
            return False
        if (
            ordering.original_product_producer
            and ordering.manufactor != product.copy_of.manufactor
        ):
            return False
        if not ordering.does_type_and_color_match(
            product.shelf_type_option, product.color_option
        ):
            return False
        if ordering.excluded_features:
            for feature_value in ordering.excluded_features:
                feature = FeatureEnum(feature_value)
                if feature.does_product_have_feature(product):
                    return False
        return True

    @classmethod
    def get_batch_group_name(
        cls, batch_connection: models.BatchConnection, product: Product
    ) -> str:
        if batch_connection.product_type and batch_connection.unique_color:
            return f'{product.shelf_type_option.name}_{product.color_option.name}'
        elif batch_connection.product_type:
            return product.shelf_type_option.name
        elif batch_connection.unique_color:
            return product.color_option.name
        else:
            return 'all'

    @classmethod
    def get_grouped_products_as_serializable_dict(cls) -> dict[str, dict[str, list]]:
        grouped_products = cls.get_grouped_products()
        return {
            str(ordering): {
                batch_name: ','.join([str(product.id) for product in products])
                for batch_name, products in batch_dict.items()
            }
            for ordering, batch_dict in grouped_products.items()
        }
