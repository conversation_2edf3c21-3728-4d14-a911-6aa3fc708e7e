from logger.choices import Source
from logger.models import Log


class LoggerViewMixin(object):
    def log_cs_data(self, request, action, model, model_id, data, additional_info):
        log = Log(
            user=request.user,
            logger_source=Source.LOGGER_CS_VIEW,
            action=action,
            model=model,
            model_id=model_id,
            url=request.path,
            data=data,
            additional_info=additional_info,
        )
        log.save()

    @staticmethod
    def prepare_form_data(form_cleaned_data):
        data = {}
        for key, val in list(form_cleaned_data.items()):
            if isinstance(val, (int, str)):
                data[key] = val
            else:
                data[key] = str(val)
        return data
