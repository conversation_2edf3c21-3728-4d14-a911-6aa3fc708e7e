# Generated by Django 3.2.16 on 2023-02-06 12:54

from django.db import migrations


def remove_countries_with_region_default(apps, schema_editor):
    Country = apps.get_model('regions', 'Country')
    default_region_countries = Country.objects.filter(region__name='_default')
    for country in default_region_countries:
        country.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0006_change_chf_currency_rate'),
        ('product_feeds', '0033_rebind_proper_country'),
    ]

    operations = [
        migrations.RunPython(
            remove_countries_with_region_default,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
