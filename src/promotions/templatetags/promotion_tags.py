from frontend_cms.templatetags.util_tags import register
from promotions.utils import (
    strikethrough_promo,
    strikethrough_promo_value,
)


@register.simple_tag()
def get_global_strikethrough_pricing():
    return strikethrough_promo_value()


@register.simple_tag()
def get_global_strikethrough_code():
    promo = strikethrough_promo()
    if promo:
        return promo.promo_code.code
    return None
