from datetime import timedelta

from django.contrib import admin
from django.db.models import Count
from django.utils import timezone
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from custom.admin_action import admin_action_with_form
from feeds.forms import FeedItemsForm
from render_tasks.choices import RenderTaskStatuses
from render_tasks.filters import (
    UnrealCategoryFilter,
    UnrealFurnishingFilter,
    UnrealHostnameFilter,
    UnrealImageTypeFilter,
    UnrealInteriorFilter,
    UnrealMoodFilter,
    UnrealShelfTypeFilter,
    UnrealShotPresetFilter,
    UnrealToneFilter,
)
from render_tasks.models import (
    UnrealRenderTask,
    WebglRenderTask,
)


@mark_safe
def image_preview(obj):
    if obj.image:
        return (
            '<div style="width:300px; float:left">'
            + f'<img src="{obj.image.url}" width="300px" /></div>'
        )
    else:
        return '-'


image_preview.short_description = 'preview'


def change_tasks_to_todo(self, request, queryset):
    queryset.update(status=RenderTaskStatuses.TO_DO)


class WebglRenderTaskAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'status',
        'furniture',
        'task_type',
        'resolved_at',
    )
    actions = (change_tasks_to_todo,)
    list_filter = ('status', 'task_type')


class UnrealRenderTaskAdmin(admin.ModelAdmin):
    change_list_template = 'admin/unrealrendertask_change_list.html'
    list_display = (
        'id',
        'status',
        'resolved_at',
        'image_preview',
        'custom_furniture',
        'custom_initiator',
    )
    search_fields = ('furniture_id', 'initiator_id')
    actions = (change_tasks_to_todo, 'add_to_feed_category')
    list_filter = (
        'status',
        UnrealImageTypeFilter,
        UnrealCategoryFilter,
        UnrealShelfTypeFilter,
        UnrealHostnameFilter,
        UnrealInteriorFilter,
        UnrealMoodFilter,
        UnrealFurnishingFilter,
        UnrealToneFilter,
        UnrealShotPresetFilter,
    )

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}

        resolved_per_consumer = (
            UnrealRenderTask.objects.filter(
                resolved_at__gte=timezone.now() - timedelta(hours=1)
            )
            .values('consumer')
            .annotate(count=Count('id'))
            .order_by('-count')
        )

        resolved_stats = {
            entry['consumer']: entry['count'] for entry in resolved_per_consumer
        }

        extra_context['stats'] = {'resolved_per_consumer': resolved_stats}

        return super().changelist_view(request, extra_context=extra_context)

    @admin.display(description='Furniture')
    def custom_furniture(self, obj: UnrealRenderTask) -> str:
        return f'{obj.furniture_content_type.model} #{obj.furniture_id}'

    @admin.display(description='Initiator')
    def custom_initiator(self, obj: UnrealRenderTask) -> str:
        return f'{obj.initiator_content_type.model} #{obj.initiator_id}'

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related('furniture_content_type', 'initiator_content_type')
        )

    def image_preview(self, obj):
        if not obj.image:
            return '-'
        return format_html(f'<img src="{obj.image.url}" width="300px" />')

    @admin.action(description='Add task to feed category')
    def add_to_feed_category(self, request, queryset):
        success_function = FeedItemsForm.create_feed_items_for_unreal_render_task

        kwargs = {
            'modeladmin': self,
            'request': request,
            'queryset': queryset,
            'form_class': FeedItemsForm,
            'success_function': success_function,
            'success_function_kwargs': {},
        }
        return admin_action_with_form(**kwargs)


admin.site.register(UnrealRenderTask, UnrealRenderTaskAdmin)
admin.site.register(WebglRenderTask, WebglRenderTaskAdmin)
