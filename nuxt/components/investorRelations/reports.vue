<template>
  <section>
    <div
      v-for="(report, index) in reports"
      v-bind:key="`${index}-report-quarter`"
      class="mb-24"
    >
      <h3
        class="bold-20 uppercase py-16 border-b border-neutral-900"
        v-html="report.title"
      />

      <BaseLink
        v-for="(file, i) in report.files"
        v-bind:key="`${i}-report-file`"
        variant="custom"
        target="_blank"
        class="flex gap-16 justify-between items-center border-b border-grey-700 py-16"
        v-bind="{
          trackData: {},
          href: file.url,
        }"
      >
        <div class="flex items-center">
          <img
            src="~/assets/icons/download.svg"
            alt="accept"
            class="w-40 mr-16"
          >
          <p
            class="normal-16 text-neutral-900"
            v-html="file.title"
          />
        </div>
        <div class="flex flex-col md:flex-row text-right min-w-80">
          <p
            class="normal-10 text-grey-900 md:order-1 md:ml-16"
            v-html="format(new Date(file.date), 'MMMM dd, uuuu')"
          />
          <p class="flex justify-end normal-10 text-offblack-900 mt-8 md:mt-0 md:order-0">
            <span
              class="block mr-8 md:mr-16"
              v-html="file.format"
            />
            {{ bytesToMegaBytes(file.size) }}MB
          </p>
        </div>
      </BaseLink>
    </div>
  </section>
</template>

<script>
import { defineComponent, ref, useContext, useFetch } from '@nuxtjs/composition-api';
import { groq } from '@nuxtjs/sanity';
import { format } from 'date-fns';

export default defineComponent({
  setup() {
    const { $logException, $sanity } = useContext();
    const reports = ref();

    const bytesToMegaBytes = (bytes) => {
      return (bytes / (1024 * 1024)).toFixed(2);
    };

    useFetch(async() => {
      const query = groq`*[_type == "reportsQuarters"]
      {
        ...,
        files[] {
          ...,
          "url": asset->url,
          "size": asset->size,
          "name": asset->originalFilename,
        }
      }`;

      try {
        reports.value = await $sanity.fetch(query);
      } catch (error) {
        $logException(error);
      }
    });

    return {
      reports,
      bytesToMegaBytes,
      format
    };
  }
});
</script>
