<template>
  <section class="container-cstm-fluid bg-white" data-section="usps">
    <ul v-if="isBigVariant" class="flex flex-col lg:flex-row justify-between py-12 xl:py-32">
      <template
        v-for="{ uspType, isHiddenOnMobile, text, subTitle, icon, isAssemblyFree } in options"
      >
        <SanityUSPBarLargeItem
          v-if="uspType === 'rating'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <div>
              <BaseRating v-bind="{ rating: reviewsObject.rating }" />
            </div>
          </template>
          <template #title>
            <p v-html="$t('usps.large.title1')" />
          </template>
          <template #subtitle>
            <p v-html="$t('usps.large.subtitle1', { count: reviewsObject.count })" />
          </template>
        </SanityUSPBarLargeItem>

        <SanityUSPBarLargeItem
          v-if="uspType === 'freeDelivery'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconDelivery class="block w-[22px] h-[22px] text-offblack-600" />
          </template>
          <template #title>
            <p v-html="$t('usps.large.title2')" />
          </template>
          <template #subtitle>
            <p class="max-w-[300px]" v-html="$t('usps.large.subtitle2')" />
            <IconTooltip
              v-tooltip="{ content: $t('usps.tooltip1'), placement: 'bottom-center', classes: ['max-w-[300px]'] }"
              class="max-w-[15px] max-h-[15px] ml-4 cursor-pointer text-grey-900"
            />
          </template>
        </SanityUSPBarLargeItem>

        <SanityUSPBarLargeItem
          v-if="uspType === 'freeReturns'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconReturn class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p v-html="$t('usps.title4')" />
          </template>
        </SanityUSPBarLargeItem>
        <SanityUSPBarLargeItem
          v-if="uspType === 'reviews'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconCustomers class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p v-html="$t('usps.title5')" />
          </template>
        </SanityUSPBarLargeItem>

        <SanityUSPBarLargeItem
          v-if="uspType === 'assemblyService'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconAssembly class="block w-[22px] h-[22px] text-offblack-600" />
          </template>
          <template #title>
            <p v-html="isAssemblyFree ? $t('usps.title3.free') : $t('usps.title3.optional')" />
          </template>
          <template #subtitle>
            <span v-html="$t('usps.large.subtitle3')" />
            <VPopover class="flex-inline max-w-[300px]" placement="bottom-center">
              <IconTooltip class="max-w-[15px] max-h-[15px] ml-4 cursor-pointer text-grey-900" />
              <template slot="popover" class="max-w-[300px]">
                <p v-html="$t('usps.tooltip2')" />
                <BaseLink
                  variant="link-color"
                  arrow="right"
                  class="mt-8 normal-14 inline-flex items-center"
                  v-bind="{ trackData: {} }"
                  v-on="{ click: showOptionalAssembly }"
                >
                  {{ $t('common.learn_more') }}
                </BaseLink>
              </template>
            </VPopover>
            <ModalOptionalAssembly />
          </template>
        </SanityUSPBarLargeItem>
        <SanityUSPBarLargeItem
          v-if="uspType === 'custom'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <img
              class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8"
              v-bind:src="$urlFor(icon).url()"
            >
          </template>
          <template #title>
            <p v-html="text" />
          </template>

          <template #subtitle>
            <p class="max-w-[300px]" v-html="subTitle" />
          </template>
        </SanityUSPBarLargeItem>
      </template>
    </ul>
    <ul v-else class="flex justify-between py-12 xl:py-32">
      <template
        v-for="{ uspType, isHiddenOnMobile, text, icon, isAssemblyFree } in options"
      >
        <SanityUSPBarSmallItem
          v-if="uspType === 'rating'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconStar class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p>
              <b>{{ reviewsObject && reviewsObject.rating }}</b> {{ $t('common.based_on') }} {{ reviewsObject && reviewsObject.count }} {{ $t('common.reviews') }}
            </p>
          </template>
        </SanityUSPBarSmallItem>

        <SanityUSPBarSmallItem
          v-if="uspType === 'freeDelivery'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconDelivery class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p class="inline-block" v-html="$t('usps.title2')" />
            <strong>{{ translatedRegion(currentRegion) }}</strong>
          </template>
        </SanityUSPBarSmallItem>

        <SanityUSPBarSmallItem
          v-if="uspType === 'freeReturns'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconReturn class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p v-html="$t('usps.title4')" />
          </template>
        </SanityUSPBarSmallItem>
        <SanityUSPBarSmallItem
          v-if="uspType === 'reviews'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconCustomers class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p v-html="$t('usps.title5')" />
          </template>
        </SanityUSPBarSmallItem>

        <SanityUSPBarSmallItem
          v-if="uspType === 'assemblyService'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <IconAssembly class="block w-[22px] h-[22px] text-offblack-600 mb-0 mr-8 md:mb-8 md:mr-0 lg:mb-0 lg:mr-8" />
          </template>
          <template #title>
            <p v-html="isAssemblyFree ? $t('usps.title3.free') : $t('usps.title3.optional')" />
          </template>
        </SanityUSPBarSmallItem>

        <SanityUSPBarSmallItem
          v-if="uspType === 'custom'"
          v-bind="{
            isHiddenOnMobile,
          }"
        >
          <template #start>
            <img
              class="block w-[22px] h-[22px] text-offblack-600 mb-0 md:mb-8 lg:mb-0 lg:mr-8"
              v-bind:src="$urlFor(icon).url()"
            >
          </template>
          <template #title>
            <p v-html="text" />
          </template>
        </SanityUSPBarSmallItem>
      </template>
    </ul>
  </section>
</template>

<script>
import { defineComponent, useStore, computed, ref, useFetch, useContext } from '@nuxtjs/composition-api';
import useRegions from '~/composables/useRegions';
import { INITIAL } from '~/utils/api';

export default defineComponent({
  props: {
    options: {
      type: Array,
      default: () => []
    },
    isBigVariant: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const data = ref();
    const reviewsHomepage = ref();
    const store = useStore();
    const { $axios } = useContext();
    const currentRegion = computed(() => store.getters['global/REGION_NAME']);
    const { translatedRegion } = useRegions();
    const reviewsUSP = computed(() => (props.options.find(usp => usp.uspType === 'rating')));
    const reviewsGlobal = computed(() => reviewsUSP.value && store.getters['global/REVIEWS'](reviewsUSP.value.ratingType));
    const reviewsObject = computed(() => ((reviewsUSP.value && reviewsUSP.value.homepage) ? reviewsHomepage.value : reviewsGlobal.value));

    if (reviewsUSP.value && reviewsUSP.value.homepage) {
      useFetch(async() => {
        try {
          data.value = await INITIAL.HOMEPAGE($axios);
          reviewsHomepage.value = { rating: data.value.reviewsAverageScore, count: data.value.reviewsCount };
        } catch (e) {
          $logException(e);
        }
      });
    }
    const showOptionalAssembly = () => {
      modal.show('optionalAssembly');
    };
    return {
      showOptionalAssembly,
      currentRegion,
      translatedRegion,
      reviewsHomepage,
      reviewsGlobal,
      reviewsObject
    };
  }
});
</script>
