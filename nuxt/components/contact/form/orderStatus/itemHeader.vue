<template>
  <div>
    <p
      class="bold-20 text-offblack-800 capitalize"
      v-html="title"
    />
    <ul class="normal-14 text-offblack-600 mt-8">
      <li
        v-if="itemId"
        class="mt-4"
        v-html="`${$t('contact.forms.order_status.item_id')} ${itemId}`"
      />
      <li
        v-if="color && !isSampleBox"
        class="mt-4"
        v-html="`${$t('contact.forms.order_status.colour')} ${color}`"
      />
      <li
        v-if="dimensions && !isSampleBox"
        class="mt-4"
        v-html="dimensions"
      />
      <li
        v-if="!isSampleBox"
        class="mt-4"
        v-html="`${$t('contact.forms.order_status.assembly')} ${assembly ? $t('contact.forms.order_status.assembly_company') : $t('contact.forms.order_status.assembly_client')}`"
      />
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      required: true
    },
    itemId: {
      type: Number,
      required: true
    },
    color: {
      type: String,
      default: ''
    },
    dimensions: {
      type: String,
      default: ''
    },
    assembly: {
      type: Boolean,
      required: true
    },
    isSampleBox: {
      type: Boolean,
      default: false
    }
  }
});
</script>
