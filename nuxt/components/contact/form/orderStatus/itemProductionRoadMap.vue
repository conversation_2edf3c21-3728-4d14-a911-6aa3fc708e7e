<template>
  <div class="mt-8">
    <ContactFormOrderStatusItemProductionStep
      v-for="(step, index) in steps"
      v-bind:key="index"
      v-bind="{
        isStepFinished: activeStepIndex > index,
        isActiveStep: index === activeStepIndex,
        isFirstStep: index === 0,
        isLastStep: index === steps.length - 1,
        label: step.label,
        additionalInfo: index === activeStepIndex ? step.additionalInfo : '',
        additionalLinkInfo: index === activeStepIndex ? step.additionalLinkInfo : {}
      }"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  useContext,
  useRoute
} from '@nuxtjs/composition-api';
import {findLastIndex} from 'lodash-es';
import {
  AS_MAX_STATUS,
  ASSEMBLY_SERVICE_STATUS,
  DELIVERY_TIME_FRAMES_STATUS,
  EMAIL24_STATUS,
  ORDER_STATUS,
  PRODUCT_STATUS
} from '~/composables/contact/useOrderStatusItem';

export default defineComponent({
  props: {
    isSampleBox: {
      type: Boolean,
      required: true
    },
    shouldDisplayFullDetails: {
      type: Boolean,
      required: true
    },
    currentStatus: {
      type: Number,
      required: true
    },
    productStatus: {
      type: Number,
      required: true
    },
    isAssemblyServiceMix: {
      type: Boolean,
      required: true
    },
    isAssemblyServiceMixNoAssembly: {
      type: Boolean,
      required: true
    },
    isDedicatedTransport: {
      type: Boolean,
      required: true
    },
    estimatedProductionDate: {
      type: String,
      required: true
    },
    deliveryTimeFrames: {
      type: Object,
      default: () => ({})
    },
    email24: {
      type: Object,
      default: () => ({})
    },
    assemblyService: {
      type: Object,
      default: () => ({})
    },
    trackingInfo: {
      type: Object,
      default: () => ({})
    },
    isComplaint: {
      type: Boolean,
      required: false,
    },
    asMaxStatus: {
      type: Number,
      default: AS_MAX_STATUS.EMTPY,
    },
    asMaxPlanningDate: {
      type: String,
      default: '',
    }
  },
  setup(props) {
    const {i18n} = useContext();
    const route = useRoute();
    const orderId = computed(() => route.value.query.order_id as string);
    const hasEmail24 = computed(() => props.email24 && Object.keys(props.email24).length > 0);
    const hasDeliveryTimeFrames = computed(() => props.deliveryTimeFrames && Object.keys(props.deliveryTimeFrames).length > 0);
    const hasAssemblyService = computed(() => props.assemblyService && Object.keys(props.assemblyService).length > 0);
    const hasTrackingInfo = computed(() => props.trackingInfo && Object.keys(props.trackingInfo).length > 0);
    const hasAsMaxStatus = computed(() => props.asMaxStatus !== AS_MAX_STATUS.EMTPY);

    const steps = computed(() => {
      if (props.isSampleBox && props.shouldDisplayFullDetails) {
        return getSampleBoxSteps();
      }

      if (hasAsMaxStatus.value) {
        return getDetailedItemStepsWithASMaxDelivery();
      }

      if (props.shouldDisplayFullDetails) {
        return getDetailedItemSteps();
      }

      return getCommonSteps();
    });

    const getDetailedItemSteps = () => [
      getPaymentStep(),
      getProductionStep(),
      ...hasEmail24.value ? getEmail24Steps() : [],
      ...hasDeliveryTimeFrames.value ? getDeliveryTimeFramesSteps() : [],
      ...hasAssemblyService.value ? getAssemblyServiceSteps() : [],
      getReadyToBeShippedStep(),
      getShippedStep(),
      getDeliveredStep()
    ];

    const getDetailedItemStepsWithASMaxDelivery = () => {
      const steps = [
        getPaymentStep(),
        getProductionStep(),
        getAsMaxPlanningStep(),
      ]
      if (props.asMaxStatus !== AS_MAX_STATUS.NEW) {
        steps.push(getAsMaxActionStep());
      }
      if (props.asMaxStatus !== AS_MAX_STATUS.DONE) {
        steps.push(getDeliveredStep());
      }
      return steps;
    }

    const activeStepIndex = computed(() => findLastIndex(steps.value, step => step.isActive === true));

    const getPaymentStep = () => ({
      label: i18n.t('contact.forms.order_status.delivery_timeline_status_payment_received'),
      isActive: [
        ORDER_STATUS.CANCELLED,
        ORDER_STATUS.DRAFT,
        ORDER_STATUS.PAYMENT_PENDING,
        ORDER_STATUS.PAYMENT_FAILED
      ].includes(props.currentStatus)
    });
    const getProductionStep = () => ({
      label: i18n.t('contact.forms.order_status.delivery_timeline_status_production'),
      isActive: [ORDER_STATUS.IN_PRODUCTION, ORDER_STATUS.TO_BE_SHIPPED].includes(props.currentStatus) &&
        !hasEmail24.value &&
        !hasDeliveryTimeFrames.value,
      ...[PRODUCT_STATUS.NEW, PRODUCT_STATUS.ASSIGNED_TO_PRODUCTION, PRODUCT_STATUS.IN_PRODUCTION].includes(props.productStatus)
        ? {additionalInfo: props.productStatus === PRODUCT_STATUS.NEW ? i18n.t('contact.forms.order_status.product_accepted_to_production') : i18n.t('contact.forms.order_status.product_in_production', {date: props.estimatedProductionDate})} : {}
    });

    const getAsMaxPlanningStep = () => {
      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_planning'),
        isActive: props.asMaxStatus === AS_MAX_STATUS.NEW
      }
    };

    const getAsMaxProposedDateStep = () => {
      if (hasAssemblyService.value) {
        return {
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_proposed_with_assembly', { proposed_date: props.asMaxPlanningDate}),
          isActive: props.asMaxStatus === AS_MAX_STATUS.WAITING_FOR_CLIENT_ACCEPTATION
        }
      }
      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_proposed', { proposed_date: props.asMaxPlanningDate}),
        isActive: props.asMaxStatus === AS_MAX_STATUS.WAITING_FOR_CLIENT_ACCEPTATION
      }
    };
    const getAsMaxAcceptedDateStep = () => {
      if (hasAssemblyService.value) {
        return {
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_accepted_with_assembly', { proposed_date: props.asMaxPlanningDate}),
          isActive: props.asMaxStatus === AS_MAX_STATUS.ACCEPTED
        }
      }
      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_accepted', { proposed_date: props.asMaxPlanningDate}),
        isActive: props.asMaxStatus === AS_MAX_STATUS.ACCEPTED
      }
    };

    const getAsMaxDeliveredStep = () => {
      if (hasAssemblyService.value) {
        return {
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_delivered_with_assembly', { proposed_date: props.asMaxPlanningDate}),
          isActive: props.asMaxStatus === AS_MAX_STATUS.DONE
        }
      }
      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_as_max_delivered', { proposed_date: props.asMaxPlanningDate}),
        isActive: props.asMaxStatus === AS_MAX_STATUS.DONE
      }
    };


    const getAsMaxActionStep = () => {
      if (props.asMaxStatus === AS_MAX_STATUS.WAITING_FOR_CLIENT_ACCEPTATION) {
        return getAsMaxProposedDateStep()
      }
      if (props.asMaxStatus === AS_MAX_STATUS.ACCEPTED) {
        return getAsMaxAcceptedDateStep()
      }
      return getAsMaxDeliveredStep()
    };

    const getEmail24Steps = () => {
      // CASE 1 - email24 created without delivery time frames with status NEW
      if (
        props.email24.status !== EMAIL24_STATUS.CONFIRMED &&
                    !(hasDeliveryTimeFrames.value && props.deliveryTimeFrames.status === DELIVERY_TIME_FRAMES_STATUS.NEW)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_take_survey'),
          isActive: true,
          additionalInfo: i18n.t('contact.forms.order_status.delivery_timeline_status_take_survey_body'),
          additionalLinkInfo: {
            label: i18n.t('contact.forms.order_status.delivery_timeline_status_take_survey_cta'),
            url: props.email24.url
          }
        }];
      }

      // CASE 2 - email24 filled with or without delivery time frames
      if (props.email24.status === EMAIL24_STATUS.CONFIRMED) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_survey_taken'),
          ...hasDeliveryTimeFrames.value || ![
            ORDER_STATUS.IN_PRODUCTION,
            ORDER_STATUS.TO_BE_SHIPPED
          ].includes(props.currentStatus)
            ? { isActive: false }
            : { isActive: true, additionalInfo: i18n.t('contact.forms.order_status.delivery_timeline_status_survey_taken_body') }
        }];
      }

      return [];
    };

    const getDeliveryTimeFramesSteps = () => {
      // CASE 1 - delivery time frames sent to client
      if ([
        DELIVERY_TIME_FRAMES_STATUS.NEW
      ].includes(props.deliveryTimeFrames.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_date'),
          isActive: true,
          additionalInfo: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_date_body'),
          additionalLinkInfo: {
            label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_date_cta'),
            url: props.deliveryTimeFrames.url
          }
        }];
      }

      // CASE 2 - delivery time frames filled by client - should be active only for fast track and SKU (order status still IN_PRODUCTION)
      if (![
        DELIVERY_TIME_FRAMES_STATUS.NEW,
        DELIVERY_TIME_FRAMES_STATUS.NEW_DATES_REQUESTED,
        DELIVERY_TIME_FRAMES_STATUS.CANCELLED
      ].includes(props.deliveryTimeFrames.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_date_picked'),
          isActive: props.currentStatus === ORDER_STATUS.IN_PRODUCTION
        }];
      }

      // CASE 3 - new dates requested by client
      if ([
        DELIVERY_TIME_FRAMES_STATUS.NEW_DATES_REQUESTED
      ].includes(props.deliveryTimeFrames.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_date'),
          isActive: true,
          additionalInfo: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_date_body_rejected')
        }];
      }

      return [];
    };

    const getAssemblyServiceSteps = () => {
      // CASE 1 - assembly service planning in progress
      if (
        [ASSEMBLY_SERVICE_STATUS.TO_BE_RELEASED, ASSEMBLY_SERVICE_STATUS.IN_PROGRESS, ASSEMBLY_SERVICE_STATUS.READY_TO_PROPOSE].includes(props.assemblyService.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_delivery_assembly_date'),
          isActive: true,
          additionalInfo: i18n.t('contact.forms.order_status.assembly_service_is_ready_additional_info')
        }];
      }

      // CASE 2 - assembly service proposed to client
      if (
        [ASSEMBLY_SERVICE_STATUS.PROPOSED_TO_CLIENT].includes(props.assemblyService.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_delivery_assembly_date'),
          isActive: true,
          additionalInfo: i18n.t(
            'contact.forms.order_status.delivery_timeline_status_pick_delivery_assembly_date_body',
            { subject: i18n.t('contact.forms.order_status.mail_assembly_service_choose_date_subject', { order_id: orderId.value }) }
          )
        }];
      }

      // CASE 2 - assembly service accepted by client
      if (
        [
          ASSEMBLY_SERVICE_STATUS.ACCEPTED_BY_CLIENT,
          ASSEMBLY_SERVICE_STATUS.COMPLETED
        ].includes(props.assemblyService.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_delivery_assembly_date_picked'),
          isActive: false
        }];
      }

      // CASE 3 - dates rejected by client
      if (
        [ASSEMBLY_SERVICE_STATUS.REJECTED_BY_CLIENT].includes(props.assemblyService.status)
      ) {
        return [{
          label: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_delivery_assembly_date'),
          isActive: true,
          additionalInfo: i18n.t('contact.forms.order_status.delivery_timeline_status_pick_delivery_assembly_date_body_rejected')
        }];
      }

      return [];
    };

    const getReadyToBeShippedStep = () => {
      const areDeliveryTimeFramesSelected = (
        hasDeliveryTimeFrames.value &&
                    [
                      DELIVERY_TIME_FRAMES_STATUS.DATES_CHOSEN,
                      DELIVERY_TIME_FRAMES_STATUS.ACCEPTED_BY_LOGISTICS,
                      DELIVERY_TIME_FRAMES_STATUS.PREPARED_BY_LOGISTICS,
                      DELIVERY_TIME_FRAMES_STATUS.FINISHED
                    ].includes(props.deliveryTimeFrames.status) &&
                    props.currentStatus === ORDER_STATUS.TO_BE_SHIPPED
      );

      const isAssemblyServiceDatePicked = (
        hasAssemblyService.value &&
                    [
                      ASSEMBLY_SERVICE_STATUS.ACCEPTED_BY_CLIENT,
                      ASSEMBLY_SERVICE_STATUS.COMPLETED
                    ].includes(props.assemblyService.status)
      );

      const isAssemblyServiceMix = props.isAssemblyServiceMixNoAssembly && props.currentStatus === ORDER_STATUS.TO_BE_SHIPPED;

      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_ready'),
        isActive: (
          areDeliveryTimeFramesSelected || isAssemblyServiceDatePicked || isAssemblyServiceMix
        ),
        ...props.isAssemblyServiceMixNoAssembly
          ? {
            additionalInfo: i18n.t('contact.forms.order_status.assembly_service_mix_additional_info')
          } : {}
      };
    };

    const getShippedStep = () => {
      let trackingInfo = {};

      if (props.isDedicatedTransport) {
        trackingInfo = {
          additionalInfo: i18n.t('contact.forms.order_status.dedicated_transport_additional_info')
        };
      } else if (hasTrackingInfo.value) {
        trackingInfo = {
          additionalInfo:
            props.trackingInfo.tracking_number &&
            `${i18n.t('contact.forms.order_status.tracking_number')} ${props.trackingInfo.tracking_number}`,
          additionalLinkInfo: {
            label: i18n.t('contact.forms.order_status.tracking_number_cta'),
            url: props.trackingInfo.url
          }
        };
      }

      return {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_shipping'),
        isActive: props.currentStatus === ORDER_STATUS.SHIPPED,
        ...trackingInfo
      };
    };

    const getDeliveredStep = () => ({
      label: hasAssemblyService.value
        ? i18n.t('contact.forms.order_status.delivered_assembled')
        : i18n.t('contact.forms.order_status.delivery_timeline_status_delivered'),
      isActive: props.currentStatus === ORDER_STATUS.DELIVERED
    });
    const getSampleBoxSteps = () => [
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_payment_received'),
        isActive: false
      },
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_production'),
        isActive: [
          ORDER_STATUS.IN_PRODUCTION,
          ORDER_STATUS.TO_BE_SHIPPED
        ].includes(props.currentStatus)
      },
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_shipping'),
        isActive: [
          ORDER_STATUS.SHIPPED
        ].includes(props.currentStatus)
      },
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_delivered'),
        isActive: [
          ORDER_STATUS.DELIVERED
        ].includes(props.currentStatus)
      }
    ];
    const getCommonSteps = () => [
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_payment_received'),
        isActive: false
      },
      {
        label: i18n.t('contact.forms.order_status.delivery_timeline_status_production'),
        isActive: [
          ORDER_STATUS.IN_PRODUCTION,
          ORDER_STATUS.TO_BE_SHIPPED,
          ORDER_STATUS.SHIPPED
        ].includes(props.currentStatus)
      }
    ];

    return {
      activeStepIndex,
      steps
    };
  }
});
</script>
