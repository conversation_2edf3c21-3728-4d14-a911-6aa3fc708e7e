<template>
  <div class="flex flex-col w-full">
    <h2
      v-if="$slots.title"
      class="bold-18 lg:bold-24 text-offblack-800 pb-8 lg:pb-0"
    >
      <slot name="title" />
    </h2>
    <BaseAccordeon
      v-for="(question, questionIndex) in data"
      v-bind:key="questionIndex"
      v-bind="{
        uniqueId: getUniqueTabId(questionIndex)
      }"
      class="text-gray-900"
    >
      <template #title>
        <h3
          class="normal-14 text-grey-900 lg:normal-18 py-16 lg:pb-0 pr-16 flex items-center min-h-28 box-border max-w-[500px]"
          v-html="question.question"
        />
      </template>
      <template #content>
        <div class="lg:pt-24 normal-12 lg:normal-14 text-grey-900">
          <template v-if="isArray(question.answer)">
            <p
              v-for="(subanswer, subanswerIndex) in question.answer"
              v-bind:key="`subanswer-${subanswerIndex}`"
              v-html="subanswer"
            />
          </template>
          <template v-else>
            <p v-html="question.answer" />
          </template>
        </div>
      </template>
    </BaseAccordeon>
    <slot name="footer" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api';
import { isArray } from 'lodash-es';

export default defineComponent({
  props: {
    uniqueId: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      default: () => ([]),
      validator: (data: Array<{question: string, answer: string }>) => (data.every(item => (!!item.question && !!item.answer)))
    },
    updateUrl: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const getUniqueTabId = (index:number) => `pd-${props.uniqueId}-tab-${index}`;

    return {
      isArray,
      getUniqueTabId
    };
  }
});
</script>
