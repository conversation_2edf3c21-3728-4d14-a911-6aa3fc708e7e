<template>
  <BaseButton
    class="flex justify-center items-center short-transition opacity-100 transition-opacity hover:opacity-70"
    v-bind="{
      'data-testid': 'cart-remove-item-button',
      variant: 'custom',
      trackData: cartEvent('ShowRemoveFromCartModal', itemId)
    }"
    v-on:click="$emit('removeItemModal')"
  >
    <IconBin 
      class="text-grey-900 mr-4"
      data-testid="cart-bin-icon"
    />
    <span class="normal-12 text-grey-900" v-html="$t('common.remove')" />
  </BaseButton>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import { CART_ANALYTICS } from '~/composables/useCart';

export default defineComponent({
  props: {
    itemId: {
      type: Number,
      required: true
    }
  },
  setup() {
    const { cartEvent } = CART_ANALYTICS();
    return {
      cartEvent
    };
  }
});
</script>
