<template>
  <div class="relative">
    <slot />
    <VPopover
      class="ml-8 flex justify-center items-center absolute top-[6px] right-[6px]"
    >
      <IconTooltip v-show="!disableTooltip" class="text-black cursor-pointer" />
      <div slot="popover" class="max-w-[300px]">
        <slot name="tooltipBody" />
      </div>
    </VPopover>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    disableTooltip: {
      type: Boolean,
      default: false
    }
  }
});
</script>
