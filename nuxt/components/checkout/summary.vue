<template>
  <Transition name="fade">
    <div
      v-if="!isNil(cartItems) && cartItems.length"
      class="md:border md:border-grey-800 md:rounded-6 md:p-16"
    >
      <div class="flex border-b border-grey-900 justify-between items-end pb-8">
        <h2
          class="bold-20 text-offblack-800"
          v-html="$t('cart.order_summary')"
        />
        <p
          v-if="isTabletOrDesktopViewport"
          class="normal-16 text-offblack-600"
          v-html="$tc('scart.items_count', cartItems.length)"
        />
      </div>
      <CheckoutItemsList
        v-if="isTabletOrDesktopViewport"
        v-bind="{
          cartItems
        }"
      />

      <CheckoutSummaryDetails
        v-bind:class="{ 'md-max:order-2': showItemListOnMobile }"
        v-bind="{
          displayPromoCode,
          displayPromoCodeRemoveButton,
          displayAssembly,
          displayAssemblyRemoveButton,
          disableHeading: !isTabletOrDesktopViewport && showItemListOnMobile,
          eventCategory: 'checkout',
        }"
      >
        <slot name="order" />
      </CheckoutSummaryDetails>
      <div
        v-bind:class="{ 'md-max:order-3': showItemListOnMobile }"
      >
        <slot />
      </div>

      <div class="mt-32 hidden md:block">
        <CheckoutFeaturesDesktop />
      </div>
    </div>
  </Transition>
</template>

<script>
import { defineComponent, useStore, computed } from '@nuxtjs/composition-api';
import { isNil } from 'lodash-es';
import useMq from '~/composables/useMq';

export default defineComponent({
  props: {
    displayPromoCode: {
      type: Boolean,
      default: true
    },
    displayAssembly: {
      type: Boolean,
      default: true
    },
    displayPromoCodeRemoveButton: {
      type: Boolean,
      default: true
    },
    displayAssemblyRemoveButton: {
      type: Boolean,
      default: true
    },
    showItemListOnMobile: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const cartItems = computed(() => getters['cart/ITEMS']);
    const { isTabletOrDesktopViewport } = useMq('md');
    const { getters } = useStore();

    return {
      isNil,
      cartItems,
      isTabletOrDesktopViewport
    };
  }
});
</script>