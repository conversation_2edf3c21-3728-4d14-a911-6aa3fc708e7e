<template>
  <section class="mt-24 lg:mt-32 -mx-16 md:mx-0">
    <BaseLink
      v-for="(order, index) in orders"
      v-bind:key="`order-status-${index}`"
      class="flex bg-grey-600 p-16 mt-4 md:max-w-[50%] rounded-6"
      v-bind="{
        href: order.check_status_link,
        variant: 'custom',
        trackData: { eventLabel: 'order-status-item', eventPath: order.check_status_link }
      }"
    >
      <IconOrderBox class="mt-4" />
      <div class="flex-1 ml-8">
        <p class="normal-20 text-offblack-800" v-html="$t('account.check_your_order_status')" />
        <p class="normal-16 text-offblack-600 mt-4">
          {{ $t('account.order_number') }}: <span v-html="order.id" />
        </p>
      </div>
      <IconChevronRight class="self-center" />
    </BaseLink>
  </section>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import IconOrderBox from '~/assets/icons/icon-order-box.svg?inline';
import IconChevronRight from '~/assets/icons/icon-chevron-right.svg?inline';

export default defineComponent({
  components: {
    IconChevronRight,
    IconOrderBox
  },
  props: {
    orders: {
      type: Array,
      default: () => ([])
    }
  },
  setup() {
    return {
      IconOrderBox,
      IconChevronRight
    };
  }
});
</script>
