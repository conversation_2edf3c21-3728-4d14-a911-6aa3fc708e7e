<template>
  <ClientOnly>
    <BaseModal
      v-bind="{
        name: 'cookiesSettings',
        'content-class': 'w-[90%] max-w-[500px] max-h-[90vh]',
        closeEvent
      }"
      v-on:modalOpened="fetchCookiesList"
    >
      <template #default="{ params }">
        <div class="flex flex-col max-h-[90vh]">
          <div class="pt-16 pb-12 px-16 pt-16 pb-12 mt-48">
            <div class="flex justify-between mb-16">
              <p
                class="bold-12 text-black"
                v-html="$t('cookiesmanager.modal.privacy_settings')"
              />
              <BaseLink
                target="_blank"
                class="bold-12 text-orange text-right"
                v-bind="{
                  href: $localeDjangoPath('privacy-policy'),
                  variant: 'custom',
                  trackData: { eventLabel: 'cta', eventPath: $localeDjangoPath('privacy-policy') }
                }"
                v-on:click="handleClickPrivacyPolicy"
              >
                <span v-html="$t('privacy_policy.header.title')" />
              </BaseLink>
            </div>
            <ToggleScroll
              v-model="activeTab"
              v-bind="{
                options: [
                  { label: $t('cookiesmanager.bar.button_settings'), value: 'settings' },
                  { label: 'Cookies', value: 'cookies' },
                ],
                'scroll-wrapper-classes': 'h-24',
                'toggle-wrapper-classes': 'bg-grey-600 w-full relative flex whitespace-nowrap capitalize h-24 rounded-12',
                'toggle-button-classes': 'px-20 flex items-center w-full bold-12 text-offblack-800 z-1',
                'toggle-active-button-classes': 'bold-12',
                'scroll-area-classes': '',
                'overlay-classes': 'absolute top-0 bottom-2 bg-white rounded-12 border-2 border-solid border-grey-800 transition-toggle duration-500 ease-in-out'
              }"
              v-on:input="changeActiveTab"
            />
          </div>
          <div class="px-16 overflow-y-auto">
            <div
              v-if="activeTab === 'settings'"
              v-bind:key="'settings'"
            >
              <CookiesManagerModalTabSettings />
            </div>
            <div
              v-else
              v-bind:key="'cookies'"
            >
              <CookiesManagerModalTabList v-bind="{ cookiesList }" />
            </div>
          </div>
          <div class="border-t border-grey-700 flex flex-col lg:flex-row justify-center items-center p-16">
            <BaseButton
              variant="outlined"
              class="mb-8 md:mb-0 md:mr-12"
              v-bind="{
                trackData: { eventLabel: 'save-cookies-settngs' },
                'data-testid': 'save cookies settings'
              }"
              v-on="{ click: handleSaveSettings }"
            >
              {{ $t('cookiesmanager.modal.save_settings') }}
            </BaseButton>

            <BaseButton
              variant="accent"
              v-bind="{
                trackData: { eventLabel: 'modal-accept-all-cookies' },
                'data-testid': 'modal accept all cookies'
              }"
              v-on="{ click: handleAcceptAllCookies }"
            >
              {{ $t('cookiesmanager.modal.accept_all') }}
            </BaseButton>
          </div>
        </div>
      </template>
    </BaseModal>
  </ClientOnly>
</template>

<script>
import { defineComponent, ref, useContext } from '@nuxtjs/composition-api';
import { COOKIES } from '~/utils/api';
import useCookies from '~/composables/useCookies';
import useModal from '~/composables/useModal';
import { useTrackingDOM } from '~/composables/useTracking';

export default defineComponent({
  props: {
    closeEvent: {
      type: Object,
      default: () => ({})
    }
  },
  setup() {
    const { $axios, $logException } = useContext();
    const { saveCookies } = useCookies();
    const { trackCookiebar } = useTrackingDOM();
    const modal = useModal();
    const activeTab = ref('settings');
    const isActive = ref(true);
    const fetchedData = ref();
    const cookiesList = ref();

    const fetchCookiesList = async() => {
      try {
        fetchedData.value = await COOKIES.LIST($axios);
        cookiesList.value = fetchedData.value.cookies;
      } catch (e) {
        $logException(e);
      }
    };

    const changeActiveTab = () => {
      trackCookiebar('consent_settings', `change_tab_${activeTab.value}`);
    };

    const handleSaveSettings = () => {
      saveCookies();
      modal.hide('cookiesSettings');
      trackCookiebar('consent_settings', 'save_settings');
    };

    const handleAcceptAllCookies = () => {
      saveCookies(true);
      modal.hide('cookiesSettings');
      trackCookiebar('consent', 'accept_all');
    };

    const handleClickPrivacyPolicy = () => {
      trackCookiebar('consent_settings', 'Privacy policy');
    };

    return {
      activeTab,
      isActive,
      changeActiveTab,
      handleSaveSettings,
      handleAcceptAllCookies,
      cookiesList,
      handleClickPrivacyPolicy,
      fetchCookiesList
    };
  }
});
</script>
