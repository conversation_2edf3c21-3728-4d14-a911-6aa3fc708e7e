<template>
  <section
    v-bind:id="id"
    class="container-cstm-fluid py-64"
    v-bind:class="(ctaCopy || secondaryCta) ? 'lg:pt-96 xl:pt-128' : 'lg:py-96 xl:py-128'"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 text-center md:text-left">
      <h2
        class="bold-24 lg:bold-32 xl:bold-46 md:pr-48 lg:col-start-2 lg:col-span-5 xl:-ml-32 xlg:ml-0"
        v-bind:class="textColor"
        v-html="title"
      />
      <p
        class="normal-16 lg:normal-20 mt-16 md:mt-4 lg:ml-24 xlg:ml-0 lg:col-span-5"
        v-bind:class="textColor"
        v-html="paragraph"
      />
    </div>
    <div
      v-if="ctaCopy || secondaryCta"
      class="flex flex-col items-center"
    >
      <BaseLink
        v-bind="{
          href: ctaUrl,
          variant: 'accent',
          trackData: { eventLabel: 'cta', eventPath: ctaUrl }
        }"
        class="mt-24 lg:mt-32 mb-16 lg:mb-24 lg:ty-btn--l"
      >
        {{ ctaCopy }}
      </BaseLink>
      <BaseLink
        variant="link"
        v-bind="{
          trackData: {
            eventLabel: 'secondaryCta'
          }
        }"
        class="text-[#E9E2D0] link link--arrow-down whitespace-nowrap block mx-auto "
        v-on="{ click: secondaryCtaCallback }"
      >
        {{ secondaryCta }}
      </BaseLink>
    </div>
  </section>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      required: true
    },
    id: {
      type: String,
      required: true
    },
    paragraph: {
      type: String,
      required: true
    },
    ctaUrl: {
      type: String,
      default: ''
    },
    ctaCopy: {
      type: String,
      default: ''
    },
    secondaryCtaCallback: {
      type: Function,
      default: () => ({})
    },
    secondaryCta: {
      type: String,
      default: ''
    },
    textColor: {
      type: String,
      default: 'text-[#E9E2D0]'
    }
  }
});
</script>
