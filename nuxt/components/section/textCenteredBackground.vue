<template>
  <section class="relative">
    <div class="h-full">
      <BasePicture
        img-classes="w-full h-full object-cover"
        v-bind="{
          alt: title,
          type: imageType,
          path: imagePath,
          isRetinaUploaded: false
        }"
      />
    </div>
    <div class="absolute w-full -translate-x-1/2 -translate-y-1/2 content top-1/2 left-1/2 z-1">
      <div class="grid grid-cols-12 container-cstm-fluid">
        <div class="col-span-12 text-center md:col-span-8 md:col-start-3 xl:col-span-6 xl:col-start-4">
          <h3
            v-if="subTitle"
            class="max-w-[755px] normal-16 uppercase mx-auto"
            v-bind:class="isLightTheme ? 'text-offwhite-600' : 'text-offblack-600'"
            v-html="subTitle"
          />
          <h2
            class="max-w-[755px] bold-32 mx-auto"
            v-bind:class="[
              subTitle ? 'lg:bold-46 py-16 lg:pb-32' : 'lg:bold-54 pb-24',
              isLightTheme ? 'text-offwhite-800' : 'text-offblack-600'
            ]"
          >
            <span v-html="title" />
            <IconCustomizeShelf
              v-if="withIcon"
              class="w-[34px] h-[34px] mx-2 inline"
            />
            <span v-html="secondaryTitle" />
          </h2>
          <BaseLink
            v-if="cta && ctaUrl"
            v-bind:variant="isLightTheme ? 'accent' : 'outlined'"
            v-bind="{ href: ctaUrl, trackData: { eventLabel: 'cta' } }"
          >
            {{ cta }}
          </BaseLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    secondaryTitle: {
      type: String,
      default: ''
    },
    imagePath: {
      type: String,
      required: true
    },
    cta: {
      type: String,
      default: ''
    },
    ctaUrl: {
      type: String,
      default: ''
    },
    withIcon: {
      type: Boolean,
      default: true
    },
    isLightTheme: {
      type: Boolean,
      default: true
    },
    subTitle: {
      type: String,
      default: ''
    },
    imageType: {
      type: String,
      default: 'M T SD LD'
    }
  }
});
</script>
