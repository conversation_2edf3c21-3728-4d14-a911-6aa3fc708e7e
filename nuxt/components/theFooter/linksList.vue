<template>
  <div>
    <p
      v-if="title"
      class="normal-16 text-grey-800 mb-32"
      v-html="title"
    />
    <ul class="grid grid-cols-2 lg:grid-cols-1 gap-y-12 lg:gap-y-0 auto-cols-max border-t border-solid border-grey-900 lg:border-0 pt-32 pb-16 lg:py-0">
      <li
        v-for="link in links"
        v-bind:key="link.attrs.pathKey"
        v-bind:class="link.itemExtraClasses"
      >
        <NavigationLink
          v-bind="{
            ...link.attrs,
            extraClasses : ' flex normal-14 md:normal-16 md:py-4 pr-2 text-offwhite-800 hover:text-orange transition-color short-transition text-left'
          }"
          v-on="link.listeners"
        />
      </li>
      <slot />
    </ul>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    },
    links: {
      type: Array,
      default: () => ([])
    }
  }
});
</script>
