<template>
  <ul>
    <li v-for="(page, index) in pages" v-bind:key="index">
      <NuxtLink
        class="px-16 py-8 rounded-2 overflow-hidden flex justify-between items-center"
        v-bind:class="[page.slug === slug ? 'bg-grey-900 text-offwhite-600' : 'text-offblack-800 hover:bg-grey-600']"
        v-bind="{
          to: `${fixedRoute}/${page.slug}`
        }"
      >
        <div class="flex lg-max:normal-16 normal-20">
          <span class="mr-4 lg:mr-8" v-html="`${page.orderId}.`" />
          <h4 v-html="page.title" />
        </div>
        <div class="lg:hidden">
          <IconArrow class="rotate-180 w-8 h-[14px] ml-4 lg:hidden" />
        </div>
      </NuxtLink>
    </li>
  </ul>
</template>
<script>
import { defineComponent, useRoute } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    pages: {
      type: Array,
      default: []
    },
    slug: {
      type: String,
      default: null
    }
  },
  setup() {
    const route = useRoute();

    const fixedRoute = route.value.matched[0].path;

    return {
      fixedRoute
    };
  }
});
</script>
