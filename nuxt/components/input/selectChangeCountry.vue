<template>
  <FormulateInput
    v-bind="{
      value,
      label,
      name,
      disabled,
      validationMessages,
      placeholder,
      validation,
      options: regionsForSelect,
      'input-class': ['country-select']
    }"
    type="select"
    v-on:input="val => $emit('input', val)"
  >
    <template #prefix>
      <UiCountryFlag
        v-if="value.length"
        v-bind="{
          regionName: value,
          class: 'absolute top-24 left-16'
        }"
      />
    </template>
  </FormulateInput>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api';
import useRegions from '~/composables/useRegions';

export default defineComponent({
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    validation: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    withOtherRegion: {
      type: Boolean,
      default: false
    },
    validationMessages: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const { regionsForSelect, regionsForSelectWithOtherRegion } = useRegions();
    return {
      regionsForSelect: props.withOtherRegion ? regionsForSelectWithOtherRegion : regionsForSelect
    };
  }
});
</script>
