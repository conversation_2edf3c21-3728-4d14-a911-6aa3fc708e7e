<template>
  <section
    class="fixed top-0 bottom-0 left-0 right-0 flex"
    v-bind:class="isCartOpened ? 'bg-black/60' : 'pointer-events-none'"
  >
    <ModalImagePreview />
    <Transition
      name="slide-in-out-right"
      v-on:before-enter="$dixa.setWidgetVisibility(false)"
      v-on:after-leave="$dixa.setWidgetVisibility(true)"
    >
      <SCartBar v-if="isCartOpened" />
    </Transition>
    <ModalCartItemRemove />
    <ModalFastTrack v-if="regionCode === 'NL'" />
    <ModalAssemblyAdd />
    <ModalRecycleTax v-if="regionCode === 'FR'" />
  </section>
</template>

<script>
import { computed, defineComponent, useStore } from '@nuxtjs/composition-api';
import useCartStatus from '~/composables/useCartStatus';

export default defineComponent({
  setup() {
    const { isCartOpened } = useCartStatus();
    const store = useStore();
    const regionCode = computed(() => store.getters['global/REGION_CODE']);

    return {
      isCartOpened,
      regionCode
    };
  }
});
</script>
