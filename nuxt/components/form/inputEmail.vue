<template>
  <FormInputBase
    v-bind="{
      ...$props,
      errorMessages: emailErrorMessages,
      validators,
      value
    }"
    ref="inputBase"
    input-type="email"
    v-on:input="(val) => $emit('input', val)"
  >
    <slot />
  </FormInputBase>
</template>

<script>
import { ref, useContext } from '@nuxtjs/composition-api';
import { isEmpty, isEmail } from '~/composables/form/validators';

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    showErrors: {
      type: Boolean,
      default: true
    },
    watchInput: {
      type: Boolean,
      default: true
    },
    showErrorsOnSubmit: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      default: ''
    },
    errorMessages: {
      type: Array,
      default: () => ([])
    },
    wrapperStyles: {
      type: Array,
      default: () => ([])
    },
    inputStyles: {
      type: Array,
      default: () => ([])
    },
    labelStyles: {
      type: Array,
      default: () => ([])
    },
    errorStyles: {
      type: Array,
      default: () => ([])
    },
    placeholder: {
      type: String,
      default: ''
    },
    inputName: {
      type: String,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    }

  },

  setup(props) {
    const { i18n } = useContext();
    const inputBase = ref(null);
    const emailErrorMessages = ref([]);
    const validators = [isEmpty, isEmail];
    const formSubmission = value => (inputBase.value.formSubmission(value));
    const setExternalError = value => (inputBase.value.setExternalError(value));
    const focusInput = () => (inputBase.value.focusInput());

    if (!props.errorMessages.length) {
      emailErrorMessages.value = [i18n.t('common.validation.required'), i18n.t('common.validation.email_invalid')];
    } else {
      emailErrorMessages.value = props.errorMessages;
    }

    return {
      validators,
      inputBase,
      formSubmission,
      setExternalError,
      focusInput,
      emailErrorMessages,
      inputType: 'email'
    };
  }
};
</script>
