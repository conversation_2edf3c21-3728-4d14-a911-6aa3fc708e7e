import { useAsync, useContext } from '@nuxtjs/composition-api';
import { INITIAL } from '~/utils/api';

export default function(space: String) {
  const { $logException, $axios } = useContext();

  const data = useAsync(async() => {
    try {
      return await INITIAL.SPACE($axios, space);
    } catch (e) {
      $logException(e);

      return null;
    }
  }, `${space}`);

  return {
    data
  };
}
