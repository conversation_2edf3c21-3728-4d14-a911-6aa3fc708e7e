/* <PERSON>ie consent manager generator (run in cstm main directory)

cd frontend_src && npm run prod:cookie && cd .. \
&& mkdir nuxt/static/nuxt-statics/generated \
&& cp src/frontend_cms/static/dist_vue/vueConsents*.js nuxt/static/nuxt-statics/vueConsents.js \
&& cp src/frontend_cms/static/dist/css/style.css nuxt/static/nuxt-statics/generated/style.css \
&& cp src/frontend_cms/static/dist/css/ds.css nuxt/static/nuxt-statics/generated/ds.css \
&& cat nuxt/static/nuxt-statics/generated/ds.css nuxt/static/nuxt-statics/generated/style.css > nuxt/static/nuxt-statics/generated/common.css \
&& cd nuxt/ && purgecss --config ./purge.config.js && cd .. \
&& cp src/frontend_cms/static/dist_vue/vueConsents*.css nuxt/static/nuxt-statics/generated/vueConsents.css \
&& cat nuxt/static/nuxt-statics/generated/common-purge.css nuxt/static/nuxt-statics/generated/vueConsents.css > nuxt/static/nuxt-statics/generated/vueConsents.scss \
&& echo '.cookie-consent-wrapper {' | cat - nuxt/static/nuxt-statics/generated/vueConsents.scss > temp && mv temp nuxt/static/nuxt-statics/generated/vueConsents.scss && echo '}' >> nuxt/static/nuxt-statics/generated/vueConsents.scss \
&& sass --no-source-map nuxt/static/nuxt-statics/generated/vueConsents.scss nuxt/static/nuxt-statics/vueConsents.css \
&& rm -r nuxt/static/nuxt-statics/generated

*/

/* vueFrontContact (run in cstm main directory)

cd frontend_src && npm run prod:contact && cd .. \
&& mkdir nuxt/static/nuxt-statics/generated \
&& cp src/frontend_cms/static/dist_vue/vueFrontContact*.js nuxt/static/nuxt-statics/vueFrontContact.js \
&& cp src/frontend_cms/static/dist/css/style.css nuxt/static/nuxt-statics/generated/style.css \
&& cp src/frontend_cms/static/dist/css/ds.css nuxt/static/nuxt-statics/generated/ds.css \
&& cat nuxt/static/nuxt-statics/generated/ds.css nuxt/static/nuxt-statics/generated/style.css > nuxt/static/nuxt-statics/generated/common.css \
&& cd nuxt/ && purgecss --config ./purge.config.js && cd .. \
&& cp src/frontend_cms/static/dist_vue/vueFrontContact*.css nuxt/static/nuxt-statics/generated/vueFrontContact.css \
&& cat nuxt/static/nuxt-statics/generated/common-purge.css nuxt/static/nuxt-statics/generated/vueFrontContact.css > nuxt/static/nuxt-statics/generated/vueFrontContact.scss \
&& echo '.vue-contact-wrapper {' | cat - nuxt/static/nuxt-statics/generated/vueFrontContact.scss > temp && mv temp nuxt/static/nuxt-statics/generated/vueFrontContact.scss && echo '}' >> nuxt/static/nuxt-statics/generated/vueFrontContact.scss \
&& sass --no-source-map --style compressed nuxt/static/nuxt-statics/generated/vueFrontContact.scss nuxt/static/nuxt-statics/vueFrontContact.css \
&& rm -r nuxt/static/nuxt-statics/generated

*/

module.exports = {
  content: ['./static/nuxt-statics/vueFrontContact.js'],
  css: ['./static/nuxt-statics/generated/common.css'],
  output: './static/nuxt-statics/generated/common-purge.css',
  defaultExtractor: content => content.match(/[A-Za-z0-9-_:\/]+/g) || []
};
