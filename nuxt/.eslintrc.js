module.exports = {
  root: true,
  ignorePatterns: ['sanity/'],
  extends: ['@nuxtjs/eslint-config-typescript'],
  rules: {
    semi: ['error', 'always'],
    'max-len': [
      'error',
      {
        code: 200,
        ignoreUrls: true,
        ignoreComments: true,
        ignoreStrings: true
      }
    ],
    'vue/no-v-html': 'off',
    'linebreak-style': ['error'],
    'vue/v-bind-style': ['error', 'longform'],
    'vue/v-on-style': ['error', 'longform'],
    'vue/object-curly-spacing': ['error', 'always'],
    'no-console': ['error', { allow: ['warn', 'error'] }],
    'vue/max-attributes-per-line': [
      'error',
      {
        singleline: 1
      }
    ],
    'space-before-function-paren': ['error', 'never'],
    'vue/padding-line-between-blocks': ['error'],
    'padding-line-between-statements': ['error',
      { blankLine: 'always', prev: 'multiline-block-like', next: '*' },
      { blankLine: 'always', prev: '*', next: 'multiline-block-like' }
    ],
    'vue/component-name-in-template-casing': ['error', 'PascalCase', {
      registeredComponentsOnly: false
    }]
  },
  overrides: [
    {
      files: ['components/icon/*.vue'],
      rules: {
        'max-len': 'off'
      }
    }
  ]
};
