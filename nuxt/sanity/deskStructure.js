import * as Structure from '@sanity/document-internationalization/lib/structure';
import S from '@sanity/desk-tool/structure-builder';
const { list, listItem, editor, documentTypeList } = S;

/**
 * Defines views/tabs for each content type.
 */
//
// export default () => S.list()
//   .title('Content')
//   .items([
//     S.listItem()
//       .title('FAQ')
//       .child(
//         S.documentList()
//           .title('FAQ')
//           .schemaType('pageFaq')
//             .filter('_type == "pageFaq" && __i18n_lang == "en"')
//       ),
//     S.divider(),
//     S.listItem()
//       .title('Rooms pages')
//       .child(
//         S.documentList()
//           .title('Rooms pages')
//           .schemaType('page')
//           .filter('_type == "pageRoom" && __i18n_lang == "en"')
//       ),
//     S.listItem()
//       .title('Pages')
//       .child(
//         S.documentList()
//           .title('Rooms pages')
//           .schemaType('page')
//           .filter('_type == "page" && __i18n_lang == "en"')
//       ),
//   ]);

const exclude = ['faqArticle', 'faqTag', 'faqCategory', 'pageFaq', 'pageTerms', 'InstaImage', 'miniGridBoardData', 'breaks'];

export default () => S.list().title('Content').items(
[
  ...Structure.getFilteredDocumentTypeListItems().filter(({id}) => !exclude.includes(id)),
  S.divider(),
  ...Structure.getFilteredDocumentTypeListItems().filter(({id}) =>
    id == "pageFaq"  || id == "pageTerms"
  ),
  S.divider(),
  ...Structure.getFilteredDocumentTypeListItems().filter(({id}) =>
    id == "InstaImage" || id == "miniGridBoardData" || id == "breaks"
  )]
)

// export default Structure.default;
