import { RiFolderLine } from 'react-icons/ri';

export default {
  name: 'page',
  title: 'Pages',
  type: 'document',
  icon: RiFolderLine,
  i18n: true,
  initialValue: {
    __i18n_lang: 'en'
  },
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'seo',
      title: 'Seo'
    }
  ],
  fields: [
    {
      name: 'pageUrl',
      type: 'string',
      title: 'Page URL',
      group: 'content',
      description: 'Page url (starts with "/", e.g. /rooms/bedroom )',
      validation: Rule => Rule.required()
    },
    {
      name: 'name',
      type: 'string',
      title: 'Page name',
      group: 'content',
      description: 'Unique page name (same for all languages)',
      validation: Rule => Rule.required()
    },
    {
      name: 'pageBuilder',
      type: 'array',
      title: 'Page sections',
      group: 'content',
      description: 'Select page sections',
      of: [
        { type: 'SectionAnchor' },
        { type: 'SectionCategoryGrid' },
        { type: 'SectionExperience' },
        { type: 'SectionExplore' },
        { type: 'SectionFaq' },
        { type: 'SectionHero' },
        { type: 'SectionHeroVideo' },
        { type: 'SectionImageUsps' },
        { type: 'SectionInstafeed' },
        { type: 'SectionMiddlePics' },
        { type: 'SectionPromo' },
        { type: 'SectionReviews' },
        { type: 'SectionRoomsExplorer' },
        { type: 'SectionSeo' },
        { type: 'SectionSideboardsHp' },
        { type: 'SectionSpaces' },
        { type: 'SectionSteps' },
        { type: 'SectionStepsWithIcons' },
        { type: 'SectionTestimonials' },
        { type: 'SectionTextImageColumns' },
        { type: 'SectionThreeVideos' },
        { type: 'SectionUspBarHomepage' },
        { type: 'SectionHeroSale' },
        { type: 'SectionNewsletterHero' },
        { type: 'SectionTextCenteredButton' },
        { type: 'SectionTextVideoColumns' },
        { type: 'SectionMinigrid' },
        { type: 'SectionCentredText' }
        // etc...
      ]
    },
    {
      name: 'seoTitle',
      title: 'Meta title',
      type: 'string',
      group: 'seo',
      validation: Rule => Rule.required()
    },
    {
      name: 'seoDescription',
      title: 'Meta description',
      type: 'text',
      group: 'seo',
      validation: Rule => Rule.required()
    },
    {
      name: 'seoImage',
      title: 'Image',
      type: 'image',
      group: 'seo',
      description: 'og:image',
      validation: Rule => Rule.required()
    }
  ]
};
