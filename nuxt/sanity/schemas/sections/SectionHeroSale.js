export default {
  name: 'SectionHeroSale',
  type: 'object',
  title: 'Section Hero Sale',
  preview: {
    select: {
      title: 'sectionName'
    }
  },
  fields: [
    {
      name: 'sectionName',
      type: 'string',
      title: 'Section name',
      hidden: true,
      initialValue: 'Section Hero Sale'
    },
    {
      name: 'isThemeLight',
      type: 'boolean',
      title: 'is theme light',
      initialValue: true
    },
    {
      name: 'bgColor',
      type: 'color',
      title: 'Background color',
      initialValue: '#A98B78'
    },
    {
      name: 'imagePath',
      type: 'string',
      title: 'Image path',
      initialValue: ''
    },
    {
      name: 'imageAlt',
      type: 'string',
      title: 'imageAlt',
      initialValue: '',
      validation: Rule => Rule.custom((field, {parent}) => (parent.imagePath !== '' && !field ) ? "This field must not be empty if imagePath filled." : true)
    },
    {
      name: 'heading',
      type: 'string',
      title: 'Heading text',
      initialValue: ''
    },
    {
      name: 'subheading',
      type: 'string',
      title: 'subheading',
      initialValue: ''
    },
    {
      name: 'paragraph',
      type: 'string',
      title: 'paragraph',
      initialValue: ''
    },

    {
      name: 'showCopyButton',
      type: 'boolean',
      title: 'Show copy code button',
      initialValue: true
    },
    {
      name: 'promoCode',
      type: 'string',
      title: 'promoCode',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showCopyButton) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showCopyButton
    },
    {
      name: 'ctaCopy',
      type: 'string',
      title: 'ctaCopy',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showCopyButton) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showCopyButton
    },
    {
      name: 'ctaCopied',
      type: 'string',
      title: 'ctaCopied',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showCopyButton) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showCopyButton
    },
    {
      name: 'ctaDefault',
      type: 'string',
      title: 'ctaDefault',
      initialValue: '',
      hidden: ({ parent }) => parent?.showCopyButton
    },
    {
      name: 'ctaUrl',
      type: 'string',
      title: 'ctaUrl',
      initialValue: '',
      hidden: ({ parent }) => parent?.showCopyButton
    },

    {
      name: 'showScrollToButton',
      type: 'boolean',
      title: 'Show scroll to buttton',
      initialValue: true
    },
    {
      name: 'scrollText',
      type: 'string',
      title: 'scrollText',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showScrollToButton) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showScrollToButton
    },
    {
      name: 'scrollToElId',
      type: 'string',
      title: 'Scroll to anchor id',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showScrollToButton) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showScrollToButton
    },
    {
      name: 'showRibbon',
      type: 'boolean',
      title: 'showRibbon',
      initialValue: false
    },

    {
      name: 'ribbonText',
      type: 'string',
      title: 'ribbonText',
      initialValue: '',
      validation: Rule => Rule.custom((value, context) => {
        if (context.parent.showRibbon) {
          return !!value === false ? 'Required field' : true;
        }
        return true;
      }),
      hidden: ({ parent }) => !parent?.showRibbon
    }
  ]
};
