<template>
  <main class="py-32 xl:py-40">
    <div class="grid-container">
      <h1 class="bold-28 md:bold-32 xl:bold-54">
        Investor Relations
      </h1>
      <InvestorRelationsNavigationTabs v-model="activeTab" />
      <TransitionGroup name="fade">
        <div
          v-show="activeTab == 'press releases'"
          v-bind:key="1"
        >
          <InvestorRelationsPressReleasesList />
        </div>
        <div
          v-show="activeTab == 'reports'"
          v-bind:key="2"
        >
          <InvestorRelationsReports class="mt-24" />
        </div>
      </TransitionGroup>
    </div>
  </main>
</template>

<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api';
import { deepMerge } from '~/utils/helpers';

export default defineComponent({
  middleware: 'investorRelations',
  setup() {
    const activeTab = ref('press releases');

    return {
      activeTab
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });

    return deepMerge(i18nHead, {
      title: 'Tylko: Investor relations',
      meta: [
        {
          hid: 'og:title',
          name: 'og:title',
          content: 'Tylko: Investor relations'
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('hp.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: 'Tylko: Investor relations'
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('hp.meta.description')
        }
      ]
    });
  }
});
</script>

<style scoped>
.fade-leave-active {
  display: none;
}
</style>
