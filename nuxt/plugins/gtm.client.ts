import { Context, NuxtConfig } from '@nuxt/types';
import { Store } from 'vuex';
import Cookies from 'universal-cookie';
import gtmGlobalEvents from '~/helpers/gtmGlobalEvents';

export default function(context: Context) {
  const { $gtm, $config, store, i18n }: { $gtm?: any, $config: NuxtConfig, store: Store<any>, i18n: any } = context;
  const id = $config && $config.gtm && $config.gtm.id;
  const { pageView } = gtmGlobalEvents(context);
  const userId = store.getters['global/USER_ID'];
  const sessionId = store.getters['global/SESSION_ID'];
  const globalSessionId = store.getters['global/GLOBAL_SESSION_ID'];
  const userEmail = store.getters['global/USER_HASH_EMAIL'];
  const userEmailNoHmac = store.getters['global/USER_HASH_EMAIL_NO_HMAC'];
  const cookies = new Cookies();
  const globalSessionIdCookie = cookies.get('global_session_id');

  const langCode = i18n.locale.split('-')[0];

  if ($gtm && $gtm.init && id) {
    $gtm.init(id);
  }

  $gtm.push({
    userId,
    sessionId,
    globalSessionId: globalSessionId || globalSessionIdCookie,
    event: 'authentication',
    userLanguage: langCode,
    userEmail,
    ea: userEmailNoHmac
  });

  const previousURL = sessionStorage.getItem('previousURL');
  pageView(previousURL, window.location.href, true);
}
