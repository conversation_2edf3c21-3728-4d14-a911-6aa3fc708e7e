@layer components {
  .ty-link-s {
    @apply inline-flex items-center relative cursor-pointer semibold-14;

    svg {
      @apply w-20 h-20 mr-8;

      path {
        @apply fill-current;
      }
    }

    span {
      @apply relative inline-flex;
      @apply after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-1 after:bg-current;
      @apply after:transform after:scale-x-100 after:origin-left;
      @apply after:transition-transform after:ease-out after:duration-300;
    }

    &:hover,
    .ty-link-parent:hover & {
      span {
        @apply after:origin-right after:scale-x-0;
      }
    }

    &:active {
      span {
        @apply after:scale-x-100 after:origin-left;
      }
    }

    &.ty-link-disabled {
      @apply opacity-[0.38] cursor-not-allowed;

      &:hover {
        span {
          @apply after:scale-x-100 after:origin-left;
        }
      }
    }
  }

  .ty-link-xs {
    @apply ty-link-s semibold-12;

    span {
      @apply after:bottom-[-1px];
    }

    svg {
      @apply w-16 h-16;
    }
  }

  .ty-link-m {
    @apply ty-link-s semibold-16;

    span {
      @apply after:bottom-[-2px];
    }

    svg {
      @apply w-24 h-24;
    }
  }

  .ty-link-l {
    @apply ty-link-s semibold-18;

    span {
      @apply after:bottom-[-3px];
    }

    svg {
      @apply w-24 h-24;
    }
  }
}


.ty-link--underline {
    span {
      @apply relative inline-flex;
      @apply after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-1 after:bg-current;
      @apply after:transform after:scale-x-0 after:origin-right;
      @apply after:transition-transform after:ease-out after:duration-300;
    }

    &:hover,
    .ty-link-parent:hover & {
      span {
        @apply after:origin-left after:scale-x-100;
      }
    }

    &:active, &.active {
      span {
        @apply after:scale-x-100 after:origin-left;
      }
    }

    &.ty-link-disabled {
      @apply opacity-[0.38] cursor-not-allowed;

      &:hover {
        span {
          @apply after:scale-x-100 after:origin-left;
        }
      }
    }
}
