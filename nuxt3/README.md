# Nuxt 3 Minimal Starter

Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install the dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## ENV

Copy example .env file and edit it to set proper values

```bash
cp .env.example .env
vim .env
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm run build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm run preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## Tylko packages
Uruchomienie nowego konfiguratora wymaga wygenerowania klucza dostępu do naszych paczek.
W folderze nuxt3 dodaj plik o nazwie: .npmrc i wklej ponizszy kod:

```
@tylkocom:registry= "https://npm.pkg.github.com/"
//npm.pkg.github.com/:_authToken=${TYLKO_PKG_INSTALL_TOKEN}
```

Do .bash_profile, .zshrc_profile dodaj zmienną o nazwie TYLKO_PKG_INSTALL_TOKEN i przypisz do niej wygenerowany klucz
Mozesz równiez dodać klucz bezpośrednio do pliku .npmrc


Klucz wygenerujesz na stronie github w zakładce tokens.
Do klucza dodaj opcję: read:packages
```
https://github.com/settings/tokens
```
