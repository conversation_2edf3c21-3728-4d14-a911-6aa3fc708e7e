<template>
  <div>
    <BaseLink
      v-bind:event="isDesktopViewport ? '' : 'click'"
      v-bind:href="backRoute || $addLocaleToPath('faq')"
      variant="link"
      v-bind:track-data="{ eventLabel: 'back_to_faq' }"
      class="flex mb-24 lg:mb-8 lg-max:bg-offwhite-600 pt-48 pb-28 -mx-16 px-16 md:-mx-32 md:px-16 lg:mx-0 lg:p-0 lg:cursor-auto"
    >
      <div>
        <IconArrow class="rotate-0 block w-12 h-24 mx-[14px] mt-4 lg:hidden" />
      </div>
      <h2
        class="bold-24 lg:normal-24 text-offblack-700 text-left"
        v-html="title"
      />
    </BaseLink>
    <div
      v-if="content"
      class="faq-article-content normal-16 text-offblack-600"
      v-html="md.render(content)"
    />
    <div
      v-if="faq_bottom_buttons"
      class="text-offblack-600 mt-12 mb-24"
    >
      <div class="flex flex-col md:flex-row lg-max:justify-center items-center">
        <div
          v-for="(button, index) in faq_bottom_buttons"
          v-bind:key="index"
        >
          <BaseButton
            v-if="button.isLiveChatButton"
            variant="custom"
            class="ml-12 bold-12 py-12"
            v-bind="{
              trackData: {
                eventLabel: 'open_live_chat'
              }
            }"
            v-on:click="openLiveChat"
          >
            <span v-html="button.copy" />
            <IconArrow class="w-6 h-8 inline-block rotate-180 -translate-y-2" />
          </BaseButton>
          <BaseLink
            v-else
            variant="outlined"
            v-bind="{
              href: button.url,
              trackData: {
                eventLabel: 'go_to_account'
              }
            }"
          >
            {{ button.copy }}
          </BaseLink>
        </div>
      </div>
    </div>
    <slot name="relatedArticles" />
    <ClientOnly>
      <TheFAQRateArticle
        data-section="faq-rate-article"
        v-bind="{
          categorySlug: categorySlug,
          articleSlug: slug
        }"
      />
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup>

import markdownit from 'markdown-it';
import useMq from '@/composables/useMq';
import scrollToElement from '~/helpers/scrollToElement';

declare global {
  interface Window {
    scrollToFAQContactForm: (() => void) | null;
  }
}

const props = withDefaults(defineProps<{
  article: FaqArticle;
  backRoute: string;
  categorySlug: string;
}>(), {
  backRoute: '', 
  categorySlug: ''
});

const { title, content, faq_bottom_buttons, slug } = props.article;

const { isDesktopViewport } = useMq('md');

const route = useRoute();
const md = markdownit();
const articleId = computed(() => route.params.article);
const { $dixa, $logException } = useNuxtApp();

const scrollToTop = () => {
  if (isDesktopViewport) {
    const el = document.getElementById('faq-nuxt-container');

    if (el && el.getBoundingClientRect().y < 0) {
      scrollToElement({
        element: '#faq-nuxt-container',
        offset: 20
      });
    }
  }
};

const openLiveChat = () => {
  try {
    $dixa.toggleWidget(true);
  } catch (e) {
    console.error(e);
    $logException(e);
  }
};

watch(articleId, () => {
  scrollToTop();
});

onMounted(() => {
  scrollToTop();

  window.scrollToFAQContactForm = () => {
    scrollToElement({
      element: '#faq-contact-form-container'
    });
  };
});

onUnmounted(() => {
  window.scrollToFAQContactForm = null;
});

</script>

<style lang="scss">
.faq-article-content {
  p + p {
    margin-top: 1em;
  }

  a {
    @apply text-orange;
  }
}
</style>
