<template>
  <div>
    <div class="mb-24 lg:mb-8 lg-max:bg-offwhite-600 pt-48 pb-28 -mx-16 px-16 md:-mx-32 md:px-16 lg:mx-0 lg:p-0">
      <BaseLink
        v-bind:event="isDesktopViewport ? '' : 'click'"
        v-bind:href="$addLocaleToPath('faq')"
        class="flex items-center  lg:cursor-auto"
        variant="link"
        v-bind:track-data="{}"
      >
        <div class="flex items-center">
          <div class="lg:hidden">
            <IconArrow class="rotate-0 w-12 h-24 mx-[14px]" />
          </div>
          <h1
            class="inline bold-24 lg:bold-46 lg:mb-8 text-offblack-700"
            v-html="name"
          />
        </div>
      </BaseLink>
      <p class="normal-16 text-grey-900 lg-max:ml-40 lg-max:mt-4">
        {{ topicsCount }} {{ $t('faq.related_topics') }}
      </p>
    </div>
    <ul
      v-if="topics"
      class="mb-48"
    >
      <li
        v-for="(topic, index) in topics"
        v-bind:key="`${index}`"
      >
        <BaseLink
          v-bind:href="$addLocaleToPath('faq') + `tags/${route.params.id}/${topic.slug}`"
          class="flex justify-between lg:justify-start items-center mt-24 text-offblack-800"
          variant="link"
          v-bind:track-data="{}"
        >
          <h4
            class="normal-16"
            v-html="topic.title"
          />
          <div>
            <IconArrow class="rotate-180 w-8 h-[14px] ml-12" />
          </div>
        </BaseLink>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>

import useMq from '@/composables/useMq';
const route = useRoute();

const props = withDefaults(defineProps<{
  name: string;
  topics: FaqTag[];
}>(), {
  name: '',
  topics: () => []
});

const { isDesktopViewport } = useMq('md');
const topicsCount = computed(() => props.topics.length);
</script>
