<template>
  <div
    ref="el"
    class="basic-transition transition-max-height overflow-hidden"
    v-bind:class="[{ [maxHeightClass]: modelValue }, overlayClasses]"
  >
    <UiSelectorWithPlusIcon
      v-bind="{
        isChecked: modelValue,
        withPlusIcon: true,
        content: title,
        selector,
        selectorClasses,
        variant,
      }"
      class="cursor-pointer"
      v-on:click.native="() => handleClick()"
    />
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useTrackInteraction } from '~/composables/useTracking';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  modelValue: {
    type: Boolean,
    required: true
  },
  selector: {
    type: String,
    default: 'h3'
  },
  selectorClasses: {
    type: String,
    default: 'normal-24 text-offblack-800 mb-12'
  },
  overlayClasses: {
    type: String,
    default: 'relative after:content-[\'\'] after:absolute after:bottom-0 after:left-0 after:right-0 after:bg-offwhite-600 after:w-full after:h-16'
  },
  maxHeightClass: {
    type: String,
    default: '!max-h-[600px]'
  },
  variant: {
    type: String,
    default: 'right'
  },
  trackData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits<{
  'update:model-value': [value: boolean]
}>();

const el = ref();
const trackInteraction = useTrackInteraction(el);

const handleClick = () => {
  trackInteraction(props.trackData);
  emit('update:model-value', !props.modelValue);
};

</script>
