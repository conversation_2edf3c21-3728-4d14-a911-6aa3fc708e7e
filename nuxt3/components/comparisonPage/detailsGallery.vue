<template>
  <div
    class="grid grid-cols-2 gap-16 col-span-2"
  >
    <div
      v-for="(productLine, index) in availableProductLines"
      v-bind:key="`styles-header-${productLine}`"
    >
      <div
        class="grid grid-cols-2 gap-16"
      >
        <div
          class="col-span-2"
        >
          <ComparisonPageDetailsImage
            class=""
            v-bind="{
              color: [comparisonColorWithDefaultValue1, comparisonColorWithDefaultValue2, comparisonColorWithDefaultValue3][index],
              detailPath: galleryDetailMatrix[0][index],
            }"
          />
        </div>

        <ComparisonPageDetailsImage
          v-bind="{
            aspectRatioClasses: 'aspect-square md:aspect-[4/3]',
            color: [comparisonColorWithDefaultValue1, comparisonColorWithDefaultValue2, comparisonColorWithDefaultValue3][index],
            detailPath: galleryDetailMatrix[1][index],
          }"
        />
        <ComparisonPageDetailsImage
          v-bind="{
            aspectRatioClasses: 'aspect-square md:aspect-[4/3]',
            color: [comparisonColorWithDefaultValue1, comparisonColorWithDefaultValue2, comparisonColorWithDefaultValue3][index],
            detailPath: galleryDetailMatrix[2][index],
          }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import useComparisonPage from '~/composables/comparisonPage/useComparisonPage';
const { galleryDetailMatrix } = storeToRefs(useComparisonPageStore());
defineProps({
  thirdColumn: {
    type: Boolean,
    default: false
  }
});
const {
  availableProductLines,
  comparisonColorWithDefaultValue1,
  comparisonColorWithDefaultValue2,
  comparisonColorWithDefaultValue3
} = useComparisonPage();

</script>
