<template>
  <section class="py-64 lg:py-96">
    <h2
      class="normal-16-2 px-16 mb-8 uppercase text-center"
      v-bind:class="isThemeLight ? 'text-offwhite-600' : 'text-offblack-800'"
      v-html="title"
    />
    <BaseCarousel
      is-scrollbar
      v-bind="{
        options: {
          slidesPerView: 'auto',
          freeMode: true
        },
        name: `${name}-instafeedCarousel`
      }"
    >
      <BaseCarouselSlide
        v-for="item in filteredInstafeeds"
        v-bind:key="item.id"
        is-fluid-container
        class="mt-32 transition-transform basic-transition transform hover:translate-y-[-5px] max-w-[calc(100%-90px)] md:max-w-[230px] lg:max-w-[320px]"
      >
        <InstafeedCard v-bind="{ item, isThemeLight }" />
      </BaseCarouselSlide>
    </BaseCarousel>
  </section>
</template>

<script setup lang="ts">
import { instafeeds } from '~/utils/instafeeds';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  isThemeLight: {
    type: Boolean,
    default: true
  },
  ids: {
    type: Array,
    required: true
  },
  name: {
    type: String,
    default: ''
  }
});

const filteredInstafeeds = Array.from(instafeeds).filter(item => props.ids.includes(item.id));
</script>
