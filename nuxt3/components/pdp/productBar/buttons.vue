<template>
  <aside
    class="flex items-center gap-[10px] lg-max:w-full lg:min-w-max lg-max:justify-between"
    v-bind:class="{
      'lg-max:flex-row-reverse': !isNewConfigurator || isUserComesFromFacebook,
    }"
  >
    <BaseButton
      class="ty-btn--m h-[36px] py-0 sm-max:w-1/2 lg-max:w-full"
      v-bind:class="[AB_TESTS_NAVIGATION_2025 && 'lg:h-48 [&__span]:lg:semibold-16']"
      data-testid="navigation-add-to-wishlist"
      variant="custom"
      v-bind="{
        class: isUserComesFromFacebook ? 'lg-max:ty-btn-accent lg:ty-btn-outlined' : 'ty-btn-outlined',
        trackData: {
          eventLabel: 'addToWishListStickyBar'
        },
        title: $t('floating_section.cta'),
      }"
      v-on="{ click: addToWishlist }"
    >
      <IconHeart class="ty-icon--m" />
      <span
        class="semibold-14 !leading-1"
        v-html="$t('common.configurator.save_for_later')"
      />
    </BaseButton>
    <BaseButton
      class="ty-btn--m h-[36px] py-0 sm-max:w-1/2 lg-max:w-full"
      v-bind:class="[AB_TESTS_NAVIGATION_2025 && 'lg:h-48 [&__span]:lg:semibold-16']"
      data-testid="navigation-add-to-cart"
      variant="custom"
      v-bind="{
        class: isUserComesFromFacebook ? 'lg-max:ty-btn-outlined lg:ty-btn-accent' : 'ty-btn-accent',
        trackData: {
          eventLabel: 'addToCartStickyBar'
        },
        title: $t('common.configurator.add_to_cart'),
      }"
      v-on="{ click: addToCart }"
    >
      <IconTrolley class="ty-icon--m transition-all basic-transition" />
      <span
        class="semibold-14 !leading-1"
        v-html="$t('common.configurator.add_to_cart')"
      />
    </BaseButton>
  </aside>
</template>

<script setup lang="ts">
defineProps({
  buttonIsVisible: {
    type: Boolean,
    required: true
  },
  isNewConfigurator: {
    type: Boolean,
    required: true
  }
});

const { IS_USER_COMES_FROM_FACEBOOK: isUserComesFromFacebook, AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());

const addToCart = () => {
  (document.querySelector('.add-to-cart-button') as HTMLElement || document.querySelector('[data-testid="a2c-button"]') as HTMLElement)?.click();
};

const addToWishlist = () => {
  (document.querySelector('.save-for-later') as HTMLElement || document.querySelector('[data-testid="s4l-button"]') as HTMLElement)?.click();
};
</script>
