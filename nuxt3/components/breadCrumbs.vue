<template>
  <div>
    <BaseLink
      v-if="links.length > 1"
      as-nuxt-link
      variant="custom"
      v-bind="{
        href: links?.[links.length - 2]?.url,
        trackData: { eventLabel: 'register-or-login-back' },
      }"
      class="md:hidden inline-flex items-center bold-12 text-offblack-600 hover:text-offblack-600"
    >
      <IconArrowRight class="mr-8 transform rotate-180 w-20 h-20" />
      {{ links?.[links.length - 2]?.label }}
    </BaseLink>
    <div class="hidden md:flex items-center ">
      <BaseLink
        v-for="(link, index) in links"
        v-bind:key="link.url"
        class="last:text-neutral-700 normal-14 block pl-12 first:pl-0
                after:ml-12 after:align-middle after:inline-block after:w-4 after:h-4 after:pointer-events-none after:bg-[currentColor] last:after:hidden"
        variant="custom"
        v-bind="{
          href: index < links.length - 1 ? link.url : '',
          asNuxtLink: link.isNuxtLink,
          trackData: {
            eventLabel: 'bread-crumb-link',
            eventAction: `click_${link.label}`,
          },
        }"
      >
        {{ link.label }}
      </BaseLink>
    </div>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    links: Array<{ label: string; url: string; isNuxtLink: boolean }>;
  }>(),
  {}
);
</script>
