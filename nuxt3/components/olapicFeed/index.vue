<template>
  <section
    v-if="media"
    ref="sectionElement"
    class="mt-64 lg:mt-120 pb-64 md:pb-112 overflow-x-hidden text-offblack-900"
  >
    <UiHeadline
      v-bind="{
        label: 'Instagram',
        title: 'Get inspired by our community',
      }"
    >
      <template #title-button>
        <BaseLink
          variant="outlined"
          class="lg-max:hidden lg:col-span-3 justify-self-end self-center"
          data-testid="explore-more"
          v-bind="{
            trackData: {},
            href: $addLocaleToURL('/gallery/'),
          }"
        >
          {{ $t('common.explore_more') }}
        </BaseLink>
      </template>
    </UiHeadline>
    <div class="grid-container flex flex-col justify-center mt-32 lg:mt-48">
      <div class="flex flex-nowrap">
        <BaseLink
          v-for="(photo, index) in media"
          ref="cards"
          variant="custom"
          class="max-w-[280px] md:max-w-[calc(100%/3-32px/3)] lg2:max-w-[calc(100%/4-48px/4)] mr-16 last:mr-0 shrink-0
                 w-full bg-white rounded-12 overflow-hidden block"
          v-bind="{
            trackData: {},
            key: `_${index}-${photo.id}`,
            href: $addLocaleToURL(`/gallery/#opi${photo.id}`),
          }"
        >
          <div class="p-12 pt-16 md:p-16 md:pt-24 flex items-center gap-12 semibold-12 text-black">
            <img
              v-bind="{
                src: photo.avatar,
                alt: photo.alt,
                class: 'w-32 rounded-full aspect-square overflow-hidden object-cover'
              }"
            >
            {{ photo.name }}
          </div>
          <img
            v-bind="{
              src: photo.img,
              alt: photo.alt,
              class: 'w-full aspect-square overflow-hidden object-cover'
            }"
          >
          <div class="p-12 pt-16 md:p-16 pb-24 flex items-center justify-start gap-12 text-black">
            <IconInstagramHeart class="w-24 h-24" />
            <IconInstagramComment class="w-24 h-24" />
            <IconInstagramMessage class="w-24 h-24" />
            <div class="flex-1" />
            <IconInstagramSave class="w-24 h-24" />
          </div>
        </BaseLink>
      </div>
      <BaseLink
        variant="outlined"
        class="lg:hidden mt-32 mx-auto"
        data-testid="explore-more-mobile"
        v-bind="{
          trackData: {},
          href: $addLocaleToURL('/gallery/'),
        }"
      >
        {{ $t('common.explore_more') }}
      </BaseLink>
    </div>
  </section>
</template>

<script setup lang="ts">
import { isNil } from 'lodash-es';
import { useMounted, useElementVisibility } from '@vueuse/core';

const sectionElement = ref<HTMLElement>();
const cards = ref<HTMLElement[]>([]);
const isSectionVisible = useElementVisibility(sectionElement);
let rafId = null;

const isMounted = useMounted();
let startTimeStamp;
let pausedTimeStamp;
let pausedTotalTime = 0;
const carouselSpeed = 0.15;

function animateFrame (timestamp) {
  if (isNil(startTimeStamp)) {
    startTimeStamp = timestamp;
  }

  const elapsed = timestamp - startTimeStamp - pausedTotalTime;
  const width = (cards.value[0].$el.offsetWidth + 16);
  const loopWidth = cards.value.length * width;

  const shift = (-carouselSpeed * elapsed) % loopWidth;
  cards.value.forEach((card) => {
    let x = shift;

    if (card.$el.offsetLeft + shift + width < 0) {
      x += loopWidth;
    }

    card.$el.style.transform = `translateX(${x}px)`;
  });

  if (isSectionVisible.value) {
    rafId = requestAnimationFrame(animateFrame);
  }
}

watch(isSectionVisible, (isVisible) => {
  if (isVisible && !rafId && isMounted.value) {
    if (pausedTimeStamp) {
      pausedTotalTime += performance.now() - pausedTimeStamp;
    }

    rafId = requestAnimationFrame(animateFrame);
  } else if (!isVisible && rafId) {
    pausedTimeStamp = performance.now();
    cancelAnimationFrame(rafId);
    rafId = null;
  }
});

const { data } = await useFetch('https://photorankapi-a.akamaihd.net/customers/220749/media/recent?auth_token=b71903afda0e586b72842e785c1df447b159f040714c8cb63aa6ae8b2eb41470&version=v2.2');

const media = computed(() => data.value?.data?._embedded?.media.map((item: any) => ({
  img: item.images.normal,
  alt: item.caption,
  avatar: item._embedded.uploader.avatar_url,
  name: item._embedded.uploader.name,
  id: item.id
})));

</script>

<style scoped lang="scss">

</style>
