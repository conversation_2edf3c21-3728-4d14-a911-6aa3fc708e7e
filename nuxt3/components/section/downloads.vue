<template>
  <section class="py-64 md:py-96 xl:pt-128 bg-white">
    <div class="container-cstm-fluid">
      <h3
        class="bold-24 xl:bold-32 mb-24 md:mb-40 text-offblack-700"
        v-html="$t('lp.press.downloads.heading')"
      />
      <div class="grid grid-cols-12 gap-16">
        <div
          v-for="(item, index) in downloads"
          v-bind:key="`downloads-${index}`"
          class="col-span-12 md:col-span-4"
          v-bind:class="{ 'pt-48 md:pt-0': index !== 0 }"
        >
          <h3
            class="text-offblack-600 bold-20 xl:bold-24 pb-8 md:pb-16 border-b"
            v-html="item.title"
          />
          <BaseLink
            v-for="(file, i) in item.files"
            v-bind:key="i"
            target="_blank"
            rel="noopener noreferrer"
            class="border-b border-grey-800 text-offblack-600 py-16 md:pt-24 px-16 normal-16 flex items-center h-[75px] transition-color basic-transition hover:text-orange"
            v-bind="{
              href: file.url,
              variant: 'custom',
              trackData: { eventLabel: file.name, eventPath: file.url }
            }"
          >
            <IconDownload class="w-[35px]" />
            <span
              class="px-16 download-item__title"
              v-html="file.name"
            />
            <span
              class="ml-auto download-item__size"
              v-html="file.size"
            />
          </BaseLink>
        </div>
      </div>
      <h3 class="text-center col-span-9 lg:col-span-6 bold-20 md:bold-24 lg:bold-32 text-offblack-700 mt-48 lg:mt-80 xl:mt-112">
        <span v-html="$t('lp.press.downloads.title')" />
        <BaseLink
          class="text-orange"
          v-bind="{
            href: 'mailto:<EMAIL>',
            variant: 'custom',
            trackData: { eventLabel: 'press mail', eventPath: '<EMAIL>' }
          }"
        >
          <EMAIL>
        </BaseLink>
      </h3>
      <p
        class="col-span-12 sm:col-span-10 lg:col-span-10 sm:col-start-2 lg:col-start-2 normal-16 md:normal-20 text-grey-900 text-center mt-40 lg:mt-64"
        v-html="$t('lp.press.downloads.description')"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
const { t } = useI18n();

const downloads = [
  {
    title: t('lp.press.downloads.section1.title'),
    files: [
      {
        name: t('lp.press.downloads.section1.meet_the_founders'),
        size: '58MB',
        url: 'https://drive.google.com/drive/folders/1QABEtLCrpSGcmMtBq9Sli8iZ2G8AHikA?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section1.the_tylko_logo'),
        size: '367KB',
        url: 'https://drive.google.com/drive/folders/1qp6G28uhrQSseROi3pWmjd3cuYmYIvQi?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section1.news'),
        size: '9MB',
        url: 'https://drive.google.com/drive/folders/1ZgSiHeV5R8kjrsmn8W1PW8gDX9KAtnuw?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section1.creative_collaborations'),
        size: '201MB',
        url: 'https://drive.google.com/drive/folders/1KVs6LTICI4hmO8owK-KQ1d3Cep_P0fyx?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section1.sustainability_report'),
        size: '14MB',
        url: 'https://drive.google.com/drive/folders/1zlOlcLD8Brs5RJ2qRw12nlMHW1MnBqLk?usp=sharing'
      }
    ]
  },
  {
    title: t('lp.press.downloads.section2.title'),
    files: [
      {
        name: t('common.category.t01p'),
        size: '216MB',
        url: 'https://drive.google.com/drive/folders/1tqBtsG6r8pbndXCutF7x6w3QLH4d-wCK?usp=sharing'
      },
      {
        name: t('common.category.t01v'),
        size: '198MB',
        url: 'https://drive.google.com/drive/folders/1UxMiRmVQqf4MpAy8S9aqjXbMcJ1q0Kj8?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section2.t01_wall_storage'),
        size: '707MB',
        url: 'https://drive.google.com/drive/folders/18YP-5PK0z_tzkd0x6x2IIGGepidHS1IS?usp=sharing'
      },
      {
        name: t('common.type02'),
        size: '257MB',
        url: 'https://drive.google.com/drive/folders/1J8mjyiEuOFoGClqrR_1ICRDRcmkVTsdT?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section2.t02_matte_black'),
        size: '244MB',
        url: 'https://drive.google.com/drive/folders/16LnY26CXA-1x3MJhrBIzK3i9E0tXLuLy'
      },
      {
        name: t('lp.press.downloads.section2.t02_new_colors'),
        size: '1.2GB',
        url: 'https://drive.google.com/drive/folders/1tFvVLA9N5WL720ZaxP2gOzkf6vWyGd5n'
      },
      {
        name: t('lp.press.downloads.section2.t03_wardrobe'),
        size: '772MB',
        url: 'https://drive.google.com/drive/folders/1OqaKGOrMeGlJ8HEwDXURcIxUqNieutUM?usp=sharing'
      }
    ]
  },
  {
    title: t('lp.press.downloads.section3.title'),
    files: [
      {
        name: t('lp.press.downloads.section3.the_tylko_app'),
        size: '39MB',
        url: 'https://drive.google.com/drive/folders/1ONs317SoJz1zGBD7wXcFRdQ73BZbqR4t?usp=sharing'
      },
      {
        name: t('lp.press.downloads.section3.tylko_online_configurator'),
        size: '68MB',
        url: 'https://drive.google.com/drive/folders/1cibEh19Odjz6CZIDzINZ_dqTvl3RVD01?usp=sharing'
      }
    ]
  }
];
</script>
