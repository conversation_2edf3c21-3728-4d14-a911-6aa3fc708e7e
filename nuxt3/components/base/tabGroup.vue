<template>
  <fieldset class="flex gap-x-32">
    <label
      v-for="(option, index) in options"
      v-bind:key="`option-${index}`"
      class="cursor-pointer relative text-neutral-700
              after:block after:mt-8 after:border-b-2 after:w-0 after:border-b-transparent transition-all after:basic-transition
              has-[:checked]:after:w-[70%] has-[:checked]:text-neutral-900 has-[:checked]:after:border-b-neutral-900"
    >
      <input
        v-model="model"
        v-bind:value="option.value"
        type="radio"
        v-bind:name="uniqName"
        class="hidden"
      >
      <span
        class="bold-14 uppercase"
        v-html="option.label"
      />
    </label>
  </fieldset>
</template>

<script setup lang="ts">
const model = defineModel<number>();
defineProps<{
  options:Array<{ label:string, value:number }>,
}>();
const uniqName = useId();

</script>
