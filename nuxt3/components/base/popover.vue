<template>
  <component
    v-bind:is="as"
    ref="rootEl"
    v-on:mouseover="isPopoverVisible = true"
    v-on:mouseout="isPopoverVisible = false"
  >
    <slot />

    <Teleport
      v-if="isPopoverVisible"
      to="body"
    >
      <div
        ref="popoverRef"
        v-bind:class="popoverExtraClasses"
        v-bind:style="popoverStyle"
      >
        <slot name="popover" />
        <span
          v-if="withArrow"
          ref="floatingArrow"
          class="inline-block w-8 h-8 translate-y-1/2 rotate-45"
          v-bind="{
            class: arrowExtraClasses,
            style: {
              position: 'absolute',
              left:
                middlewareData.arrow?.x != null
                  ? `${middlewareData.arrow.x}px`
                  : '',
              [placement]:
                middlewareData.arrow?.y != null
                  ? `${middlewareData.arrow.y}px`
                  : '100%',
            }
          }"
        />
      </div>
    </Teleport>
  </component>
</template>

<script setup lang="ts">

import {
  type Placement,
  useFloating,
  arrow,
  offset,
  autoUpdate,
  detectOverflow
} from '@floating-ui/vue';

const props = withDefaults(defineProps<{
  placement?: Placement;
  popoverExtraClasses?: string;
  arrowExtraClasses?: string;
  withArrow?: boolean;
  offsetValue?: number;
  as?: string;
}>(), {
  placement: 'top',
  popoverExtraClasses: '',
  arrowExtraClasses: '',
  withArrow: false,
  offsetValue: 6,
  as: 'div'
});

const isPopoverVisible = ref(false);
const rootEl = ref(null);
const popoverRef = ref(null);
const floatingArrow = ref(null);

const { floatingStyles: popoverStyle, middlewareData, placement } = useFloating(rootEl, popoverRef, {
  whileElementsMounted: autoUpdate,
  placement: props.placement,
  middleware: [
    offset(props.offsetValue),
    {
      name: 'overflow-x-detect',
      async fn (state) {
        const overflow = await detectOverflow(state, { rootBoundary: 'document', padding: 5 });
        const offset = {
          x: state.x,
          y: state.y
        };
        if (overflow.left > 0) { offset.x += overflow.left; }
        if (overflow.right > 0) { offset.x -= overflow.right; }
        return offset;
      }
    },
    arrow({ element: floatingArrow })
  ]
});

</script>
