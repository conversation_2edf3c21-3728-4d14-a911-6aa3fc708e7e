<template>
  <div class="pt-24">
    <ScartSimpleOption
      v-if="format(cart.totalPriceBeforeDiscount)"
      class="mb-12 !py-0"
      v-bind="{
        title: $t('cart.subtotal'),
        value: format(cart.totalPriceBeforeDiscount),
        leftStyles: 'block normal-16 text-neutral-900',
        rightStyles: 'block normal-16 text-neutral-900'
      }"
    />
    <ScartSimpleOption
      class="mb-12 !py-0"
      v-bind="{
        title: $t('scart.delivery'),
        value: format(0),
        leftStyles: 'block normal-16 text-neutral-900',
        rightStyles: 'block normal-16 text-neutral-900'
      }"
    />
    <p
      v-if="displayDeliveryNotice"
      class="normal-12 text-grey-900"
      v-html="$t('scart.delivery_time.notice')"
    />
    <ScartSimpleOption
      v-if="displayAssemblyPrice && cart.cartUsedAssembly"
      class="mb-12 !py-0"
      v-bind="{
        title: $t('scart.shelf_assembly_service'),
        value: format(cart.orderPricing?.assembly_price),
        leftStyles: 'block normal-16 text-neutral-900',
        rightStyles: 'block normal-16 text-neutral-900'
      }"
    />
    <ScartSimpleOption
      v-if="cart.promocodeName"
      class="mb-12 !py-0"
      v-bind="{
        title: `${$t('checkout_discount.code_name')} ${cart.promocodeName?.toUpperCase()}`,
        value: `-${format(cart.orderPricing?.discount_value)}`,
        leftStyles: 'block normal-16 text-neutral-900',
        rightStyles: 'block normal-16 text-neutral-900'
      }"
    />
    <ScartSimpleOption
      v-if="cart.hasT03"
      class="mb-12 !py-0"
      v-bind="{
        title: $t('scart.wardrobe_assembly_tone'),
        value: format(0),
        leftStyles: 'block normal-16 text-neutral-900',
        rightStyles: 'block normal-16 text-neutral-900'
      }"
    />
    <slot name="promocode" />
    <slot name="assembly" />

    <div
      v-if="format(cart.totalPrice)"
      class="mt-16 flex justify-between pt-16 border-t border-neutral-400 pb-4"
    >
      <h4
        class="semibold-20 text-neutral-900"
        v-html="$t('scart.total')"
      />
      <div class="flex items-end">
        <p
          class="semibold-20 text-neutral-900"
          data-testid="total-price"
          v-html="format(cart.totalPrice)"
        />
      </div>
    </div>
    <div class="text-right">
      <p
        v-if="global.regionCode !== 'FR'"
        class="normal-12 text-neutral-700 text-right md:mt-4"
        v-html="$t('checkout.form.vat_included', {
          vat: cart.orderPricing?.vat_percentage_value,
          netto: format(cart.orderPricing?.total_price_netto)
        })"
      />
      <p
        v-if="global.regionCode === 'FR'"
        class="normal-12 text-grey-900 inline-block"
        v-html="$t('scart.vat_included')"
      />
      <BaseButton
        v-if="global.regionCode === 'FR'"
        variant="custom"
        class="ml-4 normal-12 text-orange underline cursor-pointer inline-block"
        v-bind="{
          trackData: cartEvent('ShowEcoPartModal'),
          'data-testid': 'cart-ecopart-show'
        }"
        v-on:click="() => showRecycleTaxModal()"
      >
        <UiDotsLoader
          v-if="isPendingState"
          class="ml-8 text-orange w-56 !static !inline-flex gap-4 !translate-x-0 !translate-y-0"
          bounce-class="!w-8 !h-8 bg-orange"
        />

        <span
          v-else
          v-html="`${$t('scart.eco-fee')}`"
        />
      </BaseButton>
    </div>
    <CheckoutNewsletter
      v-if="displayNewsletter && isDesktopViewport && formDisplay"
      v-bind="{
        shouldRegisterNewsletterToForm: true
      }"
    />
    <ModalRecycleTax
      v-model="isRecycleTaxModalOpen"
      v-bind="{
        ...ecoTaxModalData
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { CART_ANALYTICS, handleRecycleTax } from '~/composables/useCart';
import usePrice from '~/composables/usePrice';
import { useScartStore } from '~/stores/scart';
import { useGlobal } from '~/stores/global';
const { isDesktopViewport } = useMq('md');

const props = defineProps({
  displayPromoCode: {
    type: Boolean,
    default: true
  },
  displayAssemblyRemoveButton: {
    type: Boolean,
    default: true
  },
  eventCategory: {
    type: String,
    default: 'cart_edit'
  },
  displayNewsletter: {
    type: Boolean,
    default: false
  },
  displayAssemblyPrice: {
    type: Boolean,
    default: false
  },
  displayDeliveryNotice: {
    type: Boolean,
    default: false
  }
});

const cart = useScartStore();
const global = useGlobal();
const { format } = usePrice();
const { cartEvent } = CART_ANALYTICS(props.eventCategory);
const { formDisplay } = useAdyenDropin();

const {
  ecoTaxModalData,
  isRecycleTaxModalOpen,
  showRecycleTaxModal,
  isPendingState
} = handleRecycleTax();

</script>
