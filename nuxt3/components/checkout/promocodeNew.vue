<template>
  <div ref="promocodeInput">
    <Transition
      name="slide-fade"
      mode="out-in"
    >
      <div
        v-if="cart.cartUsedPromo"
        key="used"
        class="flex items-center"
      >
        <BaseButton
          v-if="displayPromoCodeRemoveButton"
          variant="custom"
          type="button"
          class="box-content"
          style="--spinner-color: #1D1E1F;"
          v-bind="{
            trackData: {},
            pending: loading,
            'data-testid': 'cart-promocode-remove'
          }"
          v-on="{ click: removePromocode }"
        >
          <template #icon>
            <div class="rounded-full bg-neutral-900 w-24 h-24">
              <IconPlus class="w-[24px] h-[24px] mr-8 text-black transform rotate-45 text-white" />
            </div>
          </template>
        </BaseButton>
        <div class="ml-16">
          <span
            class="normal-16 text-neutral-900 py-16"
            v-html="$t('checkout.code_name')"
          />:
          <span
            class="ml-4 text-neutral-900 uppercase"
            data-testid="promo-code-applied"
            v-html="cart.promocodeName"
          />
        </div>
      </div>
      <FormKit
        v-else
        v-model="formValues"
        type="form"
        v-bind="{
          config: { validationVisibility: 'submit' },
          disabled: loading,
          actions: false,
          incompleteMessage: false,
        }"
        v-on:submit="submitForm"
      >
        <FormKit
          id="cart-promocode"
          type="tyText"
          data-testid="cart-promocode-input"
          name="promocode"
          v-bind="{
            label: $t('checkout_discount.code_box'),
            validationMessages:{
              required: $t('scart.notify.promo_invalid'),
            }
          }"
          clear-class="hidden"
          inner-class="flex"
          validation="required"
          autocomplete="off"
        >
          <template #button>
            <BaseButton
              variant="accent"
              class="rounded-4 semibold-16 -m-[1px] relative"
              type="submit"
              data-testid="cart-submit-promocode"
              v-bind="{
                disabled: loading ,
                trackData: {},
              }"
            >
              <UiDotsLoader
                v-if="loading"
                class="[&.spinner>div]:!h-[10px] [&.spinner>div]:!w-[10px] [&.spinner]:!space-x-2"
                bounce-class="bg-white"
              />
              <span v-bind:class="{ 'invisible' : loading }">
                {{ $t('scart.promocode_apply') }}
              </span>
            </BaseButton>
          </template>
        </FormKit>
      </FormKit>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { CART_ANALYTICS } from '~/composables/useCart';
import { useTrackInteraction } from '~/composables/useTracking';
import { useScartStore } from '~/stores/scart';

const props = defineProps({
  displayPromoCodeRemoveButton: {
    type: Boolean,
    default: true
  },
  eventCategory: {
    type: String,
    required: true
  },
  disablePromocodeAddition: {
    type: Boolean,
    default: false
  }
});

const formValues = ref({ promocode: '' });
const loading = ref(false);
const toast = useToast();
const cart = useScartStore();
const { cartEvent } = CART_ANALYTICS(props.eventCategory);
const { $i18n } = useNuxtApp();
const { fetchUserStatus } = useCartStatus();
const promocodeInput = ref(null);
const trackInteraction = useTrackInteraction(promocodeInput);

const isPromocodeInputVisible = ref(false);

const submitForm = async (_, node) => {
  loading.value = true;

  try {
    const result = await handlePromo(formValues.value.promocode);
    await fetchUserStatus();

    result?.status === 'ok' && trackInteraction(cartEvent('AddPromocode'));
    hendleResultMessage(result, node);
  } catch (e) {
    hendleResultMessage({ message: $i18n.t('common.error.connection') }, node);
  } finally {
    loading.value = false;
  }
};

const handlePromo = async (promocode = '') => {
  return await $fetch<{status: string, message:string}>(`/api/v1/check_promo/${promocode ? `${promocode}/` : ''}`, {
    method: 'post',
    ignoreResponseError: true
  });
};

const removePromocode = async () => {
  loading.value = true;

  try {
    const result = await handlePromo();
    result?.status === 'ok' && trackInteraction(cartEvent('RemovePromocode'));
    hendleResultMessage(result);
    await fetchUserStatus();
  } catch (e) {
    hendleResultMessage();
  } finally {
    loading.value = false;
  }

  isPromocodeInputVisible.value = false;
  formValues.value.promocode = '';
};

const hendleResultMessage = (result?: {status?: string, message?: string}, node?: any) => {
  if (result?.status === 'ok') {
    result?.message && toast.success(result?.message);
  } else if (result?.message) {
    node?.setErrors([], { promocode: result?.message });
    toast.error(result?.message);
  } else {
    toast.error($i18n.t('common.error.connection'));
  }
};
</script>
