<template>
  <aside>
    <BaseModal v-model="isModalOpen">
      <DialogPanel class="fixed top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 w-[90%] max-w-[600px] max-h-[90vh] bg-white py-32 px-48 rounded-24 z-1">
        <button
          class="box-content absolute top-0 right-0 p-12 group z-1 custom"
          v-on:click="closeModal"
        >
          <IconClose class="w-32 h-32 transition-rotate basic-transition hover:-rotate-45" />
        </button>
        <DialogTitle class="semibold-32 text-offblack-800">
          {{ $t('checkout.payment_failed_modal.title') }}
        </DialogTitle>
        <DialogDescription class="mt-32">
          <p
            class="mb-8 semibold-16"
            v-html="$t('checkout.payment_failed_modal.subtitle')"
          />
          <p
            class="normal-16 text-offblack-800 whitespace-break-spaces"
            v-html="$t('checkout.payment_failed_modal.body')"
          />

          <div class="flex justify-between mt-32 md-max:flex-col gap-8 text-center">
            <BaseLink
              variant="outlined"
              v-bind="{
                target:'_blank',
                href: $addLocaleToPath('contact'),
                trackData: { eventLabel: 'modal-paymentfailed-btn-contact' },
                'data-testid': 'paymentfailed-modal-btn-contact'
              }"
            >
              {{ $t('checkout.payment_failed_modal.contact_button') }}
            </BaseLink>
            <BaseButton
              variant="accent"
              class="md-max:w-full md-max:order-first md-max:mb-16"
              v-bind="{
                trackData: { eventLabel: 'modal-paymentfailed-btn-retry' },
                'data-testid': 'paymentfailed-modal-btn-retry'
              }"
              v-on:click.self="closeModal"
            >
              {{ $t('checkout.payment_failed_modal.retry_button') }}
            </BaseButton>
          </div>
        </DialogDescription>
      </DialogPanel>
    </BaseModal>
  </aside>
</template>

<script setup lang="ts">
import { DialogTitle, DialogPanel, DialogDescription } from '@headlessui/vue';

const isModalOpen = defineModel<boolean>({ default: false });

const closeModal = () => {
  isModalOpen.value = false;
};
</script>
