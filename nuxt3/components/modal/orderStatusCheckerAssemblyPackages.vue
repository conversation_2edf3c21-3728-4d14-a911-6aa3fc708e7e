<template>
  <ModalBasic
    v-bind:model-value="modelValue"
    class="w-full max-w-[880px] py-48"
    data-testid="ModalOrderStatusCheckerAssemblyPackages"
    modal-classes="md-max:max-h-[100vh] md-max:h-[calc(100vh-36px)] md:max-w-[550px]"
    v-on:update:model-value="$emit('update:modelValue', $event);"
  >
    <template #default>
      <div>
        <h2
          class="mb-24 bold-28 text-offblack-800"
          v-html="$t('contact.forms.order_status.assembly_title')"
        />
        <h3
          class="mt-32 capitalize text-offblack-800 normal-20"
          v-html="data.furniture_category"
        />
        <p
          class="mt-8 text-offblack-600 normal-16"
          v-html="$t('contact.forms.order_status.assembly_details_body')"
        />
        <div class="grid grid-cols-1 gap-16 mt-16 md:grid-cols-2">
          <BaseLink
            variant="outlined"
            class="w-full"
            target="_blank"
            v-bind="{
              href: videoTutorialLink,
              trackData:{},
            }"
          >
            {{ $t('contact.forms.order_status.assembly_details_cta2') }}
          </BaseLink>
          <BaseLink
            v-if="data && data.instruction"
            variant="accent"
            class="w-full"
            target="_blank"
            v-bind="{
              href: data.instruction,
              trackData:{},
            }"
          >
            {{ $t('contact.forms.order_status.assembly_details_cta1') }}
          </BaseLink>
        </div>
        <template v-if="data && data.packaging && data.packaging.length">
          <p
            class="mt-32 text-offblack-800 normal-20"
            v-html="$t('contact.forms.order_status.assembly_details_packages_title')"
          />
          <ul class="mt-8">
            <li
              v-for="(packageData, index) in data.packaging"
              v-bind:key="index"
              class="grid grid-cols-12 text-offblack-600 normal-16 text-left"
            >
              <div
                class="bold-16"
                v-html="index + 1"
              />
              <div
                class="col-span-7 md:col-span-5"
                v-html="packageData.size"
              />
              <div
                class="col-span-4 md:col-span-6 text-right"
                v-html="packageData.weight"
              />
            </li>
          </ul>
        </template>
      </div>
    </template>
  </ModalBasic>
</template>

<script setup lang="ts">
import { TUTORIAL_PAGE_LINK, type languagesType } from '~/utils/languages';

defineProps<{ modelValue: Boolean, data: any }>();
defineEmits<{ 'update:modelValue': [value: false]}>();

const { locale } = useI18n();

const videoTutorialLink = computed(() => TUTORIAL_PAGE_LINK[locale.value.split('-')[0] as languagesType] || TUTORIAL_PAGE_LINK.en);
</script>
