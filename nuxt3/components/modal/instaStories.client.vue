<template>
  <section>
    <TransitionRoot
      v-bind:show="modelValue"
      as="template"
      enter="duration-300 ease-out"
      enter-from="opacity-0"
      enter-to="opacity-100"
      leave="duration-200 ease-in"
      leave-from="opacity-100"
      leave-to="opacity-0"
      class="z-9 relative"
    >
      <Dialog v-on:close="modelValue = false">
        <TransitionChild
          enter="duration-200 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div
            class="inset-0 fixed flex justify-end bg-black bg-opacity-40 overscroll-contain"
            aria-hidden="true"
          />
        </TransitionChild>

        <TransitionChild
          class="fixed inset-0"
          enter="duration-300 md:duration-200 ease-[cubic-bezier(0.0, 0.0, 0.58, 1.0)]"
          enter-from="md-max:translate-y-full md:opacity-0 md:scale-[0.95]"
          enter-to="opacity-100"
          leave="duration-200 md:duration-150 ease-[cubic-bezier(0.25, 0.1, 0.25, 1.0)])"
          leave-from="opacity-100"
          leave-to="md-max:translate-y-full md:opacity-0 md:scale-[0.95]"
          v-bind="{
            style: {
              transform: isModalBeingDragged ? `translateY(${top})` : ''
            }
          }"
        >
          <DialogPanel
            ref="target"
            data-testid="pdp-highlights-modal"
            class="fixed w-full bottom-0 rounded-t-[24px]
                    md-max:max-h-[95%] md-max:h-full
                    md:top-1/2 md:left-1/2 md:-translate-y-1/2 md:-translate-x-1/2
                    md:w-full md:max-w-[85%] md:aspect-[804/536] md:max-h-[90vh] xl:max-w-[1128px]
                    bg-neutral-200 md:rounded-[24px] overflow-hidden z-1"
          >
            <BaseButton
              variant="custom"
              v-bind:track-data="{}"
              class="box-content absolute top-32 right-16 md:top-40 md:right-32 p-12 group z-1 custom bg-white rounded-full overflow-hidden"
              v-on:click="modelValue = false"
            >
              <IconClose
                class="w-24 h-24 transition-rotate basic-transition hover:-rotate-45"
              />
            </BaseButton>
            <Stories
              v-bind="{
                stories
              }"
            />
          </DialogPanel>
        </TransitionChild>
      </Dialog>
    </TransitionRoot>
  </section>
</template>

<script setup lang="ts">
import { DialogPanel, Dialog, TransitionRoot, TransitionChild } from '@headlessui/vue';
import { useSwipe, useWindowSize } from '@vueuse/core';
import type { StoryOptions } from '~/components/stories/index.vue';
import { useTrackInteraction } from '~/composables/useTracking';
import useMq from '~/composables/useMq';

const { $dixa } = useNuxtApp();
const modelValue = defineModel<boolean>({ default: false });
defineProps<{ stories: StoryOptions[] }>();

const modal = ref(null);

const trackInteractionClose = useTrackInteraction(modal as Ref<NonNullable<typeof modal.value>>);

watch(modelValue, (value) => {
  if (!value) {
    $dixa.setWidgetVisibility(true);
    trackInteractionClose({ event: 'close', label: 'pdp-highlights-modal-close' });
  } else {
    $dixa.setWidgetVisibility(false);
  }
});

const { isSm } = useMq();
const target = ref<HTMLElement | null>(null);
const { height: containerHeight } = useWindowSize();
const top = ref('0');

const { lengthY: distanceY, isSwiping } = useSwipe(target, {
  onSwipe () {
    if (isSm.value) {
      if (distanceY.value < 0) {
        const distance = Math.abs(distanceY.value);
        top.value = `${distance}px`;
      } else {
        top.value = '0';
      }
    }
  },
  onSwipeEnd () {
    if (isSm.value) {
      if (distanceY.value < 0 && containerHeight.value && (Math.abs(distanceY.value) / containerHeight.value) >= 0.2) {
        modelValue.value = false;
      }
    }
  }
});
const isModalBeingDragged = computed(() => isSm.value && isSwiping.value);
</script>
