<template>
  <div>
    <p class="normal-12 text-black">
      {{ $t('cookiesmanager.modal.settings.title') }}
    </p>

    <CookiesManagerAccordion
      class="mt-16 mb-24"
      v-bind="{
        modelValue: consents.necessary,
        title: $t('cookiesmanager.modal.necessary.title'),
        toggleDisabled: true,
        shortDescription: $t('cookiesmanager.modal.necessary.description_short'),
        longDescription: $t('cookiesmanager.modal.necessary.description_long'),
      }"
      v-on:expand="handleTrackExpand('necessary')"
    />

    <CookiesManagerAccordion
      v-model="consents.performance"
      class="mt-16 mb-24"
      v-bind="{
        title: $t('cookiesmanager.modal.performance.title'),
        shortDescription: $t('cookiesmanager.modal.performance.description_short'),
        longDescription: $t('consent.performance'),
      }"
      v-on:update:model-value="handleTrackInput($event, 'performance')"
      v-on:expand="handleTrackExpand('performance')"
    />

    <CookiesManagerAccordion
      v-model="consents.functional"
      class="mt-16 mb-24"
      v-bind="{
        title: $t('cookiesmanager.modal.functional.title'),
        shortDescription: $t('cookiesmanager.modal.functional.description_short'),
        longDescription: $t('consent.functional'),
      }"
      v-on:input="handleTrackInput($event, 'functional')"
      v-on:expand="handleTrackExpand('functional')"
    />

    <CookiesManagerAccordion
      v-model="consents.advertising"
      class="mt-16 mb-24"
      v-bind="{
        title: $t('cookiesmanager.modal.advertising.title'),
        shortDescription: $t('cookiesmanager.modal.advertising.description_short'),
        longDescription: $t('consent.advertising'),
      }"
      v-on:input="handleTrackInput($event, 'advertising')"
      v-on:expand="handleTrackExpand('advertising')"
    />
  </div>
</template>

<script setup lang="ts">
import { useTrackingDOM } from '~/composables/useTracking';

const { trackCookiebar } = useTrackingDOM();
const { consents } = useTyCookieStore();

const handleTrackInput = (event: boolean, category: keyof typeof consents) => {
  trackCookiebar('consent_settings', `change_${category}_${event}`);
};

const handleTrackExpand = (category: keyof typeof consents) => {
  trackCookiebar('consent_settings', `open_description_${category}`);
};
</script>
