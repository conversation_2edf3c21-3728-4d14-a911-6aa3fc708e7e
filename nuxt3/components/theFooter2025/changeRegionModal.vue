<template>
  <BaseModal
    data-testid="change-region-modal"
    v-bind:model-value="model"
    v-bind:static="false"
    v-bind:default-close="true"
    v-on:update:model-value="model = false"
  >
    <div
      class="grid-container relative h-full
              flex justify-center lg:justify-end items-center"
    >
      <DialogPanel
        class="bg-white relative w-full max-w-[560px] lg:w-[560px] rounded-24
                px-16 md:px-24 lg:px-56 py-48"
      >
        <!-- Close button -->
        <BaseButton
          variant="custom"
          v-bind:track-data="{ eventLabel: 'close' }"
          class="group box-content absolute top-0 right-0 p-12 z-1 !bg-neutral-100 rounded-full m-12 lg:m-16"
          v-on:click="model = false"
        >
          <IconClose
            class="w-32 h-32 transition-rotate basic-transition group-hover:-rotate-45 "
          />
        </BaseButton>
        <h1 class="semibold-24 pr-56">
          {{ $t('menu.region_change.title') }}
        </h1>
        <p class="normal-16 mt-8 ">
          {{ $t('menu.region_change.subtitle') }}
        </p>

        <!-- Form -->
        <FormKit
          v-slot="{ state }"
          v-model="formValues"
          form-class="mt-24"
          type="form"
          validation="isSmthChanged"
          validation-behavior="live"
          v-bind="{
            validationRules:{
              isSmthChanged: ( { value } ) => {
                return value.region !== regionName || value.locale !== locale;
              }
            },
            actions: false,
            incompleteMessage: false,
          }"
        >
          <FormKit
            v-bind="{
              label: $t('common.shipping_location'),
              placeholder: $t('common.shipping_location'),
              name: 'region',
              region: formValues.region,
              options: regionsForSelect,
            }"
            select-icon-extra-class="!pl-[44px]"
            type="tySelect"
          >
            <template #prefix>
              <img
                class="absolute top-[30px] left-16 w-20 h-20"
                v-bind="{
                  src: `/nuxt3-statics/img/flags/${formValues.region}.svg`,
                  alt: formValues.region
                }"
              >
            </template>
          </FormKit>
          <FormKit
            type="tySelect"
            name="locale"
            outer-class="mt-16"
            v-bind="{
              disabled: !(availableLanguagesList.length > 1),
              label : $t('menu.labels.language'),
              options:
                availableLanguagesList.map((lang) => ({
                  value: lang.code,
                  label: lang.name
                }))
            }"
            v-bind:placeholder="$t('menu.labels.language')"
          />

          <template
            v-if="isTO3WarningVisible"
          >
            <p
              class="normal-12 mt-24 text-error"
            >
              {{ $t('menu.t03_popup.description') }}
            </p>
            <div class="grid grid-flow-row-dense gap-16 mt-16">
              <BaseButton
                variant="accent"
                class="md:order-2"
                v-bind="{
                  trackData: { eventLabel: 'continue' },
                  disabled: !state.valid || submitButtonIsLoading,
                }"
                v-on:click="formValues.region = regionName"
              >
                {{ $t('menu.t03_popup.button1') }}
              </BaseButton>
              <BaseButton
                variant="outlined"
                v-bind="{
                  trackData: { eventLabel: 'continue' },
                  disabled: !state.valid || submitButtonIsLoading,
                }"
                v-on:click="submitForm"
              >
                {{ $t('menu.t03_popup.button2') }}
              </BaseButton>
            </div>
          </template>
          <BaseButton
            v-else
            class="mt-48 w-full relative"
            variant="filled"
            type="submit"
            data-testid="button-submit"
            v-bind="{
              trackData: { eventLabel: 'continue' },
              disabled: !state.valid || submitButtonIsLoading,
            }"
            v-on:click="submitForm"
          >
            <span v-bind:class="{ 'invisible' : submitButtonIsLoading }">
              {{ $t('common.submit') }}
            </span>
            <UiDotsLoader
              v-if="submitButtonIsLoading"
              class="mt-2 flex gap-4"
              bounce-class="bg-black"
            />
          </BaseButton>
        </FormKit>
      </DialogPanel>
    </div>
  </BaseModal>
</template>

<script setup lang="ts">
import { DialogPanel } from '@headlessui/vue';
import { useToast } from 'vue-toastification';
import { regions, type RegionName } from '~/consts/regions';
import { LOCALE_NAMES } from '~/utils/languages';
import { switchLanguageClientOnly, switchRegionClientOnly } from '~/api/region';

const model = defineModel<boolean>({ default: false });
const global = useGlobal();
const i18n = useI18n();
const { regionName, regionCode, hasT03 } = storeToRefs(global);

const { regionsForSelect } = useRegions();
const { locale, createLangCode } = useLocale();
const formValues = ref<{region: RegionName, locale:Language}>({ region: regionName.value, locale: locale.value });
const regionData = computed(() => regions[formValues.value.region]);
const availableLanguagesList = computed(() => regionData.value.langs.map((lang):{
  code: Language;
  name: string;
} => ({
  code: lang,
  name: LOCALE_NAMES[lang]
})));

watch(() => formValues.value.region, () => {
  formValues.value.locale = availableLanguagesList.value[0].code as Language;
});

const submitButtonIsLoading = ref(false);
const { $logException } = useNuxtApp();
const switchLocalePath = useSwitchLocalePath();
const toast = useToast();
const isTO3WarningVisible = computed(() => hasT03.value && !regionData.value.isT03Available);

const submitForm = async () => {
  submitButtonIsLoading.value = true;

  try {
    if (formValues.value.region !== regionName.value) {
      const response = await switchRegionClientOnly(formValues.value.region);

      if (response.status === 'ok') {
        regionCode.value = regions[formValues.value.region].iso2;
      } else {
        throw new Error('Failed to change region');
      }
    }

    if (formValues.value.locale !== locale.value) {
      await switchLanguageClientOnly(formValues.value.locale);
    }

    const langCode = createLangCode(formValues.value.locale, regionCode.value);
    window.location.href = switchLocalePath(langCode) || `/${formValues.value.locale}`;
  } catch (error) {
    $logException(error);
    toast.error(i18n.t('common.error.connection'));
  } finally {
    submitButtonIsLoading.value = false;
  }
};

</script>
