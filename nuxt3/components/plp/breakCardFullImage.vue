<template>
  <BaseLink
    class="relative block w-full h-full"
    variant="custom"
    v-bind="{
      href: ctaUrl,
      trackData: {
        eventLabel: 'plp-break',
        eventPath: ctaUrl
      }
    }"
  >
    <img
      v-if="image"
      v-bind="{
        src: $urlFor(image).url(),
        alt: heading
      }"
      class="w-full h-full object-cover group-hover:scale-[1.04] transform transition-transform duration-500 ease-in-out"
    >
    <div class="absolute top-0 left-0 p-8 pt-16 md:p-16">
      <div
        class="hidden md:block mb-8"
        v-bind:class="[isThemeLight ? 'text-offwhite-800' : 'text-offblack-700']"
      >
        <img
          v-if="icon"
          v-bind="{
            src: $urlFor(icon).url(),
            alt: heading
          }"
        >
      </div>
      <h2
        class="bold-20 xl:bold-24 mb-4 md:mb-8"
        v-bind:class="[isThemeLight ? 'text-offwhite-800' : 'text-offblack-700']"
        v-html="heading"
      />
      <p
        class="normal-14 xl:normal-16 mb-8 md:mb-16"
        v-bind:class="[isThemeLight ? 'text-offwhite-800' : 'text-offblack-600']"
        v-html="body"
      />
    </div>
  </BaseLink>
</template>

<script setup lang="ts">

withDefaults(defineProps<{
  isThemeLight: boolean,
  heading: string,
  body: string,
  ctaUrl: string | boolean,
  image: Object,
  icon: Object,
}>(), {
  isThemeLight: false,
  ctaUrl: false,
  image: undefined,
  icon: undefined
});

</script>
