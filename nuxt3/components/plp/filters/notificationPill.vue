<template>
  <Transition
    name="fade"
  >
    <div
      v-show="isNotificationVisible"
      class="fixed bottom-0 pb-16 z-2 w-full flex justify-end lg:justify-center pointer-events-none"
    >
      <BaseButton
        class="h-56 text-center pointer-events-auto mr-16 md:mr-40 lg:mr-0
            overflow-hidden bg-orange rounded-full
            grid items-center px-16 text-white normal-14
            transition-all duration-300 ease-in-out"
        v-bind:class="[
          isExpanded ? 'grid-cols-[24px_1fr_auto]' : 'grid-cols-[24px_0fr_auto]'
        ]"
        variant="custom"
        v-bind="{
          trackData:{
            event: 'userInteraction',
            eventCategory: 'grid-filters',
            eventAction: 'filters_notification_bar',
            eventLabel: `click_filters_notification_bar`
          }
        }"
        v-on:click="toggleFiltersDrawer(true)"
      >
        <IconFilter
          class="w-20 h-20 mx-2"
          v-bind:class="{
            'mr-8' : isExpanded || filtersCount,
          }"
        />
        <span
          class="whitespace-nowrap  overflow-hidden transition-opacity duration-300 ease-in-out"
          v-bind:class="{
            'opacity-0': !isExpanded,
          }"
        >
          <span class="pl-[6px] inline-block">
            {{ $t('plp.filters.header.all_filters') }}
          </span>
        </span>
        <div
          v-if="filtersCount"
          class="flex items-center justify-center justify-self-end
              w-20 h-20 ml-8 rounded-full bg-white/80
              bold-12 text-neutral-900 text-center"
        >
          {{ filtersCount }}
        </div>
      </BaseButton>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { watchDebounced, useWindowScroll, useElementVisibility } from '@vueuse/core';
import useFiltersUtils from '~/composables/plp/useFiltersUtils';
import useMq from '~/composables/useMq';

const { filtersCount } = storeToRefs(usePlpStore());
const { y: scrollTop } = useWindowScroll();
const { isDesktopViewport, isSm } = useMq();
const isExpanded = ref(isDesktopViewport.value);
const { toggleFiltersDrawer } = useFiltersUtils();

let scrollDirection = -1;
let scrollTopWhenDirectionWasChanged = 0;
let distanceFromLastScrollTop = 0;
const footerTarget = ref(document?.querySelector('[data-observe-view="Footer"]'));
const modernNavigationTarget = ref(document?.getElementById('plp-navigation-modern'));
const isFooterVisible = useElementVisibility(footerTarget);
const isNavigationVisible = useElementVisibility(modernNavigationTarget);

const isNotificationVisible = computed(() => ((scrollTop.value && !isFooterVisible.value && !isNavigationVisible.value) || isSm.value));

onMounted(() => {
  if (isDesktopViewport.value) {
    isExpanded.value = true;
  }
});

watchDebounced(scrollTop, (newScrollTop, oldScrollTop) => {
  if (isDesktopViewport.value) {
    isExpanded.value = true;
    return;
  }

  if (newScrollTop > oldScrollTop) {
    scrollDirection !== 1 && (scrollTopWhenDirectionWasChanged = newScrollTop);
    scrollDirection = 1;
    distanceFromLastScrollTop = Math.abs(newScrollTop - scrollTopWhenDirectionWasChanged);
  } else {
    scrollDirection = -1;
  }

  isExpanded.value = newScrollTop > 200 &&
    scrollDirection === -1 &&
    distanceFromLastScrollTop > 100;
}, { debounce: 100, maxWait: 100 });

</script>

<style lang="scss">
@screen md-max {
  .dixa-messenger-namespace .dixa-messenger-wrapper {
    bottom: 86px !important;
    right: 16px !important;
  }
}
@screen lg-max {
  .dixa-messenger-namespace .dixa-messenger-wrapper {
    bottom: 86px !important;
  }
}
</style>
