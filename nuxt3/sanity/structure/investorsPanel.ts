export const investorsPanel = (S: any) => (
    S.listItem()
        .title('Inverstors Panel')
        .child(
            S.list()
            .title('Inverstors Panel')
            .items([
                S.listItem()
                .title('Press Releases')
                .schemaType('pressReleases')
                .child(
                    S.documentList()
                    .title(`Press Releases`)
                    .schemaType('pressReleases')
                    .filter('_type == "pressReleases"')
                ),

                S.divider(),

                S.listItem()
                .title('Investor relations reports')
                .schemaType('reportsQuarters')
                .child(
                    S.documentList()
                    .title(`Investor relations reports`)
                    .schemaType('reportsQuarters')
                    .filter('_type == "reportsQuarters"')
                ),
            ])
        )
)
