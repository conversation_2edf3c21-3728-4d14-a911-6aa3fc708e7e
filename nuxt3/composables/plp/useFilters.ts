import { intersection } from 'lodash-es';
import { compareSizeRange } from '@/helpers/filters';

import {
  type FilterOptions,
  FilterQueryParams,
  FurnitureType,
  OptionsColor, OptionsFeatures,
  OptionsMaterials,
  OptionsStyles,
  OptionsTypes
} from '@/utils/filters';
import useNpi from '~/composables/plp/useNpi';

export default function () {
  const route = useRoute();
  const { PLP_MATERIALS, PLP_FURNITURE_TYPES } = useNpi();

  const geTypesFilteredByQuery = (type: FurnitureType, query: FilterQueryParams, value: string) =>
    PLP_FURNITURE_TYPES[type][query].includes(value as FilterOptions);

  const {
    filtersConfigurationForActiveCategory,
    filteredFurnitureTypes,
    filteredColors,
    filteredProductLines,
    filteredMaterials,
    filteredFeatures,
    filteredTypes,
    filteredStyles
  } = storeToRefs(usePlpStore());

  const isOnlyThisFilterActive : (filterName: FilterQueryParams) => boolean = (filterName: FilterQueryParams) : boolean => (Object.keys(route.query).length === 1 &&
  Object.keys(route.query).includes(filterName));

  const isFilterValueAvailable = (filterName: FilterQueryParams, value: string):boolean => {
    if (filterName === FilterQueryParams.ADDITIONAL_FEATURES) { return true; }

    if (filterName === FilterQueryParams.COLORS) {
      if (isOnlyThisFilterActive(filterName)) {
        return true;
      }

      const types:FurnitureType[] = getTypesForFeature(FilterQueryParams.COLORS);

      const filteredByTypes = types.filter(
        type => geTypesFilteredByQuery(type, FilterQueryParams.COLORS, value)
      ).length;

      const filteredByMaterials = filteredMaterials.value.length
        ? filteredMaterials.value.filter(
          material => types.filter((type:FurnitureType) => PLP_MATERIALS[material].colorsForType[type]?.includes(value as OptionsColor)).length
        ).length
        : true;

      return !!(filteredByTypes && filteredByMaterials);
    }

    if (filterName === FilterQueryParams.PRODUCT_LINES) {
      if (isOnlyThisFilterActive(filterName)) {
        return true;
      }

      const types:FurnitureType[] = getTypesForFeature(FilterQueryParams.PRODUCT_LINES);

      const filteredByTypes = types.filter(
        type => geTypesFilteredByQuery(type, FilterQueryParams.PRODUCT_LINES, value)
      ).length;

      return !!filteredByTypes;
    }

    // SIZE filters
    const size:Array<FilterQueryParams> = [
      FilterQueryParams.WIDTH,
      FilterQueryParams.HEIGHT,
      FilterQueryParams.DEPTH
    ];

    if (size.includes(filterName)) {
      if (isOnlyThisFilterActive(filterName) || filteredFurnitureTypes.value.length === filtersConfigurationForActiveCategory.value.types.length) {
        return true;
      }

      const types:FurnitureType[] = getTypesForFeature(filterName);
      // const availableOptions = uniq(flatten(state.filteredFurnitureTypes.map(type => PLP_FURNITURE_TYPES[type][filterName as FilterQuery])));
      // const filteredOptions = availableOptions.filter(option => compareSizeRange(option, value));
      const result = types.filter((type:FurnitureType) => PLP_FURNITURE_TYPES[type][filterName as FilterQueryParams].filter((option:string) => compareSizeRange(option, value)).length);
      return !!result.length;
    }

    if (filterName === FilterQueryParams.MATERIALS) {
      const types:FurnitureType[] = getTypesForFeature(FilterQueryParams.MATERIALS);

      const filteredByColors = filteredColors.value.length
        ? filteredColors.value.filter(color => types.filter((type:FurnitureType) => PLP_MATERIALS[value as OptionsMaterials].colorsForType[type]?.includes(color)).length).length
        : true;

      const filteredByFeatures = filteredFurnitureTypes.value
        ? types
          .filter(type => geTypesFilteredByQuery(type, FilterQueryParams.MATERIALS, value)).length
        : filteredFurnitureTypes.value;

      return !!(filteredByColors && filteredByFeatures);
    }

    if (filterName === FilterQueryParams.STYLES) {
      return !!filteredFurnitureTypes.value.filter(type => PLP_FURNITURE_TYPES[type].styles.includes(value as OptionsStyles)).length;
    }

    if (filterName === FilterQueryParams.TYPES) {
      /*

      Special case:
      In filter TYPES (only available in wardrobe category) Only one option
      can be active at the same time - so it should works like radio button

     */
      if (filteredTypes.value.length > 0) {
        return filteredTypes.value.includes(value as OptionsTypes);
      }

      /*

      Special case:
      if feature OPEN_COMPARTMENTS is selected, type FULLY CLOSED should be disabled

     */
      if (filteredFeatures.value.includes(OptionsFeatures.OPEN_COMPARTMENTS) && value === OptionsTypes.FULLY_CLOSED) {
        return false;
      }

      const types:FurnitureType[] = getTypesForFeature(FilterQueryParams.TYPES);

      const filteredByTypes = types.filter(
        type => geTypesFilteredByQuery(type, FilterQueryParams.TYPES, value)
      ).length;

      return !!filteredByTypes;
    }

    if (filterName === FilterQueryParams.FEATURES) {
      /*

      Special case:
      if type FULLY CLOSED is selected, feature OPEN_COMPARTMENTS should be disabled

     */
      if (filteredTypes.value.includes(OptionsTypes.FULLY_CLOSED) && value === OptionsFeatures.OPEN_COMPARTMENTS) {
        return false;
      }

      const types:FurnitureType[] = filteredFurnitureTypes.value;// getTypesForFeature(FilterQuery.FEATURES);

      const filteredByFeatures = types.filter(
        type => geTypesFilteredByQuery(type, FilterQueryParams.FEATURES, value)
      ).length;

      return !!filteredByFeatures;
    }

    return true;
  };

  const getTypesForFeature = (filterQuery: FilterQueryParams): Array<FurnitureType> => {
    const types : Array<Array<FurnitureType>> = [];

    const productLines:FurnitureType[] = filteredProductLines.value.length
      ? filtersConfigurationForActiveCategory.value.types.filter(
        type => intersection(PLP_FURNITURE_TYPES[type].productLines.join(',').split(','), filteredProductLines.value).length
      )
      : filtersConfigurationForActiveCategory.value.types;

    types.push(productLines);

    if (filterQuery !== FilterQueryParams.COLORS) {
      const colors:FurnitureType[] = filteredColors.value.length
        ? filtersConfigurationForActiveCategory.value.types.filter(
          type => (filteredColors.value.length ? intersection(PLP_FURNITURE_TYPES[type].colors, filteredColors.value).length : true)
        )
        : filtersConfigurationForActiveCategory.value.types;

      types.push(colors);
    }

    if (filterQuery !== FilterQueryParams.MATERIALS) {
      const materials:FurnitureType[] = filteredMaterials.value.length
        ? filtersConfigurationForActiveCategory.value.types.filter(
          type => intersection(PLP_FURNITURE_TYPES[type].materials, filteredMaterials.value).length
        )
        : filtersConfigurationForActiveCategory.value.types;

      types.push(materials);
    }

    // STYLES
    const styles:FurnitureType[] = filteredStyles.value.length
      ? filtersConfigurationForActiveCategory.value.types.filter(
        type => intersection(PLP_FURNITURE_TYPES[type].styles, filteredStyles.value).length
      )
      : filtersConfigurationForActiveCategory.value.types;

    types.push(styles);

    // TYPES

    let typesForTypes: FurnitureType[];

    if (!filteredTypes.value.length) {
      typesForTypes = filtersConfigurationForActiveCategory.value.types;
    } else {
      typesForTypes = filtersConfigurationForActiveCategory.value.types.filter(type => intersection(PLP_FURNITURE_TYPES[type].types, filteredTypes.value).length);
    }

    types.push(typesForTypes);

    // if (filterQuery !== FilterQuery.FEATURES) {
    const features:FurnitureType[] = filteredFeatures.value.length
      ? filtersConfigurationForActiveCategory.value.types.filter(
        type => intersection(PLP_FURNITURE_TYPES[type].features, filteredFeatures.value).length
      )
      : filtersConfigurationForActiveCategory.value.types;

    types.push(features);
    // }

    // SIZE

    const size:Array<FilterQueryParams> = [
      FilterQueryParams.WIDTH,
      FilterQueryParams.HEIGHT,
      FilterQueryParams.DEPTH
    ];

    if (size.includes(filterQuery)) {
      size.splice(size.findIndex((el:FilterQueryParams) => el === filterQuery), 1);
    }

    const { query } = route;
    const typesFilteredBySize: Array<FurnitureType[]> = size.map((name:FilterQueryParams) => ((query[name] as string)?.split(',').length
      ? filtersConfigurationForActiveCategory.value.types
        .filter(type => PLP_FURNITURE_TYPES[type][name].find((option: string) => (query[name] as string)?.split(',').findIndex(value => compareSizeRange(option, value)) !== -1))
      : filtersConfigurationForActiveCategory.value.types));
    types.push(intersection(...typesFilteredBySize));

    return intersection(...types);
  };

  return {
    isFilterValueAvailable
  };
}
