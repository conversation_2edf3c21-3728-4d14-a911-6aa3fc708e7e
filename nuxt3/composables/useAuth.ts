import { appendResponseHeader } from 'h3';
import type { UseFetchOptions } from 'nuxt/app';
import getParameterByName from '~/helpers/getParameterByName';
import parseCookiesToBeFormat from '~/helpers/parseCookiesToBeFormat';
import { type GlobalState, type UserGlobal } from '~/stores/global';

type Cookie = string;

function parseCookies (rawCookiesString: string): Cookie[] {
  const cookieParts = rawCookiesString.split(/,(?!\s+\d{2}\s+\w{3}\s+\d{4}\s+\d{2}:\d{2}:\d{2}\s+GMT)/);
  return cookieParts.map(cookie => cookie.trim());
}

export async function useAuth () {
  const { $logException, $pinia } = useNuxtApp();
  const store = useGlobal($pinia);
  const event = useRequestEvent();

  const url = useRequestURL();

  const lat: string | null = getParameterByName('lat', url.search);

  const options: UseFetchOptions<GlobalState> = process.server
    ? {
        params: { ...(lat ? { lat } : {}) },
        deep: false,
        headers: {
          referer: url.href
        },
        onResponse (ctx) {
          const rawCookiesString = ctx.response.headers.get('set-cookie');

          if (rawCookiesString) {
            const cookies = parseCookies(rawCookiesString);
            store.SET_COOKIES(parseCookiesToBeFormat(cookies));

            for (const cookie of cookies) {
              appendResponseHeader(event, 'Set-Cookie', cookie);
            }
          }
        }
      }
    : {};

  const { data, error } = await useApi<UserGlobal>('api/v1/user-global/', options);

  if (error.value) {
    $logException(error.value);
  } else if (data.value) {
    store.SET_USER_GLOBAL(data.value);
  }
}
