import { useToast } from 'vue-toastification';
import scrollToElement from '~/helpers/scrollToElement';
import isElementInViewport from '~/helpers/isElementInViewport';
import { SUBMIT_FORM, SUBSCRIBE_NEWSLETTER } from '~/api/contact';
import { useContactStore } from '~/stores/contact';
import ContactFormDefault from '~/components/contact/form/default.vue';
import ContactFormChangeOrder from '~/components/contact/form/changeOrder.vue';
import ContactFormIssueOrComplaint from '~/components/contact/form/issueOrComplaint.vue';

export interface ContactFormData {
  newsletter?: boolean
  email: string
  firstName?: string
  message?: string
  topic?: string
  subTopic?: string
  orderNumber?: string
  areAnyElementsMissing?: string
  missingElementsDescription?: string
  isPackagingDamaged?: string
  isPackagingDamageReportedToDeliveryCompany?: string
  areAnyShelfElementsDamaged?: string
  shelfElementsDamageDescription?: string
  damagedPackageFiles?: any
  damagedElementsFiles?: any
}

const getSubmitFormParamsObject = (data: ContactFormData, topic: object | string | null) => {
  const { GLOBAL_CONTACT } = useContactStore();

  const images = GLOBAL_CONTACT.images.results.map((image: { id: string }) => image.id);

  const parsedData = {
    first_name: data.firstName || null,
    email: data.email || null,
    message: data.message || null,
    order: data.orderNumber || null,
    topic: data.subTopic || topic?.topic || 'other',
    missing_elements: (data.areAnyElementsMissing === 'yes') || false,
    missing_elements_desc: data.missingElementsDescription || '',
    damaged: {
      packaging: (data?.isPackagingDamaged === 'yes') || false,
      reported_to_delivery_company: (data?.isPackagingDamageReportedToDeliveryCompany === 'yes') || false,
      shelf_elements: (data?.areAnyShelfElementsDamaged === 'yes') || false,
      damages_description: data?.shelfElementsDamageDescription || '',
      images
    }
  };

  return parsedData;
};

export default function () {
  const toast = useToast();
  const route = useRoute();
  const router = useRouter();

  const { t } = useI18n();
  const { $logException } = useNuxtApp();

  const topicOptions = [
    {
      label: t('contact.forms.topics.product_info'),
      value: 'product_info',
      form: ContactFormDefault
    },
    {
      label: t('contact.forms.topics.order_status'),
      value: 'order_status'
    },
    {
      label: t('contact.forms.topics.feedback'),
      value: 'feedback',
      form: ContactFormDefault
    },
    {
      label: t('contact.forms.topics.change_order'),
      value: 'change_order',
      form: ContactFormChangeOrder
    },
    {
      label: t('contact.forms.topics.report_issue'),
      value: 'report_an_issue_or_complaint',
      form: ContactFormIssueOrComplaint
    },
    {
      label: t('contact.forms.topics.assembly_service'),
      value: 'assembly_service',
      form: ContactFormDefault
    },
    {
      label: t('contact.forms.topics.other'),
      value: 'other',
      form: ContactFormDefault
    }
  ] as const;

  const selectedTopic = ref({
    topic: ''
  });

  const isFormSent = ref(false);
  const isFormBeingSent = ref(false);
  const formValues = ref<ContactFormData>({
    newsletter: false,
    email: '',
    topic: '',
    subTopic: ''
  });

  const resetForm = () => {
    isFormSent.value = false;
    isFormBeingSent.value = false;
    formValues.value = {
      newsletter: false,
      email: '',
      topic: selectedTopic.value,
      subTopic: ''
    };
  };

  const submitForm = async () => {
    isFormBeingSent.value = true;

    formValues.value.newsletter && await signNewsletter();

    try {
      await SUBMIT_FORM(getSubmitFormParamsObject(formValues.value, selectedTopic.value));

      isFormBeingSent.value = false;
      isFormSent.value = true;

      // Scroll to thankyou message if sent
      const contactFormEl = document.getElementById('contact-form');

      if (contactFormEl) {
        !isElementInViewport(contactFormEl) && scrollToElement({ element: contactFormEl, offset: 32 });
      }
    } catch (error) {
      toast.error(t('common.error.connection'));
      $logException(error);
    } finally {
      isFormBeingSent.value = false;
    }
  };

  const signNewsletter = async () => {
    try {
      await SUBSCRIBE_NEWSLETTER({ email: formValues.value.email, source: 'contact' });
      toast.success(t('newsletter.notify_success'));
    } catch (error) {
      toast.error(t('common.error.connection'));
      $logException(error);
    }
  };

  watch(route, () => {
    selectedTopic.value.topic = route.query?.topic as string || '';
    formValues.value.subTopic = route.query?.subtopic as string || '';
  }, { immediate: true });

  watch(selectedTopic, () => {
    resetForm();
    router.push({ query: { topic: selectedTopic.value.topic } });
  });

  const formType = computed(() => topicOptions.find(topic => topic.value === selectedTopic.value.topic)?.form || topicOptions[0].form);

  return {
    formType,
    formValues,
    isFormSent,
    topicOptions,
    selectedTopic,
    isFormBeingSent,
    resetForm,
    submitForm
  };
}
