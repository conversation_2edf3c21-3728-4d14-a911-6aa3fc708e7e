<template>
  <h1>Loading...</h1>
</template>

<script setup lang="ts">
import { getRawUrlWithQuery } from '~/helpers/checkoutUrls';
const { $addLocaleToPath } = useNuxtApp();
definePageMeta({
  layout: 'checkout'
});

const global = useGlobal();
await global.FETCH_GLOBAL();
const { cartId, userId } = storeToRefs(global);
const route = useRoute();

if (cartId.value) {
  await navigateTo(getRawUrlWithQuery($addLocaleToPath, 'checkout', cartId.value, userId.value, '', route.query));
} else {
  await navigateTo('/404');
}

</script>
