<template>
  <section class="grid grid-cols-12">
    <TheFAQBreadcrumbs
      class="col-span-12 lg:mb-48"
      v-bind="{
        categories,
        tags
      }"
    />
    <TheFAQSideMenu
      class="hidden col-span-3 lg:block"
      v-bind="{
        categories
      }"
    />
    <article class="col-span-12 lg-max:mb-16 lg:col-start-5 lg:col-end-12 xl:col-start-5 xl:col-end-11 xl2:col-start-5 xl2:col-end-11">
      <NuxtPage
        v-bind="{
          tags,
          articles,
          categories
        }"
      />
    </article>
  </section>
</template>

<script lang="ts" setup>
defineProps<{
  categories: FaqCategory[],
  articles: FaqArticle[],
  tags: FaqTag[]
}>();
</script>
