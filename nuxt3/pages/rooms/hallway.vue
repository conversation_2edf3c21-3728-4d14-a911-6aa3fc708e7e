<template>
  <main>
    <NuxtLazyHydrate when-idle>
      <SectionHeroImage
        data-section="hero"
        v-bind="{
          imagePath: 'lp/hallway/hero',
          imageAlt: $t('lp.hallway.hero.title'),
          imageType: 'M T SD LD',
          headingCopy: $t('lp.hallway.hero.title'),
          subheadingCopy: $t('common.tylko_spaces'),
          ctaCopy: $t('common.explore_more'),
          ctaUrl: $addLocaleToPath('plp'),
          headerColorClass: 'text-offwhite-600',
          backgroundColor: '#d8d1cc'
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionMiddlePics
        class="bg-[#EDECE8]"
        v-bind="{
          title: $t('lp.hallway.harmony.title'),
          subtitle: $t('lp.hallway.harmony.body'),
          items: [
            {
              headline: $t('lp.hallway.harmony.sideboard.title'),
              description: $t('lp.hallway.harmony.sideboard.body'),
              imagePath: 'lp/hallway/harmony/1',
              url: `${$addLocaleToPath('plp')}sideboard/`
            },
            {
              headline: $t('lp.hallway.harmony.shoerack.title'),
              description: $t('lp.hallway.harmony.shoerack.body'),
              imagePath: 'lp/hallway/harmony/2',
              url: `${$addLocaleToPath('plp')}shoerack/`
            },
            {
              headline: $t('lp.hallway.harmony.wardrobe.title'),
              description: $t('lp.hallway.harmony.wardrobe.body'),
              imagePath: 'lp/hallway/harmony/3',
              url: `${$addLocaleToPath('plp')}wardrobe/`
            }
          ],
          isLightTheme: true,
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionExperience
        data-section="experience"
        class="bg-[#847268] pt-full lg:pt-[53.33%]"
        v-bind="{
          videoID: {
            mobile: 'o9p67tuf4m',
            desktop: 'gnvpwx2pwf'
          },
          imageAlt: $t('common.experience_the_perfect_fit'),
          headingCopy: $t('common.experience_the_perfect_fit'),
          subheadingCopy: $t('common.tylko_spaces'),
          body: $t('common.animation.caption'),
          backgroundColor: '#d8d1cc'
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <SectionUspBar
          data-section="usps"
          is-assembly
          is-assembly-free
          v-bind="{
            reviews: {
              rating: reviewsAverageScore,
              count: reviewsCount
            }
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <section class="grid bg-[#D3D3CA] py-64 md:py-96">
        <h2
          class="order-1 mx-auto mb-24 text-center container-cstm bold-28 lg:bold-46 md:mb-16 text-offblack-800"
          v-html="$t('lp.hallway.solutions.title')"
        />
        <p
          class="container-cstm order-4 md:order-2 normal-16 md:normal-20 text-center mt-16 md:mt-0 md:mb-40 !max-w-[440px] mx-auto text-offblack-600"
          v-html="$t('lp.hallway.solutions.body')"
        />
        <SectionGallery
          class="order-3"
          v-bind="{
            path: 'lp/hallway/gallery/',
            alt: $t('lp.hallway.hero.title'),
            images: 3
          }"
        />
      </section>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <LazySectionMinigrid
          data-observe-view="minigrid-browse-by-room"
          data-section="minigrid-browse-by-room"
          v-bind="{
            id: 'hallway_category_shoerack',
            title: $t('common.browse_by_category'),
            itemListName: 'mini_grid',
            filters: [
              {
                value: 'hallway_category_shoerack',
                label: $t('common.category.shoerack'),
              },
              {
                value: 'hallway_category_wardrobe',
                label: $t('common.category.wardrobe'),
              },
              {
                value: 'hallway_category_sideboard',
                label: $t('common.category.sideboard'),
              },
            ],
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <InstafeedCarousel
        data-section="insta-grid"
        class="bg-[#FFFFFF]"
        v-bind="{
          title: $t('hp.instafeed.title'),
          ids: ['t110', 't109', 't111', 't108', 't104', 't105', 't113', 't112', 't107', 't106'],
          'is-theme-light': false
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <ErrorBoundary>
        <SectionReviewsOld
          v-if="data && reviewsData"
          class="bg-[#FFF8E9]"
          v-bind="{
            reviews: reviewsData,
            overallReviewsCount: reviewsCount,
            generalScore: reviewsAverageScore,
            reviewsTitle: 'common.a_word_from_our_customers',
            cta: 'common.see_all_reviews'
          }"
        />
      </ErrorBoundary>
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionTextImageColumns
        data-section="values-section"
        class="bg-[#A48F8E]"
        v-bind="{
          title: $t('lp.hallway.journal.headline'),
          subtitle: $t('common.tylko_journal'),
          imagePath: 'lp/hallway/journal',
          description: $t('lp.hallway.journal.body'),
          ctaCopy: $t('common.read_full_story'),
          ctaUrl: $addLocaleToPath('6-creative-ideas-how-to-use-space-under-the-stairs'),
          isLightTheme: true,
          isReversed: false,
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionExplore
        data-section="explore"
        class="bg-[#F9F9F9]"
        v-bind="{
          heading: $t('common.discover_more_tylko_spaces'),
          ctaText: $t('common.explore'),
          type: 'spaces',
          sectionCategories: ['kidsroom', 'livingroom', 'office', 'bedroom'],
          largeItems: [1,3],
          isThemeLight: false
        }"
      />
    </NuxtLazyHydrate>
    <NuxtLazyHydrate when-visible>
      <SectionSeo
        v-bind="{
          body: $t('lp.hallway.seo')
        }"
      />
    </NuxtLazyHydrate>
  </main>
</template>

<script setup lang="ts">
import useSeo from '~/composables/useSeo';

import { reviews } from '~/api/spaces';

const { roomMeta, roomsJsonLd } = useSeo();
const { reviewsCount, reviewsAverageScore } = storeToRefs(useGlobal());

const { data } = await reviews('hallway');

interface Review {
  name?: string,
  country?: string,
  score?: number,
  title?: string,
  description?: string,
  properPhoto?: string
}

type Spaces = {
  reviews: Array<Review>,
  reviewsCount: Number,
  reviewsAverageScore: Number,
}

const reviewsData = computed(() => (data?.value as Spaces)?.reviews);

useSeoMeta(roomMeta('hallway'));

// @ts-expect-error
useJsonld(roomsJsonLd(
  reviewsData,
  reviewsCount.value,
  reviewsAverageScore.value
));
</script>
