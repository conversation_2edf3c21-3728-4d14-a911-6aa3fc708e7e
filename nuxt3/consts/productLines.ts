export enum PRODUCT_LINE {
  EDGE = 'edge',
  ORIGINAL = 'original',
  TONE = 'tone',
  MODERN = 'modern',
  CLASSIC = 'classic',
  SOTTY = 'sotty'
}

export const PRODUCT_LINES: { [key in PRODUCT_LINE]: {
  name: string;
  labelKey: string;
  label:string;
  plpQuery: string;
  minigridShelfTypes: string
} } = {
  edge: {
    name: 'edge',
    label: 'Edge',
    labelKey: 'common.product_line.edge',
    plpQuery: '?productLines=4,5',
    minigridShelfTypes: '4,5'
  },
  original: {
    name: 'original',
    label: 'Original',
    labelKey: 'common.product_line.original',
    plpQuery: '?productLines=0,1,2',
    minigridShelfTypes: '0,1,2'
  },
  tone: {
    name: 'tone',
    label: 'Tone',
    labelKey: 'common.product_line.tone',
    plpQuery: '?productLines=3',
    minigridShelfTypes: '3'
  },
  classic: {
    name: 'classic',
    label: 'Classic',
    labelKey: 'common.product_line.classic',
    plpQuery: '?productLines=0,2',
    minigridShelfTypes: '0,2'
  },
  modern: {
    name: 'modern',
    label: 'Modern',
    labelKey: 'common.product_line.modern',
    plpQuery: '?productLines=1',
    minigridShelfTypes: '1'
  },
  sotty: {
    name: 'sotty',
    label: 'Sotty',
    labelKey: 'common.product_line.sotty',
    plpQuery: '',
    minigridShelfTypes: '10'
  }
} as const;

export enum PRODUCT_COLOR_STYLES {
  SOLID = 'solid',
  PLYWOOD = 'plywood',
  WOODEN = 'wooden',
  MIXED = 'mixed',
}
