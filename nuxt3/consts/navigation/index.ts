import { pick } from 'lodash-es';
import { GRID_CATEGORIES, SOTTY_CATEGORIES } from '~/consts/categories';

const imagePath = 'common/menu/categories/allshelves';
const gridCategories = GRID_CATEGORIES();
export const NavigationCategoriesNames = {
  ALL_PRODUCTS: 'ALL_PRODUCTS',
  HIGH_STORAGE: 'HIGH_STORAGE',
  LOW_STORAGE: 'LOW_STORAGE',
  SOFA: 'SOFA',
  WARDROBES: 'WARDROBES',
  WARDROBE: 'wardrobe',
  DESKS: 'DESKS',
  DESK: 'desk',
  BOOKCASE: 'bookcase'
} as const;
export type NAVIGATION_CATEGORIES_NAMES = typeof NavigationCategoriesNames[keyof typeof NavigationCategoriesNames];
export type NavigationCategory = {
  name: string;
  nameKey: string;
  urlPathKey: string;
  descriptionKey: string;
  imagePath: string;
  children?: NavigationCategory[];
  query?: {};
};

export const CATEGORIES_MAP = ($addLocaleToPath, t, smoothFeatureFlag: boolean = false):NavigationCategory[] => [{
  name: NavigationCategoriesNames.ALL_PRODUCTS,
  nameKey: 'common.category.all',
  urlPathKey: '',
  descriptionKey: 'common.category.all_description',
  imagePath,
  children: [
    {
      name: NavigationCategoriesNames.HIGH_STORAGE,
      nameKey: 'common.megamenu.categories.high_furniture',
      descriptionKey: 'plp.category.description.high_furniture',
      imagePath: 'common/menu/categories/high_furniture',
      urlPathKey: 'common.megamenu.categories.high_furniture_url_path',
      children: [
        gridCategories.wallstorage,
        gridCategories.bookcase,
        gridCategories.vinylstorage
      ]
    },
    {
      name: NavigationCategoriesNames.LOW_STORAGE,
      nameKey: 'common.megamenu.categories.low_furniture',
      descriptionKey: 'plp.category.description.low_furniture',
      imagePath: 'common/menu/categories/low_furniture',
      urlPathKey: 'common.megamenu.categories.low_furniture_url_path',
      children: [
        gridCategories.sideboard,
        gridCategories.tvstand,
        gridCategories.chest,
        gridCategories.dressingtable,
        gridCategories.bedsideTable,
        gridCategories.shoerack,
        // gridCategories.vinylstorage,
        gridCategories.desk
      ]
    },
    {
      name: NavigationCategoriesNames.WARDROBES,
      nameKey: 'common.category.wardrobe_plural',
      descriptionKey: 'common.category.wardrobe_description',
      imagePath: 'common/menu/categories/wardrobes',
      urlPathKey: 'common.category.wardrobe_url_path',
      children: [
        {
          urlPathKey: t(gridCategories.wardrobe.urlPathKey),
          name: 'wardrobe___closed',
          nameKey: 'common.megamenu.categories.closed_wardrobes',
          descriptionKey: 'common.category.wardrobe_description',
          imagePath: 'common/menu/categories/wardrobes_closed',
          query: {
            types: 'fullyClosed'
          }
        },
        {
          urlPathKey: t(gridCategories.wardrobe.urlPathKey),
          name: 'wardrobe___semi-open',
          nameKey: 'common.megamenu.categories.semi_open_wardrobes',
          descriptionKey: 'common.category.wardrobe_description',
          imagePath: 'common/menu/categories/wardrobes_semiopen',
          query: {
            types: 'partiallyOpen'
          }
        },
        {
          urlPathKey: t(gridCategories.wardrobe.urlPathKey),
          name: 'wardrobe___open',
          nameKey: 'common.megamenu.categories.open_wardrobes',
          descriptionKey: 'common.category.wardrobe_description',
          imagePath: 'common/menu/categories/wardrobes_opened',
          query: {
            types: 'fullyOpen'
          }
        }
      ]
    },
    smoothFeatureFlag && {
      name: NavigationCategoriesNames.SOFA,
      nameKey: SOTTY_CATEGORIES.sofas.pluralNameKey,
      descriptionKey: 'common.category.sotty.sofas_description',
      imagePath: 'common/menu/categories/sofa',
      urlPathKey: SOTTY_CATEGORIES.sofas.urlPathKey,
      children: [
        pick(SOTTY_CATEGORIES.two_seater, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        pick(SOTTY_CATEGORIES.three_seater, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        pick(SOTTY_CATEGORIES.four_plus_seater, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        pick(SOTTY_CATEGORIES.chaise_longue, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        pick(SOTTY_CATEGORIES.corner, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        pick(SOTTY_CATEGORIES.armchair, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath'])
        // pick(SOTTY_CATEGORIES.footrests_and_modules, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath']),
        // pick(SOTTY_CATEGORIES.cover, ['urlPathKey', 'name', 'nameKey', 'descriptionKey', 'imagePath'])
      ]
    }
  ].filter(Boolean)
}];

export function findBreadCrumbsByName (
  categories: any,
  targetName: string,
  parentPath: any[] = []
): any | null {
  for (const category of categories) {
    if (category.name === targetName) {
      parentPath.unshift(category);
      return parentPath;
    } else if (category.children) {
      const nestedResult = findBreadCrumbsByName(category.children, targetName, parentPath);

      if (nestedResult?.length) {
        parentPath.unshift(category);
        return parentPath;
      }
    }
  }

  return null;
}
