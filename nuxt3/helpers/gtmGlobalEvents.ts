import { type GlobalState } from '~/stores/global';
import { getPLPCategoryTranslations } from '~/composables/useProductUrl';

// addNewLanguage
const contentGroupingDict: {[key: string]: string} = {
  '.*/(etageres|regale|shelves|products|produits|produkte|furniture-c|mobel-c|meubles-c|muebles-c|meubels-c|mobili-c|mobler-c)(/|\\?|$).*': 'Category Page',
  '.*/journal.*': 'Blog Page',
  'assembly-service': 'Assembly Service Page',
  '.*/(payment_method|payment_methods)/.*': 'Payment Method Page',
  '.*/(checkout|betaal)/.*': 'Checkout Page',
  '.*/(terms|terminos|voorwaarden)/.*': 'Terms Of Service Page',
  '.*/(shipping|envio|verzenden)/.*': 'Shipping Page',
  '.*/(review-list|lista-resenas|bekijk-lijst).*': 'Reviews Page',
  '.*/(product-lines|produktlinien|gammes-de-produits|productportfolio|lineas-de-productos)/.*': 'Comparison (Product Lines)',
  '.*/(contact|contacto)/.*': 'Contact Page',
  '.*/(library|wishlist)/.*': 'Wishlist Page',
  '.*/(sale|reduction|venta|uitverkoop).*': 'Promo LP',
  '.*/(categorie|kategorie|category|categoria|kledingkast)/.*': 'Category Landing Page',
  '.*/(gallery|galeria)/.*': 'Gallery',
  '.*/(echantillon-de-matiere|material-samples|muster|muestras-de-material|materiaal-staaltje)/.*': 'Material Samples',
  '.*/(rooms|pieces|raeume|habitaciones|kamers)/.*': 'Spaces Page',
  '.*(tylko-for-business|tylko-business|tylko-fur-unternehmen|tylko-para-empresas|tylko-voor-bedrijven).*': 'B2B Page',
  '.*/(etagere|regal|shelf|wardrobe|furniture|mobel|meuble|mueble|meubel|mobler|mobili)/.*': 'Product Page',
  '/(our-mission|de/unsere-mission|fr/mission|nuestra-mision)/.*': 'Our Mission Page',
  '/(|(de|fr|es|nl)/)faq/': 'FAQ Page',
  '/(|(de|fr|es|nl)/)account/': 'Account Page',
  '.*/review-form/.*': 'Review Form',
  '.*/cart/.*': 'Cart Page',
  '.*/visit-our-showroom/.*': 'Showroom Page',
  '.*/delivery-time-frames/.*': 'Delivery Time Frames',
  '.*/error-404/(.*/|)payment_methods/.*': 'Error on payment method - return with cart drop',
  '.*register.*': 'Register or Login Page',
  '/error.*': 'Error Page',
  '.*blackfriday.*': 'Black Friday',
  '.*/subscription-confirmation/.*': 'Subscription',
  '.*confirmation.*': 'Purchase',
  '/(|(en|de|fr|es|nl|pl|it)/)': 'Home Page'
};

type Category = {
  category: string;
  en: string;
  es: string;
  nl: string;
  de: string;
  fr: string;
  da: string;
  sv: string;
  it: string;
};

type Categories = {
  'Category Page': Category[];
  'Product Page': Category[];
};

// addNewLanguage
const categories: Categories = {
  'Category Page': getPLPCategoryTranslations(),
  'Product Page': [
    {
      category: 'sideboard',
      en: 'sideboard',
      es: 'consola',
      nl: 'sideboard',
      de: 'sideboard',
      fr: 'etagerebasse',
      da: 'skænk',
      sv: 'skank',
      it: 'credenza'
    },
    {
      category: 'bookcase',
      en: 'bookcase',
      es: 'libreria',
      nl: 'boekenkast',
      de: 'bucherregal',
      fr: 'bibliotheque',
      da: 'bogreol',
      sv: 'Bokhylla',
      it: 'libreria'
    },
    {
      category: 'wallstorage',
      en: 'wall-storage',
      es: 'estanteria',
      nl: 'wandopslag',
      de: 'regalsystem',
      fr: 'etagere-murale',
      da: 'vægopbevaring',
      sv: 'Hylla',
      it: 'parete-attrezzata'
    },
    {
      category: 'wardrobe',
      en: 'wardrobe',
      es: 'armario',
      nl: 'kledingkast',
      de: 'kleiderschrank',
      fr: 'dressing',
      da: 'garderobeskab',
      sv: 'garderob',
      it: 'armadio'
    },
    {
      category: 'desk',
      en: 'desk',
      es: 'escritorio',
      nl: 'bureau',
      de: 'schreibtisch',
      fr: 'bureau',
      da: 'skrivebord',
      sv: 'skrivbord',
      it: 'scrivania'
    },
    {
      category: 'tvstand',
      en: 'tv-stand',
      es: 'mueble-de-tv',
      nl: 'tv-meubel',
      de: 'fernsehtisch',
      fr: 'meuble-tv',
      da: 'tv-bord',
      sv: 'tv-bank',
      it: 'mobile-TV'
    },
    {
      category: 'chest',
      en: 'chest-of-drawers',
      es: 'cajonera',
      nl: 'ladekast',
      de: 'kommode',
      fr: 'commode',
      da: 'kommode',
      sv: 'byra-med-lador',
      it: 'cassettiera'
    },
    {
      category: 'shoerack',
      en: 'shoe-rack',
      es: 'zapatero',
      nl: 'schoenenkast',
      de: 'schuhregal',
      fr: 'meuble-a-chaussures',
      da: 'skoreol',
      sv: 'skohylla',
      it: 'scarpiera'
    },
    {
      category: 'vinyl_storage',
      en: 'vinyl-storage',
      es: 'estanteria-para-vinilos',
      nl: 'platenkast',
      de: 'schallplattenregal',
      fr: 'rangement-vinyle',
      da: 'vinylopbevaring',
      sv: 'vinyl-forvaring',
      it: 'mobile-per-vinili'
    },
    {
      category: 'bedside_table',
      en: 'bedside-table',
      es: 'mesita-de-noche',
      nl: 'nachtkastje',
      de: 'nachttisch',
      fr: 'table-de-chevet',
      da: 'sengebord',
      sv: 'sangbord',
      it: 'comodino'
    }
  ]
};

const findCategory = (
  pageType: 'Category Page' | 'Product Page',
  lang: string,
  translatedCategory: string
): string | null => {
  const pageCategories = categories[pageType];

  if (!pageCategories) {
    return null;
  }

  for (const cat of pageCategories) {
    if (cat[lang] === translatedCategory) {
      return cat.category;
    }
  }

  return null;
};

const getContentGrouping = (url: string): string | null => {
  for (const regex in contentGroupingDict) {
    if (new RegExp(regex).test(url)) {
      return contentGroupingDict[regex];
    }
  }

  return null;
};

export default function (store: GlobalState, gtm: any, i18n: any, route: any) {
  const { params: { category } } = route;
  const { locale } = useLocale();
  return {
    pageView: (
      prevUrl: string | null,
      pageUrl: string,
      initialPage: boolean = false
    ) => {
      if (process.client) {
        if (initialPage) {
          prevUrl = document.referrer;
        }

        const pageType = getContentGrouping(pageUrl);

        const langCode = locale.value;
        const width = window.innerWidth;
        const height = window.innerHeight;
        let properCategory = null;

        if (category && (pageType === 'Category Page' || pageType === 'Product Page')) {
          properCategory = findCategory(pageType, locale.value, category);
        }

        if (!category && pageType === 'Category Page') {
          properCategory = 'All products';
        }

        gtm?.push({
          event: 'page_view',
          globalSessionId: store.globalSessionId,
          language_version: langCode,
          region_name: store.regionName,
          ab_test: store.abIds,
          page_url: pageUrl,
          product_category: properCategory || store.pdpProductCategory,
          screen_resolution: `${width}x${height}`,
          canonical_url: document.querySelector("link[rel='canonical']")?.getAttribute('href'),
          page_type: pageType,
          virtual_referrer: prevUrl,
          user_logged_in: store.isSignedIn ? 'yes' : 'no'
        });
      }
    }
  };
}
