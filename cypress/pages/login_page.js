import CommonPage from "./common_page"

class LoginPage extends CommonPage {

  url = '/register-or-login'
  urlLogin = '/login'

  selectors = {
    email_login_input: '[data-testid=input-login-email]',
    password_login_input: '[data-testid=input-login-password]',
    login_button: '[data-testid=button-login]',
    account_link: '[data-testid=redirect-account]',
    create_account_button: '[data-testid=create-account-button]',
    password_reminder_button: '[data-testid="button-forgotten-password"]',
    continue_as_guest: '[data-testid="continue-as-guest"]',
  }

  fillLogin(email) {
    cy.get(this.selectors.email_login_input)
      .type(email, { timeout:10000 }).should('have.value', email)
  }

  fillPassword(password) {
    cy.get(this.selectors.password_login_input)
      .type(password, { timeout:10000 }).should('have.value', password)
  }

  loginUser(email, password) {
    this.fillLogin(email)
    cy.wait(500)
    cy.get(this.selectors.login_button).click()
    // page is changing here
    this.fillPassword(password)
    cy.get(this.selectors.login_button).click()
  }

  sendPasswordReminder() {
    cy.get(this.selectors.password_reminder_button).click()
  }

  continueAsGuest() {
    cy.get('[data-testid="continue-as-guest"]').click()
  }
}

export default new LoginPage()
