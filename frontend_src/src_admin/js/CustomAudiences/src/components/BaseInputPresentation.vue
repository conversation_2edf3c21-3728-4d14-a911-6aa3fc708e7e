<template>
    <q-input
        v-model="vValue"
        class="q-px-md q-py-lg"
        :disable="true"
        :label="label"
    />
</template>

<script>
import { ref } from "vue";

export default {
    name: 'BaseInputPresentation',
    props: {
        valueIn: {
            type: [String, Number],
            default: 'None',
        },
        label: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const vValue = ref(props.valueIn);

        return {
            vValue,
        };
    },
};
</script>
