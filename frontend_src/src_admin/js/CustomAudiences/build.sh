ROOT_DIR="../../../../"
SOURCE_DIR="frontend_src/src_admin/js/CustomAudiences/"
STATIC_DIR="src/custom_audiences/static/custom_audiences/"
# go to project root
cd $ROOT_DIR || exit
# remove old version
git rm -r $STATIC_DIR
# make directory for new panel
mkdir $STATIC_DIR
# go to CustomAudiences dir and make build
cd $SOURCE_DIR && npm run build
# go to project root
cd $ROOT_DIR || exit
# add new build to git
git add $STATIC_DIR
# back to CustomAudiences dir
cd $SOURCE_DIR || exit
