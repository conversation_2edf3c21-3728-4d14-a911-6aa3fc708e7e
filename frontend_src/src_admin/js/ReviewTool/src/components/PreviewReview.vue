<template>
    <section
        v-if="review.photos.length < 1"
        class="pdp-2018 reviews"
    >
        <div class="pdp-reviews-slider">
            <div class="main-carousel reviews-carousel">
                <div class="card text">
                    <div>
                        <div class="card-content">
                            <div class="shadow-wrapper">
                                <div class="headline tmt-l">
                                    <div class="rating">
                                        <ul>
                                            <li
                                                v-for="(x, i) in review.score"
                                                :key="i"
                                                class="active"
                                            >
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 32 32"
                                                >
                                                    <path
                                                        d="M16 21.445l-7.053 4.359 2.146-7.776-6.506-5.082 8.38-.447L16 5l3.033 7.5 8.38.446-6.506 5.082 2.146 7.776z"
                                                    />
                                                </svg>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="tfc-grey-1300 th-4-m th-4-t tmb-s">
                                        {{ review.title }}
                                    </h2>
                                    <p class="tp-default-m tp-default-t tfc-grey-1000">
                                        {{ review.description }}
                                    </p>
                                </div>
                            </div>
                            <div class="user flex">
                                <img
                                    src="https://tylko.com/r_static/icons/icon-verified.svg"
                                    width="21"
                                    height="21"
                                    alt=""
                                >
                                <span class="tp-sub-m tp-sub-t name">
                                    <span class="tfc-grey-1150">{{ review.name }}</span>
                                    from {{ review.country }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section
        v-else
        class="reviews-2020"
    >
        <div class="pdp-reviews-slider pdp-reviews-slider--desktop">
            <div class="main-carousel reviews-carousel">
                <div class="carousel-cell">
                    <div
                        class="card"
                        :data-review-id="review.id"
                    >
                        <div>
                            <div class="photo">
                                <img
                                    style="width: 100%; height: 100%;"
                                    :src="review.photos[0].image"
                                >
                            </div>
                            <div class="card-content tpb-s tpt-s tpl-s tpr-s">
                                <div class="headline">
                                    <div class="rating">
                                        <ul>
                                            <li
                                                v-for="(x, i) in review.score"
                                                :key="i"
                                                class="active"
                                            >
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 32 32"
                                                >
                                                    <path
                                                        d="M16 21.445l-7.053 4.359 2.146-7.776-6.506-5.082 8.38-.447L16 5l3.033 7.5 8.38.446-6.506 5.082 2.146 7.776z"
                                                    />
                                                </svg>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="th-4-m th-5-t tmb-xs title">
                                        {{ review.title }}
                                    </h2>
                                    <p class="tp-small-m description">
                                        {{ review.description }}
                                    </p>
                                </div>
                            </div>
                            <div class="user flex tpl-s tpr-s tpt-s tpb-s position-relative">
                                <span class="pdp-2018-tooltip-trigger position-relative">
                                    <img
                                        src="https://tylko.com/r_static/icons/icon-verified.svg"
                                        width="21"
                                        height="21"
                                        alt=""
                                    >
                                </span>
                                <span class="tp-sub-m tp-sub-t name">
                                    <span class="tfc-grey-1150">{{ review.name }}</span>
                                    from {{ review.country }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    name: 'PreviewReview',
    props: {
        review: {
            type: Object,
            default: () => ({}),
        },
        mainReviewPicture: {
            type: String,
            default: '',
        },
    },
};
</script>
<style lang="scss" scoped>
@import "../../../../../src/scss/style";

section.reviews {
  padding-bottom: 0;
  max-width: 400px !important;
  overflow: hidden;
}

.reviews-2020 {
  background-image: none;
  width: auto;
}
</style>
