# ReviewTool
####(Quasar App review_tool)

How to find ReviewTool?<br><br>
Here: `/admin/reviews/review/` You'll find a large button: `Open ReviewTool`
or You can just simply go here: `/admin/review_tool/`

Don't forget to `npm run build` after the changes.
## Install the dependencies
```bash
npm install
```

### Start the app in development mode (hot-code reloading, error reporting, etc.)
```bash
npm run dev
```

### Lint the files
```bash
npm run lint
```

### Build the app for production
```bash
npm run build
```

