<!DOCTYPE html>
<html>
  <head>
    <title><%= htmlWebpackPlugin.options.productName %></title>

    <meta charset="utf-8">
    <meta name="description" content="<%= htmlWebpackPlugin.options.productDescription %>">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width<% if (htmlWebpackPlugin.options.ctx.mode.cordova || htmlWebpackPlugin.options.ctx.mode.capacitor) { %>, viewport-fit=cover<% } %>">

    <link rel="icon" type="image/png" href="statics/app-logo-128x128.png">
    <link rel="icon" type="image/png" sizes="16x16" href="statics/icons/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="statics/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="statics/icons/favicon-96x96.png">
    <link rel="icon" type="image/ico" href="statics/icons/favicon.ico">
    <script type="text/javascript">
        window.LOGISTIC_URL = "{{ LOGISTIC_URL }}"
        window.USER_TOKEN = "{{ user.auth_token.key }}"
    </script>
  </head>
  <body>
    <!-- DO NOT touch the following DIV -->
    <div id="q-app"></div>
  </body>
</html>
