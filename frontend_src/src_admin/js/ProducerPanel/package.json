{"name": "producers", "version": "0.0.1", "description": "producer panel", "productName": "Producer<PERSON><PERSON>l", "cordovaId": "", "capacitorId": "", "author": "<EMAIL>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue src", "test": "echo \"No test specified\" && exit 0", "build": "quasar build && cp -R ./dist/spa/* '../../../../src/producers/static/new_panel/'"}, "dependencies": {"@quasar/cli": "^1.3.2", "@quasar/extras": "^1.0.0", "axios": "^0.26.1", "quasar": "^1.4.1"}, "devDependencies": {"@quasar/app": "^1.2.3", "@vue/eslint-config-airbnb": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^6.8.0", "eslint-loader": "^3.0.3", "eslint-config-airbnb-base": "^13.2.0", "eslint-plugin-vue": "^6.1.2", "strip-ansi": "=3.0.1"}, "engines": {"node": ">= 8.9.0", "npm": ">= 5.6.0", "yarn": ">= 1.6.0"}, "browserslist": ["last 1 version, not dead, ie >= 11"]}