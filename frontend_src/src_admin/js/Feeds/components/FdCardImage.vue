<template>
    <div class="fd-card-image" v-bind:class="{'fd-tablle-row_selected' : selected }" 
    v-on:click="toogleSelected(itemInCard.shelfId)" >
      <img class="fd-card-image_preview" :src="itemInCard.src" alt="">
			<img v-show="selected" class="fd-card_check" src="/r_static/icons/icon-check-green.svg" width="60" height="30" alt="">
    </div>
</template>
<script>
import FdTableCellInfo from "./FdTableCellInfo";
import FdTableCellTechnical from "./FdTableCellTechnical";
import FdTableCellImage from "./FdTableCellImage";
import { EventBus } from "../utils/EventBus";

export default {
  data() {
    return {
      selected: this.itemInCard.isSelected
    };
  },
  components: {
    FdTableCellInfo,
    FdTableCellImage,
    FdTableCellTechnical
  },
  props: ["itemInCard", "itemIndex"],
  methods: {
    toogleSelected(shelfId) {
      this.selected = !this.selected;
      EventBus.$emit("SelectShelf", {
        shelfId: shelfId,
        value: this.selected
      });
    }
  }
};
</script>


<style lang="scss">
@import "../scss/variables";

.fd-card-image {
  display: block;
  width: 400px;
  height: 300px;
  float: left;
  overflow: hidden;
  border: 1px solid $color-gray-border;
  border-radius: $border-radius;
  margin: 10px;
  transition: all 0.1s ease-in;
  position: relative;
  .fd-card-image_preview {
    max-width: 100%;
  }
  .fd-card_check {
    position: absolute;
    left: 0px;
    bottom: 20px;
    transition: all 0.1s ease-in;
    z-index: 1;
  }
  &.fd-tablle-row_selected {
    border: 1px solid $color-confirm;
  }
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2);
  }
}
</style>
