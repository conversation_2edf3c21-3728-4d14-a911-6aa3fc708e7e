<template>
  <div class="fd-sort-bar row">
	  <div class="col-xs-3">
      <div class="fd-view-switch">
        <span v-on:click="onChangeViewMode('list')">
          <img class="fd-view-switch_icon" 
            v-bind:class="{ 'fd-view-switch_selected' : listView }" 
            src="/r_static/icons/icon-list-view.svg" 
            width="18" 
            height="18" 
            alt="List View">
        </span>
        <span v-on:click="onChangeViewMode('grid')">
          <img 
            class="fd-view-switch_icon" 
            v-bind:class="{ 'fd-view-switch_selected' : gridView }" 
            src="/r_static/icons/icon-grid-view.svg" 
            width="18" 
            height="18" 
            alt="Grid View">
        </span>
        <span v-on:click="onChangeViewMode('image')">
          <img 
            class="fd-view-switch_icon" 
            v-bind:class="{ 'fd-view-switch_selected' : imageView }" 
            src="/r_static/icons/icon-image-view.svg" 
            width="26" 
            height="26" 
            alt="Image View">
        </span>
      </div>
		  <span class="fd-sort-bar_label">Sort by: </span>
      <select name="" id="sort-by" @input="onSelectChange($event)">
        <option value="id">Id</option>
        <option value="price">From lowest price</option>
        <option value="-price">From highest price</option>
        <option value="created_at">From oldest date</option>
        <option value="-created_at">From latest date</option>
        <option value="avg_rating">From lowest Rating</option>
        <option value="-avg_rating">From highest rating</option>
      </select>
    
	  </div>
    <div class="col-xs-6">
      <slot></slot>
    </div>
	  <div class="fd-sort-bar_result col-xs-3 pull-right">
		  Results: {{ count }}
	  </div>
  </div>
</template>
<script>
import { EventBus } from "../utils/EventBus";
export default {
  props: ["count", "viewMode"],
  computed: {
    gridView() {
      return this.viewMode === "grid";
    },
    listView() {
      return this.viewMode === "list";
    },
    imageView() {
      return this.viewMode === "image";
    }
  },
  methods: {
    onSelectChange($event) {
      EventBus.$emit("OrderList", { orderingValue: $event.target.value });
    },
    onChangeViewMode(viewMode) {
      EventBus.$emit("ChangeViewMode", { viewMode: viewMode });
    }
  }
};
</script>
<style lang="scss" scoped>
@import "../scss/variables";
.fd-sort-bar {
  padding: 10px 0;
  background: #d6d6d6;
  position: relative;
  z-index: 2;
  .fd-sort-bar_label {
    margin-left: 15px;
  }
  .fd-sort-bar_result {
    line-height: 30px;
    font-size: 24px;
    text-align: right;
    line-height: 40px;
  }
  .fd-sort-bar_label {
    font-weight: 500;
    line-height: 40px;
  }
  .fd-view-switch {
    display: inline-block;
    height: 24px;
    .fd-view-switch_icon {
      opacity: 0.4;
      margin-right: 5px;
      &.fd-view-switch_selected {
        opacity: 1;
      }
    }
  }
}
</style>

