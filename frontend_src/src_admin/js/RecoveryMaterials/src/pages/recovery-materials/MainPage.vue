<template>
  <q-page class="q-pa-lg">
    <BaseRecoveryMaterialsTable
      v-bind="{
        tableButtons,
        fetchData,
        tableData,
        loading,
        columns
      }"
      ref="table"
      title="Odzysk materiałów"
    >
      <template #body-cell-report="props">
        <q-td
          v-bind:props="props"
          class="table-details q-gutter-sm"
        >
          <a
            v-if="props.row.recovery_report"
            v-bind:href="`${props.row.recovery_report.manufactor_report}`"
          >
            {{ props.row.recovery_report.id }}
          </a>
          <span v-else>-</span>
        </q-td>
      </template>
    </BaseRecoveryMaterialsTable>
    <q-dialog v-model="addItemModal">
      <div class="modal-wrapper q-pa-lg bg-white">
        <AddProductForRecovery
          v-bind:close-modal="closeModals"
        />
      </div>
    </q-dialog>
    <q-dialog
      v-model="generateReportModal"
    >
      <div class="modal-wrapper bg-white">
        <CreateRecoveryReport
          v-bind:product-ids="selectedIds"
          v-bind:close-modal="closeModals"
        />
      </div>
    </q-dialog>
  </q-page>
</template>

<script>
import BaseRecoveryMaterialsTable from 'components/BaseRecoveryMaterialsTable.vue';
import AddProductForRecovery from 'components/AddProductForRecovery.vue';
import CreateRecoveryReport from 'components/CreateRecoveryReport.vue';
import { date } from 'quasar';

export default {
  name: 'RecoveryMaterials',
  components: {
    CreateRecoveryReport,
    BaseRecoveryMaterialsTable,
    AddProductForRecovery,
  },
  data() {
    return {
      selectedIds: [],
      tableButtons: [
        {
          label: 'Dodaj pozycję',
          icon: 'add',
          handleClick: () => { this.addItemModal = true; },
          enableOnOnlySelectedItems: false,
        },
        {
          label: 'Usuń pozycję',
          icon: 'delete',
          handleClick: selectedIds => this.markAsNotRecovered(selectedIds),
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },
        {
          label: 'Wygeneruj raport',
          icon: 'description',
          handleClick: selectedIds => {
            this.generateReportModal = true;
            this.selectedIds = selectedIds;
          },
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },

      ],
      tableData: [],
      columns: [
        {
          name: 'transportNumber',
          align: 'center',
          label: 'ID regału',
          field: ({ id }) => id,
        },
        {
          name: 'manufactor',
          align: 'center',
          label: 'Producent',
          field: ({ manufactor }) => manufactor,
        },
        {
          name: 'color',
          align: 'center',
          label: 'Kolor',
          field: ({ color }) => color,
        },
        {
          name: 'cachedDepth',
          align: 'center',
          label: 'Głębokość',
          // eslint-disable-next-line camelcase
          field: ({ depth }) => depth,
        },
        {
          name: 'createdAt',
          align: 'center',
          label: 'Data utworzenia',
          // eslint-disable-next-line camelcase
          field: ({ recovered_at }) => date.formatDate(recovered_at, 'DD-MM-YYYY HH:mm'),
        },
        {
          name: 'carrier',
          align: 'center',
          label: 'Typ wysyłki',
          field: ({ carrier }) => carrier || '-',
        },
        {
          name: 'report',
          align: 'center',
          label: 'Raport',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? recovery_report.id : '-'),
        },
        {
          name: 'reportCreatedAt',
          align: 'center',
          label: 'Data utworzenia raportu',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? date.formatDate(recovery_report.created_at, 'YYYY-MM-DD HH:mm') : '-'),
        },
      ],
      loading: true,
      addItemModal: false,
      generateReportModal: false,
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      this.$axios.get('/api/v1/product_material_recovery/?is_recovered=true')
        .then(resp => {
          this.tableData = resp.data;
          this.loading = false;
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    markAsNotRecovered(selectedIds) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz usunąć pozycję?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios.post(`/api/v1/product_material_recovery/mark_as_not_recovered/?ids=${selectedIds}`)
          .then(() => {
            this.selectedIds = [];
            this.loading = false;
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          });
      });
    },
    closeModals() {
      this.addItemModal = false;
      this.generateReportModal = false;
      this.$refs.table.resetSelection();
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
.modal-wrapper {
  min-width: 400px;
  width: 50vw;
}
</style>
