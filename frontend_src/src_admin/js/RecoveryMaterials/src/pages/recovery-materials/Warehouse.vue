<template>
  <q-page class="q-pa-lg">
    <div class="q-gutter-md row">
      <q-select
        v-model="tableType"
        v-bind:options="tableTypeOptions"
        label="Typ elementów w magazynie"
        class="warehouse-select"
      />
      <q-select
        v-model="selectedColor"
        clearable
        v-bind:options="availableColors"
        label="Kolor elementów"
        class="warehouse-select"
      />
      <q-btn
        v-bind:disable="!tableType"
        class="q-mt-lg bg-indigo-9 text-white"
        v-on:click="fetchData"
      >
        Szukaj elementów
      </q-btn>
    </div>
    <BaseRecoveryMaterialsTable
      v-if="isFetched"
      v-bind="{
        selection: 'single',
        title: tableType.value,
        fetchData,
        tableData,
        tableButtons,
        loading,
        columns: columnsForTableType[tableType.value]
      }"
    />
    <q-dialog v-model="decreaseAmount">
      <div class="modal-wrapper q-pa-lg bg-white">
        <DecreaseAmountFromWarehouse
          v-bind="{
            closeModal,
            warehouseElementsIds: selectedIds,
          }"
        />
      </div>
    </q-dialog>
  </q-page>
</template>

<script>
import BaseRecoveryMaterialsTable from 'components/BaseRecoveryMaterialsTable.vue';
import DecreaseAmountFromWarehouse from 'components/DecreaseAmountFromWarehouse.vue';

export default {
  name: 'Warehouse',
  components: {
    DecreaseAmountFromWarehouse,
    BaseRecoveryMaterialsTable,
  },
  data() {
    return {
      selectedIds: [],
      decreaseAmount: false,
      tableButtons: [
        {
          label: 'Zdejmij ze stanu',
          icon: 'remove',
          handleClick: selectedIds => {
            this.decreaseAmount = true;
            this.selectedIds = selectedIds;
          },
          enableOnOnlySelectedItems: true,
        }],
      maxAvailableValues: {},
      loading: true,
      tableData: [],
      isFetched: false,
      tableType: null,
      tableTypeOptions: [
        {
          label: 'Elementy',
          value: 'elements',
        },
        {
          label: 'Okucia',
          value: 'fittings',
        },
        {
          label: 'Sample',
          value: 'samples',
        },
      ],
      availableColors: [],
      selectedColor: null,
      columnsForTableType: {
        elements: [
          {
            name: 'codename',
            align: 'center',
            label: 'codename',
            // eslint-disable-next-line camelcase
            field: ({ codename }) => codename,
            style: 'width: 400px',
            headerStyle: 'width: 400px',
          },
          {
            name: 'amount',
            align: 'center',
            label: 'Ilość',
            field: ({ amount }) => amount,
            style: 'width: 400px',
            headerStyle: 'width: 400px',
          },
        ],
        fittings:
            [
              {
                name: 'codename',
                align: 'center',
                label: 'codename',
                // eslint-disable-next-line camelcase
                field: ({ codename }) => codename,
                style: 'width: 400px',
                headerStyle: 'width: 400px',
              },
              {
                name: 'amount',
                align: 'center',
                label: 'Ilość',
                field: ({ amount }) => amount,
                style: 'width: 400px',
                headerStyle: 'width: 400px',
              },
            ],
        samples:
            [
              {
                name: 'color',
                align: 'center',
                label: 'kolor',
                // eslint-disable-next-line camelcase
                field: ({ color }) => color,
                style: 'width: 400px',
                headerStyle: 'width: 400px',
              },
              {
                name: 'amount',
                align: 'center',
                label: 'Ilość',
                field: ({ amount }) => amount,
                style: 'width: 400px',
                headerStyle: 'width: 400px',
              },
            ],
      },
    };
  },
  created() {
    this.getAvailableColors();
  },
  methods: {
    closeModal() {
      this.decreaseAmount = false;
    },
    getMaxValuesForElements(data) {
      return data.reduce((acc, productInfo) => {
        acc[productInfo.id] = productInfo.amount;
        return acc;
      }, []);
    },
    fetchData() {
      this.loading = true;
      this.$axios.get(`/api/v1/product_material_recovery/warehouse?type=${this.tableType.value}&color=${(this.selectedColor && this.selectedColor.value) || ''}`)
        .then(response => {
          this.tableData = response.data;
          this.loading = false;
          this.isFetched = true;
          this.maxAvailableValues = this.getMaxValuesForElements(response.data);
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    getAvailableColors() {
      this.$axios.get('/api/v1/product_material_recovery/product_material_recovery_configuration/')
        .then(response => {
          this.availableColors = [...response.data.color_options.map(({ name, value }) => ({
            label: name,
            value,
          }))];
        }).catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
  },
};
</script>
<style scoped lang="scss">
.warehouse-select {
  width: 350px;
}
.table-column {
  width: 350px;
}
.modal-wrapper {
  width: 400px;
}
</style>
