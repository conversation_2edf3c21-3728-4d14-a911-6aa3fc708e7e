<template>
  <q-table
    class="sticky-header-table"
    v-bind:title="title"
    row-key="id"
    v-bind:data="tableData"
    v-bind:columns="columns"
    v-bind:loading="loading"
    loading-label="Pobieranie danych..."
    rows-per-page-label="Ilość na stronie:"
    v-bind:rows-per-page-options="[25, 10, 50, 100, 0]"
    selection="multiple"
    v-bind:selected.sync="selected"
    flat
    bordered
    separator="cell"
  >
    <template #top>
      <q-btn
        v-for="(btn, index) in tableButtons"
        v-bind:key="`table-btn-${index}`"
        v-bind:label="btn.label"
        v-bind:icon="btn.icon"
        v-bind:disable="(btn.disableFunction && btn.disableFunction(selected)) || (btn.enableOnOnlySelectedItems && (loading || selected.length === 0))"
        flat
        dense
        color="primary"
        v-on:click="btn.handleClick(getSelectedIds())"
      />
    </template>
    <template
      v-for="(_, slot) of $scopedSlots"
      #[slot]="scope"
    >
      <slot
        v-bind:name="slot"
        v-bind="scope"
      />
    </template>
  </q-table>
</template>
<script>
export default {
  name: 'BaseRecoveryMaterialsTable',
  props: {
    title: {
      type: String,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    fetchData: {
      type: Function,
      required: true,
    },
    tableButtons: {
      type: Array,
      required: false,
      default: () => [],
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
    tableData: {
      type: Array,
      required: false,
      default: () => [],
    },
    selection: {
      type: String,
      required: false,
      default: 'multiple',
    },
  },
  data() {
    return {
      pagination: {
        rowsPerPage: 250,
      },
      selected: [],
      filter: '',
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    getSelectedIds() {
      return this.selected.map(selectedItem => selectedItem.id);
    },
    resetSelection() {
      this.selected = [];
    },
  },
};
</script>
