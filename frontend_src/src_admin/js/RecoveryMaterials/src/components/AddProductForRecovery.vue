<template>
  <section class="column">
    <div class="text-h6">
      <PERSON><PERSON><PERSON>
    </div>
    <ProductInfo
      v-bind:set-searched-product-id="setSearchedProductId"
    />
    <q-btn
      v-if="searchedProductId"
      class="q-mt-lg bg-indigo-9 text-white"
      label="Dodaj znaleziony produkt"
      v-on:click="addProductForRecovery"
    />
  </section>
</template>
<script>
import ProductInfo from './ProductInfo.vue';

export default {
  name: 'AddProductForRecovery',
  components: {
    ProductInfo,
  },
  props: {
    closeModal: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      searchedProductId: null,
    };
  },
  methods: {
    setSearchedProductId(productId) {
      this.searchedProductId = productId;
    },
    addProductForRecovery() {
      this.$axios.post(`/api/v1/product_material_recovery/${this.searchedProductId}/mark_as_recovered/`)
        .then(() => {
          this.$q.notify({ message: 'Pomyślnie dodano produkt', type: 'positive' });
          this.closeModal();
        })
        .catch(e => {
          if (e.response.status === 400) {
            const responseMessage = e.response.data.non_field_errors && e.response.data.non_field_errors.join(', ');
            this.$q.notify({ message: responseMessage || 'Wystąpił błąd', type: 'negative' });
          } else {
            this.$q.notify({ message: `Wystąpił błąd - ${e.message}`, type: 'negative' });
          }
        });
    },

  },
};

</script>
