<template>
    <div class="container container-main container-shelf">
        <div class="row">
            <div class="col-xs-12 text-center">

                <h2 class="ht-card-title">{{ currentShelf.category }}</h2>

                <div class="ht-card m-b-lg">

                    <div class="ht-image-container">

                        <img
                                class="ht-card-image"
                                v-bind:class="{ 'ht-fade-out': nextShelfFadesIn }"
                                :src="currentShelf.src"/>
                        <img
                                v-if="nextShelfSrc"
                                class="ht-card-image ht-card-image-clone"
                                v-bind:class="{ 'ht-next-shelf-fades-in': nextShelfFadesIn }"
                                :src="nextShelfSrc"/>

                        <div
                                class="ht-rating-displayed-on-shelf"
                                v-bind:class="{ [currentRatingClass]: ratingIsBeingSent, 'ht-rating-animation-appeared': ratingIsBeingSent }"></div>

                    </div>

                </div>

                <HtRatings
                        v-bind:ratings="ratings"
                        v-bind:ratingIsBeingSent="ratingIsBeingSent"
                        v-bind:currentShelf="currentShelf"
                        v-bind:currentRating="currentRating"/>


            </div>
        </div>
    </div>
</template>

<script>
    import HtRatings from './HtRatings.vue'

    export default {
        props: ['currentShelf', 'currentRating', 'nextShelfTemporary', 'nextShelfFadesIn', 'currentRatingClass', 'ratingIsBeingSent', 'ratings'],
        components: {
            HtRatings
        },
        computed: {
            nextShelfSrc: function () {
                return typeof this.nextShelfTemporary !== 'undefined' ? this.nextShelfTemporary.src : '';
            }
        },
        methods: {
        },
        created() {
            if ( window.scrollY > 0 ) {
                window.scrollTo(0, 0);
            }
        }

    }
</script>
