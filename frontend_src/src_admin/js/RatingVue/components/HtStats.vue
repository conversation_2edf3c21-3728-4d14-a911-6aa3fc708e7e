<template>

    <div class="container container-main container-stats">
        <div class="row">
            <div class="col-xs-12 text-center">

                <div class="ht-stats">

                    <h1 class="m-0 p-b-lg">Thank you! <br>Your stats look great.</h1>

                    <div class="row">

                        <div class="container-narrow p-b-lg">
                            <div class="col-xs-3 inline-block" v-for="(rating, index) in ratings.slice().reverse()">
                                <div class="ht-stats-graph"
                                     v-bind:style="{ height: getRatingPercentShare( rating.count ) + 'px'}"></div>
                                <div class="ht-stats-count">{{ rating.count }}</div>
                                <div
                                        class="ht-stats-icon"
                                        v-bind:class="`ht-rating-${rating.value}`">
                                </div>
                            </div>
                        </div>

                        <div class="container-narrow">

                            <transition name="fade" mode="out-in">

                                <HtButtonLoad v-bind:btnOutline="true"/>

                            </transition>
                            <!--<div class="ht-gif-container">-->
                                <!--<img-->
                                    <!--class="ht-gif"-->
                                    <!--v-bind:src="gifs[currentGif]" />-->
                            <!--</div>-->

                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
    import HtButtonLoad from './HtButtonLoad.vue'

    export default {
        props: ['ratings', 'statsViewCount', 'collectionIsEmpty'],
        components: {
            HtButtonLoad
        },
        data() {
            return {
                gifs: [
                    'https://m.popkey.co/c9e6d8/w6YQb.gif',
                    'https://i.pinimg.com/originals/9d/ca/b7/9dcab78d81ac6aaf5920c5a11bf5db58.gif',
                    'http://www.adventureseeker.org/wp-content/uploads/2013/01/tumblr_m3bw0myJNX1qe9eo3.gif'
                ],
            }
        },
        computed: {
            highestRatingCount: function () {
                let countsArr = [];
                let highestValue;
                Object.keys(this.ratings).forEach(item => countsArr.push(this.ratings[item].count));
                highestValue = Math.max.apply(Math, countsArr);

                return highestValue;

            },
            currentGif: function() {
                const index = ( this.statsViewCount - 1 ) % this.numberOfGifs;
                console.log(index);
                return index;
            },
            numberOfGifs: function() {
                return this.gifs.length;
            }
        },
        methods: {
            getRatingClass: function (rating) {
                return `ht-rating-${ rating.value }`
            },

            getRatingPercentShare: function (ratingCount) {
                const result = Math.round(parseInt(ratingCount, 10) * 100 / parseInt(this.highestRatingCount, 10));
                return result;
            },
        }
    }
</script>

<style lang="scss" scoped>
    .ht-gif {
        width: 100%;
        height: auto;

        &-container {
            overflow: hidden;
            border-radius: 6px;

        }
    }


</style>
