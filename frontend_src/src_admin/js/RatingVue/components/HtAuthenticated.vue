<template>
    <div class="container container-main container-login">
        <div class="row">
            <div class="col-xs-12 text-center">

                    <div class="ht-login container-narrow">
                        <h1 class="ht-login-text text-left m-b-xl">Welcome Back!<br><br>Continue to help us train our algorithms, so that they show only the most beautiful shelf presets to our customers!
                        </h1>

                        <HtButtonLoad v-bind:class="'m-b-xl'"/>
                        <HtButtonSecondary
                                v-bind:buttonText="'Logout'"
                                v-bind:eventToEmit="'logout'"
                                v-bind:extraMarginMobile="true"/>
                    </div>

            </div>
        </div>
    </div>
</template>

<script>
    import HtKeyboardShortcut from './HtKeyboardShortcut.vue'
    import HtButtonLoad from './HtButtonLoad.vue'
    import HtButtonSecondary from './HtButtonSecondary.vue'


    export default {
        props: ['isUserRegistered'],
        components: {
            HtButtonLoad,
            HtButtonSecondary
        },
        data() {
            return {
            }
        },
    }
</script>
