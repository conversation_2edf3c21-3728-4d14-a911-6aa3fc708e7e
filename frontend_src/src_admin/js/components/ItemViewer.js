import React from 'react';

class <PERSON>emViewer extends React.Component{
    constructor(props) {
        super(props);
        this.state = {
          actual_displayed: 0,
        };
        this.items_per_page = 10;
        this.handleNextPage = this.handleNextPage.bind(this);
        this.handlePrevPage = this.handlePrevPage.bind(this);
    }
    handleNextPage() {
        this.setState({
          actual_displayed: this.state.actual_displayed + 1,
        })
    }
    handlePrevPage() {
        this.setState({
          actual_displayed: this.state.actual_displayed - 1,
        })
    }
    render(){
        const items_per_page = 10;
        let filtered_items = this.props.items.slice(this.state.actual_displayed*items_per_page, (this.state.actual_displayed +1)*items_per_page)
        const keys = this.props.keys ? this.props.keys : Object.keys(this.props.items[0]);
        return (
                <table className="display table table-striped table-bordered">
                <tbody>
                <tr>
                    {keys.map((key_name,i)=>
                    {
                        return (<td key={i}>{key_name}</td>)
                    })}
                </tr>
                {filtered_items.map((item,i) =>
                {
                    return (
                        <tr key={item.id}>
                            {keys.map((value_name,ii) => {
                                    return (<td key={ii}>{item[value_name] != null && item[value_name] != undefined ? item[value_name].toString() : 'false'}</td>)
                                }
                            )}
                        </tr>)
                })}
                <tr>
                    <td> <input type='button' className="button" disabled = {this.state.actual_displayed == 0} value="Poprzednia strona" onClick={this.handlePrevPage}/></td>
                    <td> Aktualna - {this.state.actual_displayed}/{Math.ceil(this.props.items.length/this.items_per_page)}</td>
                    <td> <input type='button' className="button" disabled = {this.state.actual_displayed == Math.ceil(this.props.items.length/this.items_per_page)} value="Nastepna strona" onClick={this.handleNextPage}/></td>
                </tr>
                </tbody>
                </table>
            )
    }
};

export default ItemViewer;