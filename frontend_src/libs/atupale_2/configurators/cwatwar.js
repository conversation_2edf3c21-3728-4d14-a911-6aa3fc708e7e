import AtupaleCore from '../core';

function getGridWattyId() {
  // TODO: fetch gridID form url
  return -1;
}

class AtupaleCwatwar extends AtupaleCore {
  constructor() {
    super('configurator');
  }

  setupDataLayer() {
    const deliveryMax = document.getElementById('pdp-cplus').getAttribute('data-max-delivery');
    this.dataLayer = {
      eventParam_0: this.cstmItem.dna_object_id,
      eventParam_1: -1,
      eventParam_2: `${this.cstmItem.configurator_type}_${this.cstmItem.digital_product_version}_${this.cstmItem.physical_product_version}`,
      eventParam_3: -1,
      eventParam_4: -1,
      eventParam_5: this.getBoardId(),
      eventParam_6: getGridWattyId(),
      eventParam_7: undefined,
      eventParam_8: undefined,
      eventParam_9: undefined,
      eventParam_10: undefined,
      eventParam_11: undefined,
      eventParam_12: undefined,
      eventParam_13: undefined,
      eventParam_14: undefined,
      eventParam_15: undefined,
      eventParam_16: -1,
      eventParam_17: -1,
      eventParam_18: -1,
      eventParam_19: -1,
      eventParam_20: -1,
      eventParam_21: -1,
      eventParam_22: -1,
      eventParam_23: -1,
      eventParam_24: deliveryMax.length ? parseInt(deliveryMax, 10) : undefined,
    };
  }

  hitGlobalObjectData({ configuratorState, paramName, payload }) {
    this.configuratorState = configuratorState;

    if (
      paramName === 'thumbnail'
            || paramName === 'local_x'
            || paramName === 'doors_direction'

    ) {
      this.hitComponentData(paramName, payload);
      return;
    }
    this.dataLayer.eventParam_1 = -1;
    this.buildDataLayer(paramName, payload);
  }

  buildDataLayer(paramName, payload) {
    const {
      configuration, renderer, FPM,
      commonPrices: {
        price,
      },
    } = this.configuratorState;
    this.dataLayer.eventParam_3 = paramName;
    this.dataLayer.eventParam_4 = payload;
    this.dataLayer.eventParam_17 = renderer.material;
    this.dataLayer.eventParam_18 = configuration.drawer_style;
    this.dataLayer.eventParam_19 = price.priceGrossEur;
    this.dataLayer.eventParam_20 = configuration.width;
    this.dataLayer.eventParam_21 = configuration.height;
    this.dataLayer.eventParam_22 = configuration.depth;
    this.dataLayer.eventParam_23 = Object.keys(FPM.components).length;
    this.dataLayer.event = 'confEvent';

    this.dispatchData(this.dataLayer);
  }

  hitComponentData(paramName, payload) {
    if (!paramName) return;
    this.dataLayer.eventParam_1 = this.configuratorState.FPM.components[this.configuratorState.activeComponent].bi_info.component_id;
    this.buildDataLayer(paramName, payload);
  }

  async addToCart({ data, ecommerceHitType }) {

  }

  cameraInteraction({ eventLabel }) {
    this.dispatchData({
      event: 'userInteraction',
      eventType: 'NOEEC',
      eventFrameVersion: 1,
      eventCategory: 'conf options',
      eventAction: 'conf_Camera',
      eventLabel,
      eventParam_0: 'type03',
      eventParam_1: `${this.cstmItem.configurator_type}_${this.cstmItem.physical_product_version}`,
      eventNonInteraction: false,
    });
  }
}
export default AtupaleCwatwar;
