const Site = window.Site || {};

Site.multiSlides = (function() {
    const sliderCells = [...document.querySelectorAll('.multi-slides .common-slider__cell')];
    const sliderPill = document.querySelector('.multi-slides .common-slider__cell-pill');
    const videos = [];
    let lastActiveVideoIndex = null;
    let zIndexTop = 10;

    const handleChange = e => {
        const activeIndex = Number(e.target.getAttribute('data-index'));
        const areVideosNotPlaying = videos.every(item => item && item.state() !== 'playing');
        const video = videos[activeIndex];
        if (areVideosNotPlaying && video && lastActiveVideoIndex !== activeIndex) {
            video.play(0);
            video.container.style.zIndex = zIndexTop++;
            lastActiveVideoIndex = activeIndex;

            sliderCells.forEach((cell, index) => {
                window.Site.carouselsUtils.handlePill({ activeIndex, sliderCells, sliderPill });
                if (index === activeIndex) {
                    cell.classList.add('common-slider__cell--active');
                } else {
                    cell.classList.remove('common-slider__cell--active');
                }
            });
        }
    };
    function initialize() {
        // eslint-disable-next-line no-underscore-dangle
        window._wq = window._wq || [];
        _wq.push({
            id: 'multi-slides-video1',
            onReady(video) {
                videos[0] = video;
                video.mute();
                video.time(0);
            },
        });

        _wq.push({
            id: 'multi-slides-video2',
            onReady(video) {
                videos[1] = video;
                video.mute();
                video.time(0);
            },
        });

        window.Site.carouselsUtils.handlePill({ activeIndex: 0, sliderCells, sliderPill });


        sliderCells.forEach(cell => {
            cell.addEventListener('click', handleChange);
        });
    }

    return {
        initialize,
    };
}(jQuery));
