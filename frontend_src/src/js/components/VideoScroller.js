/*
 * Easing Functions - inspired from http://gizma.com/easing/
 * only considering the t value for the range [0, 1] => [0, 1]
 */
const EasingFunctions = {
    // no easing, no acceleration
    linear(t) { return t },
    // accelerating from zero velocity
    easeInQuad(t) { return t * t },
    // decelerating to zero velocity
    easeOutQuad(t) { return t * (2 - t) },
    // acceleration until halfway, then deceleration
    easeInOutQuad(t) { return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t },
    // accelerating from zero velocity
    easeInCubic(t) { return t * t * t },
    // decelerating to zero velocity
    easeOutCubic(t) { return (--t) * t * t + 1 },
    // acceleration until halfway, then deceleration
    easeInOutCubic(t) { return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1 },
    // accelerating from zero velocity
    easeInQuart(t) { return t * t * t * t },
    // decelerating to zero velocity
    easeOutQuart(t) { return 1 - (--t) * t * t * t },
    // acceleration until halfway, then deceleration
    easeInOutQuart(t) { return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t },
    // accelerating from zero velocity
    easeInQuint(t) { return t * t * t * t * t },
    // decelerating to zero velocity
    easeOutQuint(t) { return 1 + (--t) * t * t * t * t },
    // acceleration until halfway, then deceleration
    easeInOutQuint(t) { return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t },
};

const VideoScroller = function(options) {
    this.options = options || {};

    if (!this.options.el) {
        throw new Error('Missing video element ref.');
    }

    if (!this.options.container) {
        throw new Error('Missing container element.');
    }

    this.container = this.options.container;
    this.transitionTime = this.options.transitionTime || 2000;
    this.easingFunction = this.options.easingFunction || EasingFunctions.easeOutQuint;
    this.invert = this.options.invert !== undefined ? this.options.invert : false;
    this.scrollTimeout = this.options.scrollTimeout || 10;

    this.el = this.options.el;
    this.el.addEventListener('loadeddata', this.init.bind(this));
};

VideoScroller.prototype = {
    init() {
        this.videoDuration = this.el.duration;

        window.addEventListener('scroll', this.onScroll.bind(this), false);
        // eslint-disable-next-line no-underscore-dangle
        this._top = window.pageYOffset;
        this.start(this.inView(this.el));
    },

    start(time) {
        this.startTime = Date.now();

        this.currentTime = this.el.currentTime;
        this.targetDuration = (this.videoDuration * time) - this.el.currentTime;
    },

    inView() {
        const containerHeight = this.container.offsetHeight;

        const topHeroHeight = document.getElementsByClassName('hero home-new')[0].offsetHeight;

        let fromTop = window.pageYOffset - topHeroHeight - 68;

        let bottomeBreakpoint;
        if (topHeroHeight + 68 >= containerHeight) {
            bottomeBreakpoint = topHeroHeight + 68 + (containerHeight - this.el.offsetHeight) / 2;
        } else {
            bottomeBreakpoint = topHeroHeight + 68 + (containerHeight - topHeroHeight - 68) - (containerHeight - this.el.offsetHeight) / 2;
        }

        if (window.pageYOffset > bottomeBreakpoint) {
            this.el.style.top = `${((bottomeBreakpoint - (window.pageYOffset - bottomeBreakpoint) / 4) * 100) / (topHeroHeight + 68) / 2}%`;
        } else {
            this.el.style.top = `${(window.pageYOffset * 100) / (topHeroHeight + 68) / 2}%`;
        }

        if (parseFloat(this.el.style.top) > 40 && this.el.paused && !this.scrollingUp) {
            if (this.reverse) {
                clearInterval(this.reverse);
                this.reverse = null;
            }
            this.el.play();
        }
        if (parseFloat(this.el.style.top) < 20 && this.scrollingUp) {
            if (!this.reverse) {
                const video = this.el;
                this.reverse = setInterval(() => {
                    if (video.currentTime > 0) {
                        video.currentTime -= 0.01;
                    }
                }, 10)
            }
        }


        if (fromTop < 0) {
            fromTop = 0;
        }

        let percentage = Math.abs((fromTop) / (containerHeight));
        if (!this.invert) {
            percentage = 1 - percentage;
        }

        if (percentage > 1) {
            return 1;
        } if (percentage < 0) {
            return 0;
        }

        return percentage;
    },

    inCenter(el) {
        const windowHeight = window.innerHeight;

        const elTop = el.getBoundingClientRect().top;
        const elHeight = el.offsetHeight;

        const bar = elTop - (windowHeight / 2) + (elHeight / 2);

        const percentage = Math.abs(bar / (windowHeight / 2));

        if (percentage > 1) {
            return 1;
        } if (percentage < 0) {
            return 0;
        }

        return percentage;
    },

    onScroll() {
        if (this.isWaiting) {
            return;
        }

        this.isWaiting = true;
        const currentTop = window.pageYOffset;
        // eslint-disable-next-line no-underscore-dangle
        this.scrollingUp = this._top >= currentTop;
        // eslint-disable-next-line no-underscore-dangle
        this._top = currentTop;
        setTimeout(() => {
            this.isWaiting = false;

            const time = this.inView(this.el);

            if (time === undefined) return;

            this.start(time);
        }, this.scrollTimeout);
    },
};
