const Site = window.Site || {};

Site.formatPromocode = (function() {
    const autoInit = () => {
        document.addEventListener('DOMContentLoaded', () => {
            const promocodeElements = document.querySelectorAll('[data-promocode-value]');
            
            promocodeElements.forEach(promocodeElement => {
                const promocodeValue = window.priceFormatFn(promocodeElement.dataset.promocodeValue);
                const promocodeValueFormatted = `<span class="text-lowercase">${promocodeValue}</span>`;

                promocodeElement.innerHTML = promocodeElement.innerHTML.replace('[newsletter-value]', promocodeValueFormatted);
            });
        });
    }

    return {
        autoInit,
    }
}());
