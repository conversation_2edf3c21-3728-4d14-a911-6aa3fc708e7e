/* Select component */

const Site = window.Site || {};

Site.Selects = (function($) {
    const onPageLoad = function() {
        if (!$('.js-select').length) return;
        bind();
    };

    const bind = function() {
        $('.js-select').find('select').off().on('change.selects', onChange)
            .each((i, select) => {
                setValue($(select));
            });
    };

    const setValue = function($select) {
        const select = $select.get(0);
        const dispVal = $select.parent().find('.js-select-val').get(0);
        const val = select.options[select.selectedIndex].innerHTML;

        if (!$select.val()) {
            $select.parent().parent().addClass('is-focused');
        } else {
            $select.parent().parent().removeClass('is-focused');
        }

        dispVal.innerHTML = val;
    };

    const onChange = function(e) {
        setValue($(e.currentTarget));
    };

    return {
        onPageLoad,
        bind,
    };
}(jQuery));
