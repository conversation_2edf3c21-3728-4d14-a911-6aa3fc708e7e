const Site = window.Site || {};

Site.twoPicsCarousels = (function() {
    function initialize() {
        const carousels = [...document.querySelectorAll('.two-pics__carousel')];

        carousels.forEach(carousel => {
            const dataName = carousel.getAttribute('data-carousel');
            const sliderCells = [...document.querySelectorAll(`.two-pics .common-slider[data-carousel="${dataName}"] .common-slider__cell`)];
            const sliderPill = document.querySelector(`.two-pics .common-slider[data-carousel="${dataName}"] .common-slider__cell-pill`);
            const slider = document.querySelector(`.two-pics .common-slider[data-carousel="${dataName}"]`);

            const options = {
                cellAlign: 'left',
                prevNextButtons: false,
                pageDots: false,
                fade: true,
                wrapAround: false,
                on: {
                    ready: () => {
                        window.Site.carouselsUtils.handlePill({
                            activeIndex: 0, sliderCells, sliderPill,
                        });
                        window.dispatchEvent(new Event('resize'));
                    },
                    change: index => {
                        window.Site.carouselsUtils.handlePill({
                            activeIndex: index, readyEvent: false, sliderCells, sliderPill,
                        });
                    },
                },
            };

            const flickity = new window.Flickity(`.two-pics__carousel[data-carousel="${dataName}"]`, options);

            slider.addEventListener('click', e => {
                const index = e.target.getAttribute('data-index');
                flickity.select(index);
            });
        });
    }

    return {
        initialize,
    };
}(jQuery));
