/** Title: footer.js
 *
 * Related templates:
 * frontend_cms/templates/front/_footer.html
 *
 */

const Site = window.Site || {};

Site.footer = (function($) {
    let wasInitialized = false;

    function initialize() {
        if (wasInitialized) return;
        wasInitialized = true;

        const ctaEvent = ('ontouchstart' in document.documentElement) ? 'click' : 'mouseup';

        $('.js-dixa-message').on('click', () => {
            /* eslint-disable no-underscore-dangle */
            if (typeof window._dixa_ !== 'undefined') {
                window._dixa_.invoke('setWidgetOpen', true);
            }
        });

        $('.fast-checkbox').on(ctaEvent, e => {
            $(e.target).toggleClass('selected');
            $(e.target).removeClass('error');
        });

        $('.newsletter__checkbox').on(ctaEvent, e => {
            $(e.target).removeClass('newsletter__checkbox--error');
        });

        $('.newsletter__checkbox-label').click(() => {
            $('.newsletter__checkbox').removeClass('newsletter__checkbox--error');
        });

        $('.mobile-tooltip-cta').on(ctaEvent, () => {
            $('.mobile-tooltip').addClass('open');
        });


        $('.mobile-tooltip .close').on(ctaEvent, () => {
            $('.mobile-tooltip').removeClass('open');
        });

        $('.tooltip-container .back').on(ctaEvent, e => {
            $(e.target).parent().parent().removeClass('open');
        });

        $('.grid-popup-rodo-special .popup-rodo .close').on(ctaEvent, e => {
            $('.tooltip-container .tooltip', $(e.target).parents().eq(5)).addClass('open');
        });

        $(window).scroll(() => {
            $('.mobile-tooltip').removeClass('open');
        });

        [...document.querySelectorAll('.cookies-modal-trigger')].forEach(button => {
            button.addEventListener('click', () => {
                PubSub.publish('openCookiesModal');
            });
        });
    }

    return {
        initialize,
    };
}(jQuery));
