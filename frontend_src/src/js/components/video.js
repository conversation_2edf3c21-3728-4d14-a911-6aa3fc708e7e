/* Home Page view */
const Site = window.Site || {};

Site.Video = (function($) {
    const slideShowBreakPoint = 600;

    let videoSlidesCreated = false;
    let $videoSlides;
    let activeVideoSlides = [];
    let videoInterval;

    let initialized = false;

    const isElementHalfVisible = function(el) {
        if (!el || el.nodeType !== 1) { return false; }
        const html = document.documentElement;
        const r = el.getBoundingClientRect();
        return (!!r
        && r.bottom >= 0
        && r.right >= 0
        && r.top + (html.clientHeight / 3) <= html.clientHeight
        && r.left <= html.clientWidth);
    };

    const initialize = function() {
        initialized = true;
        videoSlidesCreated = false;
        activeVideoSlides = [];

        $('.js-video-module').each(function() {
            const $this = $(this);
            const video = $this.find('.js-video').get(0);
            const defaultSettings = {
                autoplay: 0, // should it autoplay
                loop: 0, // should it loop
                slides: 0, // has a slide fallback for mobile phones
                volume: 0, // 0..1
                playbackRate: 1, // default value
            };
            let settings = $this.data('video') || {};

            settings = $.extend(defaultSettings, settings);
            if (video) {
                video.volume = settings.volume;
            }

            // save the full settings
            $this.data('video', settings);

            // prevent reinitalizing the js on partial page load
            if ($this.data('initialized')) {
                return;
            }
            $this.data('initialized', true);

            if (video) {
                video.addEventListener('ended', function() {
                    if (settings.loop === 1) {
                        if ($this.data('loop') && parseInt($this.data('loop'), 10) > 0) {
                            $this.data('loop', parseInt($this.data('loop'), 10) - 1);
                        }
                        if (!$this.attr('data-loop') || parseInt($this.data('loop'), 10) > 0) {
                            this.play();
                            $this.addClass('is-playing').find('.hero-video').addClass('is-playing');
                        } else {
                            $this.addClass('is-video-ended');
                            $this.removeClass('is-playing').removeClass('video-is-playing').find('.hero-video').removeClass('is-playing');
                        }
                    } else {
                        $this.addClass('is-video-ended');
                        $this.removeClass('is-playing').removeClass('video-is-playing').find('.hero-video').removeClass('is-playing');
                        $this.data('loop', 3);
                    }
                });

                video.addEventListener('play', () => {
                    $this.removeClass('is-video-ended').find('.hero-video').addClass('is-playing');
                });

                video.addEventListener('pause', () => {
                    $this.removeClass('video-is-playing').find('.hero-video').removeClass('is-playing');
                });

                $this.find('.js-video-replay').off().on('click', e => {
                    e.preventDefault();
                    video.play();
                    $this.addClass('video-is-playing').find('.hero-video').addClass('is-playing');
                });

                $this.find('.js-video-play').off().on('click', e => {
                    e.preventDefault();
                    $this.addClass('video-is-playing').find('.hero-video').addClass('is-playing');
                });

                video.addEventListener('click', () => {
                    video.play();
                });
            }

            if (settings.autoplay === 1) {
                playOnVisible(video, (video => {
                    video.playbackRate = settings.playbackRate;
                    video.play();
                    $this.addClass('video-is-playing').find('.hero-video').addClass('is-playing');
                }).bind(this, video, $this));
            }

            window.$window.off('.video').on('resize.video', onResize);
            onResize();
        });
    };

    const videoSlidesPlay = function() {
        let currentFrame = 0;
        let videoFrames = 0;

        $.each(activeVideoSlides, (i, $activeVideoSlide) => {
            currentFrame = $activeVideoSlide.data('currentSlide') || 0;
            videoFrames = $activeVideoSlide.attr('data-video-slides');

            $activeVideoSlide.find('img')
                .removeClass('is-current')
                .eq(currentFrame)
                .addClass('is-current');

            currentFrame++;

            if (currentFrame >= videoFrames) {
                currentFrame = 0;
            }
            $activeVideoSlide.data('currentSlide', currentFrame);
        });
    };

    const playOnVisible = function(videoEl, cb) {
        setTimeout(() => {
            if (isElementHalfVisible(videoEl)) {
                if (cb) {
                    cb()
                }
            } else {
                playOnVisible(videoEl, cb);
            }
        }, 33.333);
    };

    const createVideoSlides = function() {
        $videoSlides = $('.js-video-slides');

        if (!$videoSlides.length) {
            return;
        }

        $videoSlides.each((i, videoSlide) => {
            let videoCode = '';
            const $videoSlide = $(videoSlide);
            const videoFrames = $videoSlide.attr('data-video-slides') || 0;
            const videoName = $videoSlide.attr('data-video-name') || 0;

            if (videoFrames === 0 || videoName === '') {
                return;
            }

            for (let i = 1; i <= videoFrames; i++) {
                videoCode += `<img src="${videoName.replace('{{ no }}', i)}">`;
            }

            $videoSlide.html(videoCode)
                .imagesLoaded(() => {
                    activeVideoSlides.push($videoSlide);
                });
        });

        // initalize the interval

        videoInterval = setInterval(videoSlidesPlay, 300);

        videoSlidesCreated = true;
    };

    const destroyVideoSlides = function() {
        if (videoSlidesCreated) {
            videoSlidesCreated = false;
        }
        if ($videoSlides) {
            $videoSlides.html('');
        }
        clearInterval(videoInterval);
    };

    const onResize = function() {
        if (window.matchMedia(`(max-width: ${slideShowBreakPoint}px)`).matches) {
            if (videoSlidesCreated === false) {
                createVideoSlides();
            }
        } else if (videoSlidesCreated) {
            destroyVideoSlides();
        }
    };

    const onUnLoad = function() {
        if (!initialized) {
            return;
        }

        if (videoSlidesCreated) {
            destroyVideoSlides();
            activeVideoSlides.length = 0;
        }

        window.$window.off('.video');
    };

    return {
        onPageLoad: initialize,
        onUnLoad,
        playOnVisible,
    };
}(jQuery));
