/* Share component */

.share {
    width: 48px;
    border: 1px solid $color-gray-light;
    position: absolute;
    top: 0;
    left: 0;
}

.share-option {
    a {
        display: block;
        padding: 2px 0;
    }
}

.share-option + .share-option {
    border-top: 1px solid $color-gray-light;    
}

.no-touch {
    .share-option {
        transition: all $animation-time $animation-easing;
        &:hover {
            background: $color-header;
        }
    }
}

@media screen and (max-width: $size-tablet) {

    .share {
        width: 44px;
        height: 44px;
        overflow: hidden;
        z-index: 4;
        background: $color-bg;
        transition: height $animation-time $animation-easing;

        &.is-open {
            height: 46px * 4 - 1px;
        }
    }

    .share-option {
        height: 44px;
    }
    
    .js-mobile-share {
        border-top: 1px solid $color-gray-light;
        background: $color-bg;
        position: absolute;
        left: 0;
        bottom: 0;
    }
}