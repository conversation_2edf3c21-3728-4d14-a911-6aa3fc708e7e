.checkout-nav {
  position: relative;
  width: auto;
  white-space: nowrap;

  .inner-container {
    align-items: flex-start;
    justify-content: flex-start;
    width: 355px;
    padding-bottom: 30px;
    padding-left: 20px;

    @include desktop-min {
      width: auto;
      height: auto;
      align-items: center;
      justify-content: center;
    }
  }

  &::-webkit-scrollbar {
    -webkit-appearance: none;
    display: none;
    width: 0;
    height: 0;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .1)
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3)
  }

  .nav-btn {
    width: 24px;
    height: 24px;
    border: 1px solid $color-gray-border;
    border-radius: 50%;
    position: relative;
    cursor: auto;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $color-gray-form;

    &:not(:first-child) {
      margin-left: 80px;

      @include desktop-min {
        margin-left: 114px;
      }
    }

    &:nth-child(1):after,
    &:nth-child(2):after,
    &:nth-child(3):after {
      content: '';
      position: absolute;
      width: 81px;
      height: 1px;
      background-color: $color-gray-border;
      top: 50%;
      left: 24px;
      transform: translateY(-50%);
      pointer-events: none;

      @include desktop-min {
        width: 115px;
      }
    }

    &.active {
      &:after {
        background-color: $color-confirm;
      }
    }

    &.current {
      border: 1px solid $color-confirm;
      color: $color-confirm;

      .step-label {
        color: $color-black;
      }
    }

    &.disabled {
      pointer-events: none;
    }

    .step-number {
      font-size: 12px;
    }

    .tick {
      display: none;
    }

    .step-label {
      position: absolute;
      top: 35px;
      left: 50%;
      transform: translateX(-50%);
      color: $color-gray;
    }
  }

  .active {
    cursor: pointer;
    pointer-events: auto;
    background: $color-confirm;
    border: 1px solid $color-confirm;

    .tick {
      display: block;
    }

    .step-number {
      display: none;
    }

    .step-label {
      color: $color-black;
    }
  }

  @include to-desktop-min {
    overflow-x: scroll;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
}

.checkout-link {
  display: block;
  @media screen and (min-width: 1024px) {
    display: none;
  }
}

.checkout-btn {
  height: 56px;
}