.yoda-country-select {
  position:relative;
  width:100%;
  height:68px;
  cursor: pointer;
  overflow:hidden;
  &:hover .inner {
    border-color:#404040;
  }
  &.active {
    overflow: visible;
    .inner {
      border-color: #404040;
      height: 321px;
      overflow:visible;
    }
    .country-select__container {
      .country-list {
        border-top:1px solid #000;
      }
    }
    .arrow-list {
      transform:rotate(135deg) translateY(-50%);
    }
  }
  .arrow-list {
    position: absolute;
    height:7px;
    width:7px;
    border:2px solid #4a4a4a;
    border-top:none;
    border-right:none;
    transform:rotate(-45deg) translateY(-50%);
    transform-origin:center;
    right:18px;
    top:50%;
  }
  .inner {
    border-radius:6px;
    border:1px solid $color-gray-form;
    display:grid;
    grid-template-columns: 1fr;
    grid-template-rows:28px 1fr;
    height:68px;
    background-color:#fff;
    overflow:hidden;
    .country-select__label {
      width:100%;
      display:flex;
      align-items: center;
    }
    .country-select__container {
      width:100%;
      position:absolute;
      top:28px;
      //height:294px;
      &:after {
        content: '';
        transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
        position: absolute;
        right: 15px;
        top: 13px;
        display: inline-block;
        height: 6px;
        width: 6px;
        border: 1px solid #000;
        border-top: none;
        border-left: none;
        background-color: transparent;
        transform: rotate(45deg);
      }
      .country-select__selected {
        width:100%;
        height:40px;
        display:flex;
        align-items: center;
      }
      .country-list {
        height:253px;
        overflow-y:scroll;
        overflow-x:hidden;
        &::-webkit-scrollbar {
          width:4px;
        }
        &::-webkit-scrollbar-track {
          background-color:transparent;
        }
        &::-webkit-scrollbar-thumb {
          background-color:$color-gray-form;
          border-radius:8px;
        }
        .list__option {
          height:36px;
          background-color:#fff;
          display:flex;
          align-items: center;
          border-radius:6px;
          &:hover {
            background-color:$color-gray-light;
          }
        }
      }

      .country-native-select {
        -webkit-appearance: none;
        outline:none;
        padding-left:12px;
        background-color:#fff;
        width:calc(100% - 2px);
        height:39px;
      }
    }
  }
}

.yoda-country-select__mobile, .yoda-country-select__desktop {
  display:none;
}

@media screen and (min-width: 1024px) {
  .yoda-country-select__desktop {
    display:flex;
  }
}

@media screen and (max-width: 1024px) {
  .yoda-country-select__mobile {
    display:flex;
  }
}