body {
    text-rendering: optimizeLegibility; /* emphasizes in legibility over rendering speed */
    -webkit-font-smoothing: antialiased; /* apply font anti-aliasing */
    -moz-osx-font-smoothing: grayscale; /* optimize font rendering */

    -moz-font-feature-settings: "kern" 1; /* turn on kerning, highly recomened */
    -ms-font-feature-settings: "kern" 1; /* turn on kerning, highly recomened */
    -o-font-feature-settings: "kern" 1; /* turn on kerning, highly recomened */
    -webkit-font-feature-settings: "kern" 1; /* turn on kerning, highly recomened */
    font-feature-settings: "kern" 1; /* turn on kerning, highly recomened */
    font-kerning: normal; /* turn on kerning, highly recomened */

    font-feature-settings: "liga" off; /* ligatures: on or off */
    font-feature-settings: "dlig" off; /* discretionary-ligatures: on or off */
    font-feature-settings: "tnum" off; /* tabular figures: on or off */
    font-feature-settings: "onum" off; /* old-style-figures: on or off */
    font-feature-settings: "ss01" off; /* alternate glyphs (stylistic Set): on or off */
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-nowrap {
    white-space: nowrap;
}

.text-uppercase {
    &#{&} {
        text-transform: uppercase;
    }
}

.text-capitalize {
    &#{&} {
        text-transform: capitalize;
    }
}

.text-underline {
    text-decoration: underline;
}

.text-no-underline {
    text-decoration: none;
}

.text-capitalize-first-letter {
    text-transform: lowercase;
    &::first-letter {
        text-transform: uppercase;
    }
}

.one-line {
    height: 18px;
    overflow: hidden;
    display: inline-block;
    width: 222px;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 0;

    &::after {
        content: '...';
    }
}

.text-lowercase {
    text-transform: lowercase;
}

.text-bold {
    font-weight: $font-weight-bold;
}

.normal-9 {
    font-weight: $font-weight-normal;
    font-size: 9px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-9 {
    font-weight: $font-weight-bold;
    font-size: 9px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.normal-10 {
    font-weight: $font-weight-normal;
    font-size: 10px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-10 {
    font-weight: $font-weight-bold;
    font-size: 10px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-10-2 {
    font-weight: $font-weight-bold;
    font-size: 10px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0_5;
    text-transform: uppercase;
}

.normal-12 {
    font-weight: $font-weight-normal;
    font-size: 12px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.normal-12-2 {
    font-weight: $font-weight-normal;
    font-size: 12px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
    text-transform: uppercase;
}

.bold-12 {
    font-weight: $font-weight-bold;
    font-size: 12px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-12-2 {
    font-weight: $font-weight-bold;
    font-size: 12px;
    line-height: $leading-1_3;
    letter-spacing: $tracking-0_5;
    text-transform: uppercase;
}

.bold-13 {
    font-weight: $font-weight-bold;
    font-size: 13px;
    letter-spacing: $tracking-0;
    line-height: $leading-1_3;
}

.normal-14 {
    font-weight: $font-weight-normal;
    font-size: 14px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-14 {
    font-weight: $font-weight-bold;
    font-size: 14px;
    line-height: $leading-1_4;
    letter-spacing: $tracking-0;
}

.bold-14-2 {
    font-weight: $font-weight-bold;
    font-size: 14px;
    line-height: $leading-1_3;
    letter-spacing: $tracking-0_5;
    text-transform: uppercase;
}

.light-16 {
    font-weight: $font-weight-light;
    font-size: 16px;
    line-height: $leading-1_4;
    letter-spacing: $tracking--0_2;
}

.normal-16 {
    font-weight: $font-weight-normal;
    font-size: 16px;
    line-height: $leading-1_4;
    letter-spacing: $tracking--0_2;
}

.bold-16 {
    font-weight: $font-weight-bold;
    font-size: 16px;
    line-height: $leading-1_25;
    letter-spacing: $tracking--0_2;
}

.normal-16-2 {
    font-weight: $font-weight-normal;
    font-size: 16px;
    line-height: $leading-1_2;
    letter-spacing: $tracking-1_5;
    text-transform: uppercase;
}

.normal-20 {
    font-weight: $font-weight-normal;
    font-size: 20px;
    line-height: $leading-1_3;
    letter-spacing: $tracking--0_3;
}

.bold-20 {
    font-weight: $font-weight-bold;
    font-size: 20px;
    line-height: $leading-1_3;
    letter-spacing: $tracking--0_3;
}

.light-24 {
    font-weight: $font-weight-light;
    font-size: 24px;
    line-height: $leading-1_3;
    letter-spacing: $tracking--0_3;
}

.normal-24 {
    font-weight: $font-weight-normal;
    font-size: 24px;
    line-height: $leading-1_3;
    letter-spacing: $tracking--0_3;
}

.bold-24 {
    font-weight: $font-weight-bold;
    font-size: 24px;
    line-height: $leading-1_3;
    letter-spacing: $tracking--0_3;
}

.normal-28 {
    font-weight: $font-weight-normal;
    font-size: 28px;
    line-height: $leading-1_2;
    letter-spacing: $tracking--0_5;
}

.bold-28 {
    font-weight: $font-weight-bold;
    font-size: 28px;
    line-height: $leading-1_2;
    letter-spacing: $tracking--0_5;
}

.normal-32 {
    font-weight: $font-weight-normal;
    font-size: 32px;
    line-height: $leading-1_1;
    letter-spacing: $tracking--1_5;
}

.bold-32 {
    font-weight: $font-weight-bold;
    font-size: 32px;
    line-height: $leading-1_2;
    letter-spacing: $tracking--0_5;
}

.normal-42 {
    font-weight: $font-weight-normal;
    font-size: 42px;
    line-height: $leading-1_1;
    letter-spacing: $tracking--1_5;
}

.bold-46 {
    font-weight: $font-weight-bold;
    font-size: 46px;
    line-height: $leading-1_1;
    letter-spacing: $tracking--1_5;
}

.bold-54 {
    font-weight: $font-weight-bold;
    font-size: 54px;
    line-height: $leading-1_05;
    letter-spacing: $tracking--1_5;
}

.bold-72 {
    font-weight: $font-weight-bold;
    font-size: 72px;
    line-height: $leading-1_1;
    letter-spacing: $tracking--1;
}

.bold-88 {
    font-weight: $font-weight-bold;
    font-size: 88px;
    line-height: $leading-0_95;
    letter-spacing: $tracking--3_5;
}

//all media query-prefixes
@include media-query-prefix {
    &\:text-center {
        text-align: center;
    }

    &\:text-left {
        text-align: left;
    }

    &\:text-right {
        text-align: right;
    }

    &\:normal-9 {
        font-weight: $font-weight-normal;
        font-size: 9px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:bold-9 {
        font-weight: $font-weight-bold;
        font-size: 9px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:normal-10 {
        font-weight: $font-weight-normal;
        font-size: 10px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:bold-10 {
        font-weight: $font-weight-bold;
        font-size: 10px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:bold-10-2 {
        font-weight: $font-weight-bold;
        font-size: 10px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0_5;
        text-transform: uppercase;
    }

    &\:normal-12 {
        font-weight: $font-weight-normal;
        font-size: 12px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:normal-12-2 {
        font-weight: $font-weight-normal;
        font-size: 12px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
        text-transform: uppercase;
    }

    &\:bold-12 {
        font-weight: $font-weight-bold;
        font-size: 12px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:bold-12-2 {
        font-weight: $font-weight-bold;
        font-size: 12px;
        line-height: $leading-1_3;
        letter-spacing: $tracking-0_5;
        text-transform: uppercase;
    }

    &\:bold-13 {
        font-weight: $font-weight-bold;
        font-size: 13px;
        letter-spacing: $tracking-0;
        line-height: $leading-1_3;
    }

    &\:normal-14 {
        font-weight: $font-weight-normal;
        font-size: 14px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }
    &\:bold-14 {
        font-weight: $font-weight-bold;
        font-size: 14px;
        line-height: $leading-1_4;
        letter-spacing: $tracking-0;
    }

    &\:bold-14-2 {
        font-weight: $font-weight-bold;
        font-size: 14px;
        line-height: $leading-1_3;
        letter-spacing: $tracking-0_5;
        text-transform: uppercase;
    }

    &\:light-16 {
        font-weight: $font-weight-light;
        font-size: 16px;
        line-height: $leading-1_4;
        letter-spacing: $tracking--0_2;
    }

    &\:normal-16 {
        font-weight: $font-weight-normal;
        font-size: 16px;
        line-height: $leading-1_4;
        letter-spacing: $tracking--0_2;
    }

    &\:bold-16 {
        font-weight: $font-weight-bold;
        font-size: 16px;
        line-height: $leading-1_25;
        letter-spacing: $tracking--0_2;
    }

    &\:normal-16-2 {
        font-weight: $font-weight-normal;
        font-size: 16px;
        line-height: $leading-1_2;
        letter-spacing: $tracking-1_5;
        text-transform: uppercase;
    }

    &\:normal-20 {
        font-weight: $font-weight-normal;
        font-size: 20px;
        line-height: $leading-1_3;
        letter-spacing: $tracking--0_3;
    }

    &\:bold-20 {
        font-weight: $font-weight-bold;
        font-size: 20px;
        line-height: $leading-1_3;
        letter-spacing: $tracking--0_3;
    }

    &\:light-24 {
        font-weight: $font-weight-light;
        font-size: 24px;
        line-height: $leading-1_3;
        letter-spacing: $tracking--0_3;
    }

    &\:normal-24 {
        font-weight: $font-weight-normal;
        font-size: 24px;
        line-height: $leading-1_3;
        letter-spacing: $tracking--0_3;
    }

    &\:bold-24 {
        font-weight: $font-weight-bold;
        font-size: 24px;
        line-height: $leading-1_3;
        letter-spacing: $tracking--0_3;
    }

    &\:normal-28 {
        font-weight: $font-weight-normal;
        font-size: 28px;
        line-height: $leading-1_2;
        letter-spacing: $tracking--0_5;
    }

    &\:bold-28 {
        font-weight: $font-weight-bold;
        font-size: 28px;
        line-height: $leading-1_2;
        letter-spacing: $tracking--0_5;
    }

    &\:normal-32 {
        font-weight: $font-weight-normal;
        font-size: 32px;
        line-height: $leading-1_1;
        letter-spacing: $tracking--1_5;
    }

    &\:bold-32 {
        font-weight: $font-weight-bold;
        font-size: 32px;
        line-height: $leading-1_2;
        letter-spacing: $tracking--0_5;
    }

    &\:normal-42 {
        font-weight: $font-weight-normal;
        font-size: 42px;
        line-height: $leading-1_1;
        letter-spacing: $tracking--1_5;
    }

    &\:bold-46 {
        font-weight: $font-weight-bold;
        font-size: 46px;
        line-height: $leading-1_1;
        letter-spacing: $tracking--1_5;
    }

    &\:bold-54 {
        font-weight: $font-weight-bold;
        font-size: 54px;
        line-height: $leading-1_05;
        letter-spacing: $tracking--1_5;
    }

    &\:bold-72 {
        font-weight: $font-weight-bold;
        font-size: 72px;
        line-height: $leading-1_1;
        letter-spacing: $tracking--1;
    }

    &\:bold-88 {
        font-weight: $font-weight-bold;
        font-size: 88px;
        line-height: $leading-0_95;
        letter-spacing: $tracking--3_5;
    }
}
