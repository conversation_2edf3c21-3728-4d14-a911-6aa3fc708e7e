.our-mission {
    &__hero {
        position: relative;
        height: calc(var(--vh, 1vh) * 100 - #{$header-height-mobile});
        min-height: 525px;
        overflow: hidden;
        background-color: #F4E7E7;

        @include desktop-min {
            height: calc(var(--vh, 1vh) * 100 - #{$header-height});
        }

        .mobile-visible {
            display: none!important;
        }
    }
    &__row {
        height: 100%;
    }
    &__icon {
        fill: currentColor;
    }
    &__media {
        pointer-events: none;
        &--interactive {
            pointer-events: auto;
        }
    }
    &__overlay {
        @include size(100%, 100%);
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
        background-blend-mode: multiply, normal;
        box-sizing: border-box;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        opacity: 1;
        visibility: visible;
        transition: opacity $basic-transition;
        &--hidden {
            opacity: 0;
            visibility: none;
            pointer-events: none;
        }
    }
    &__hidden {
        @include tablet-min {
            display: none;
        }

        &--mobile {
            display: none;

            @include tablet-min {
                display: flex;
            }
        }
    }
    &__image {
        width: 100%;
        object-fit: cover;
        object-position: center;
        max-height: 290px;

        @include mobile-middle {
            max-height: none;
        }
    }
    &__caption {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        margin: 0 auto;
        transform: translateY(-50%);
    }
    &__furniture {
        background-color: #F4ECDE;
    }
    &__respect {
        background-color: #919891;
    }
    &__future {
        background-color: #f9f9f9;
    }
    &__care {
        background-color: #D6BFA6
    }
}
