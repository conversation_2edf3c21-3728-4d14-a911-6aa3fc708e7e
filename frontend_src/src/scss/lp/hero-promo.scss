.hero-promo {
    &__scroll {
        position: absolute;
        bottom: 16px;
        left: 0;
        right: 0;
        width: 100px;
        transition: bottom $basic-transition;

        &--rodo {

            @include desktop-min {
                bottom: 48px;
            }
        }

        &:after {
            padding: 4px;
        }
    }

    &__content {
        text-align: center;
    }

    &__image {
        max-height: calc(100vh - 68px);
        object-fit: cover;
    }

    &__button {
        overflow: hidden;
    }

    &__code {
        transition: opacity $short-transition;
    }

    &__copied {
        position: absolute;
        bottom: -12px;
        opacity: 0;
        visibility: hidden;
        transition: opacity $short-transition, bottom $short-transition;

        @include desktop-air {
            bottom: -24px;
        }
    }

    &__tick {
        &:after {
            position: absolute;
            content: "L";
            left: 2px;
            right: 0;
            bottom: 0;
            top: 2px;
            text-align: center;
            color: $ds-offblack-600;
            font-family: arial;
            transform: scaleX(-1) rotate(-40deg);
        }
    }

    &__clicked {
        background-color: $ds-white;

        &:hover,
        &:active {
            background-color: $ds-white;
        }

        .hero-promo__code {
            opacity: 0;
            visibility: hidden;
        }

        .hero-promo__copied {
            opacity: 1;
            visibility: visible;
            bottom: 10px;
        }
    }

    &__ribbon {
        transition: margin-top $basic-transition;
        line-height: 20px;
        margin-top: -100%;
        display: block;
        top: -100%;

        &--active {
            margin-top: 0;
        }
    }

    &__fullscreen {
        height: 100%;
        max-height: calc(100vh - 68px);
    }

    &__padding {
        &:before {
            content: '';
            height: 0;
            padding-top: 100%;
        }

        @include tablet-min {
            &:before {
                padding-top: 48%;
            }
        }
    }
}
