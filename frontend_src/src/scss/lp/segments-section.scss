$tablet-btn-left: 24%;
$tablet-btn-right: 78%;

$small-desktop-btn-left: 25%;
$small-desktop-btn-right: 76%;

$large-desktop-btn-left: 27%;
$large-desktop-btn-right: 74%;

$tablet-btn-top: 58%;
$small-desktop-btn-top: 54%;
$large-desktop-btn-top: 59%;

.segments {
    background: linear-gradient(0deg, rgba(236, 215, 215, 0.3), rgba(236, 215, 215, 0.3)), #FFFFFF;
    text-align: center;

    &__desc {
        max-width: 440px;
    }

    &__wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
        border-radius: $border-radius-xl;

        @include tablet-max {
            display: none;
        }
    }

    &__inner-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: $border-radius-xl;
        overflow: hidden;
    }

    &__main-photo {
        display: block;
        max-width: 100%;
        width: 100%;
    }

    &__mask-wrapper {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        display: none;
        width: 100%;
        height: 100%;

        &--tablet {
            @include tablet-min {
                display: inline-block;
            }
            @include desktop-min {
                display: none;
            }
        }

        &--small-desktop {
            @include desktop-min {
                display: inline-block;
            }
            @include desktop-air {
                display: none;
            }
        }

        &--large-desktop {
            @include desktop-air {
                display: inline-block;
            }
        }
    }

    &__mask {
        opacity: 0;
        transition: opacity $animation-time;

        &.active {
            opacity: 1
        }
    }
}

.tooltips {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;

    &__btn {
        position: absolute;
        top: $tablet-btn-top;
        display: block;
        width: 52px;
        height: 52px;
        transform: translateX(-50%);
        border-radius: 50%;
        z-index: 5;
        opacity: 0.5;
        transition: opacity $animation-time $animation-easing;

        @include desktop-min {
            top: $small-desktop-btn-top;
        }

        @include desktop-air {
            top: $large-desktop-btn-top;
        }

        &.active {
            opacity: 1;

            .tooltips__circle {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        &:before, &:after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
        }

        &:before {
            z-index: 6;
            width: 20px;
            height: 20px;
            background-color: $ds-orange;
        }

        &:after {
            z-index: 7;
            width: 12px;
            height: 12px;
            background-color: $ds-white;
        }

        &--left {
            left: $tablet-btn-left;

            @include desktop-min {
                left: $small-desktop-btn-left;
            }

            @include desktop-air {
                left: $large-desktop-btn-left;
            }
        }

        &--center {
            left: 50%;
        }

        &--right {
            left: $tablet-btn-right;

            @include desktop-min {
                left: $small-desktop-btn-right;
            }

            @include desktop-air {
                left: $large-desktop-btn-right;
            }
        }
    }

    &__bg {
        position: absolute;
        top: 50%;
        left: 50%;
        display: block;
        width: 32px;
        height: 32px;
        background-color: $ds-white;
        border-radius: 50%;
        transform: translate(-50%, -50%);
    }

    &__circle {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 42px;
        height: 42px;
        background-color: transparent;
        border: 1px solid $ds-orange;
        box-sizing: border-box;
        border-radius: 50%;
        transition: transform $animation-time $animation-easing, opacity $animation-time $animation-easing;
        animation: pulse 1.5s infinite linear forwards;
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(0.7);
            opacity: 0;
        }
        50% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) scale(1.3);
            opacity: 0;
        }
    }

    @keyframes pulse-hover {
        0% {
        }
        100% {
            transform: translate(-50%, -50%) scale(0.7);
            opacity: 0;
        }
    }

    &__item {
        position: absolute;
        width: 242px;
        min-height: 97px;
        box-sizing: border-box;
        margin-top: 72px; // that's the distance between tooltip button and tooltip
        transform: translate(-50%, 10%) scale(0.97);
        top: $tablet-btn-top;;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(4px);
        border-radius: 12px;
        opacity: 0;
        visibility: hidden;
        transition: all $animation-time $animation-easing;

        @include desktop-min {
            top: $small-desktop-btn-top;
        }

        @include desktop-air {
            top: $large-desktop-btn-top;
        }

        &.active {
            transform: translate(-50%, 0%) scale(1);
            visibility: visible;
            opacity: 1;
        }

        &--left {
            left: $tablet-btn-left;

            @include desktop-min {
                left: $small-desktop-btn-left;
            }

            @include desktop-air {
                left: $large-desktop-btn-left;
            }
        }

        &--center {
            left: 50%;
        }

        &--right {
            left: $tablet-btn-right;

            @include desktop-min {
                left: $small-desktop-btn-right;
            }

            @include desktop-air {
                left: $large-desktop-btn-right;
            }
        }
    }
}

// Mobile carousel

.segments-carousel {
    width: 100%;
    border-radius: $border-radius-xl;
    overflow: hidden;
    backface-visibility: hidden;
    transform: translate3d(0, 0, 0);

    @include tablet-min {
        display: none;
    }

    .flickity-button {
        z-index: 2;
    }

    &.hide-mask {
        &:before, &:after {
            opacity: 0;
        }
    }

    &:before, &:after {
        content: "";
        width: 22%;
        height: 51.5%;
        position: absolute;
        background: $ds-grey-900;
        opacity: 0.3;
        z-index: 1;
        transition: opacity $animation-time $animation-easing, width $animation-time $animation-easing;
    }

    &:before {
        left: 0;
        top: 27.3%;
    }

    &:after {
        right: 0;
        top: 27.3%;
    }

    &.segment-left {
        &:before {
            opacity: 0;
        }

        &:after {
            width: 23.5%;
        }
    }

    &.segment-center {
        &:before {
            width: 21.4%;
        }

        &:after {
            width: 23.2%;
        }
    }

    &.segment-right {
        &:before {
            width: 21%;
        }

        &:after {
            opacity: 0;
        }
    }

    &.t01v {
        &:before, &:after {
            top: 26%;
            height: 58%;
        }
    }

    &.t01p {
        &:before, &:after {
            top: 37%;
            height: 48%;
        }
    }

    &__item {
        margin-right: 0;

        &:nth-child(1) {
            width: 76.5%; // This is the width of each segment relative to the screen width.
        }

        &:nth-child(2) {
            width: 53.47%;
        }

        &:nth-child(3) {
            width: 77.08%;
        }
    }

    &__photo {
        display: block;
        width: 100%;
    }
}

.segments-carousel-text {
    @include tablet-min {
        display: none;
    }

    &__item {
        width: 100%;
    }
}
