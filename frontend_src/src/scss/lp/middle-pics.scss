.middle-pics {
  &__btn-inside {
    display: none;

    @include tablet-min {
      display: inline-flex;
    }
    @include desktop-min {
      display: none;
    }
  }

  &__btn-outside {
    display: flex;

    @include tablet-min {
      display: none;
    }
    @include desktop-min {
      display: flex;
    }
  }

  &__heading {
    max-width: 483px;
  }

  &__photo {
    width: 100%;
    border-radius: $border-radius-xl;
  }
}