.img-fullwidth-textcenter {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;

  &__background {
    display: block;
    height: auto;
    width: 100%;
  }

  &__caption {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    &--top {
      top: 64px;
      transform: translate(-50%, 0);

      @include desktop-air {
        top: 94px;
      }
    }
  }

  &__icon {
    height: 34px;
    width: 34px;
    margin: 0 2px;
    transform: translateY(7px);

    @include desktop-min {
      transform: translateY(3px);
    }
  }

  &__title {
    max-width: 755px;
  }

  &__paragraph {
    @include desktop-air {
      max-width: 520px;
    }
  }

  .heading-span {
    display: block;
  }

  .title-break {
    @include tablet-min {
      display: block;
    }

    @include desktop-air {
      display: inline;
    }
  }
}
