.pictures-row {
    @keyframes flickity-bar {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    &__item {
        position: relative;
        width: 100vw;

        &--fullwidth {
            max-width: 100%;
        }
    }

    &__picture {
        height: auto;
        width: 100%;
    }

    &__image {
        border-radius: $border-radius-xl;
        display: block;
        height: auto;
        min-height: 220px;
        width: 100%;
    }

    &__hover-container {
        border-radius: $border-radius-xl;
        display: none;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        right: 24px;
        top: 49px;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        visibility: hidden;
        width: 280px;
        z-index: 1;

        @include tablet-min {
            display: block;

            &:hover {
                opacity: 1;
                visibility: visible;
            }
        }
    }

    &__hover-title {
        border-radius: $border-radius;
        padding-bottom: 3px;
        cursor: pointer;
        position: absolute;
        top: 24px;
        right: 24px;

        &:hover + .pictures-row__hover-container {
            opacity: 1;
            visibility: visible;
        }
    }

    &__hover-image {
        cursor: pointer;
        height: auto;
        max-width: 100%;
    }

    &__hover-text {
        border-bottom-left-radius: $border-radius-xl;
        border-bottom-right-radius: $border-radius-xl;
        cursor: pointer;
        max-width: 100%;
        position: relative;
        top: -5px;
    }

    &__text-below-button {
        max-width: 307px;
    }

    &__bottom-text {
        max-width: 300px;
    }

    .flickity-prev-next-button {
        &.next, &.previous {
            top: 30%;
            transform: translateY(0);

            @include desktop-min {
                top: 40%;
            }
        }
    }

    .flickity-page-dots {
        display: flex;
        width: calc(100% - 64px);

        @include tablet-min {
            width: calc(100% - 192px);
        }

        .dot {
            background-color: $ds-white;
            border-radius: 0;
            height: 2px;
            margin: 0;
            opacity: 1;
            width: 100%;

            &:first-of-type {
                border-bottom-left-radius: 2px;
                border-top-left-radius: 2px;
            }

            &:last-of-type {
                border-bottom-right-radius: 2px;
                border-top-right-radius: 2px;
            }

            &.is-selected {
                animation: flickity-bar .6s;
                background-color: $ds-offblack-600;
            }
        }
    }

    &__carousel {
        &--mobile {
            .flickity-page-dots {
                position: static;
                margin: 16px auto 4px;
            }

            @include tablet-min {
                display: flex;
                padding-left: 32px; //the same as container-cstm-fluid
                padding-right: 32px; //the same as container-cstm-fluid
            }

            @include desktop-min {
                padding-left: 48px; //the same as container-cstm-fluid
                padding-right: 48px; //the same as container-cstm-fluid
            }

            @include desktop-air {
                padding-left: 88px; //the same as container-cstm-fluid
                padding-right: 88px; //the same as container-cstm-fluid
            }

            @include desktop-xl2 {
                padding-left: 128px; //the same as container-cstm-fluid
                padding-right: 128px; //the same as container-cstm-fluid
            }
        }

        &--desktop {
            .flickity-page-dots {
                bottom: auto;
                margin-top: 16px;
                position: relative;
                transform: translateX(-50%);
            }
        }

        &--fade-in {
            &.flickity-enabled.is-draggable .flickity-viewport {
                cursor: default;
            }
        }
    }
}
