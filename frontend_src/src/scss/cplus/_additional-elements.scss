.card {
  padding: 24px;
  border-radius: 12px;
  height: fit-content;
  box-sizing: border-box;
  width: 100%;
  background-color: white;
}

.tylko-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .text {
    margin-bottom: 0;
  }
}

.miniature {
  display: flex;
}

.tylko-tabs-container {
  position: relative;
  overflow: hidden;
  margin-top: -10px;

  .shadow {
    position: absolute;
    width: 10px;
    height: 36px;
    top: 2px;
    bottom: 0;
    z-index: 1;
    transition: opacity 0.3s ease;

    &.left {
      opacity: 0;
      left: 0;
      background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }

    &.right {
      opacity: 1;
      right: 0;
      background: linear-gradient(to left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }
  }
}

.tylko-tabs-wrapper {
  display: block;
  text-align: left;
  padding-bottom: 10px;
  transform: translateY(10px);
  flex-wrap: nowrap;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  box-sizing: border-box;
  margin: auto;

  > * {
    display: inline-block;
  }

  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  -webkit-overflow-scrolling: touch;
}

.tooltip-slide-leave-active {
    transition: all .3s ease;
}

.tooltip-slide-enter-active {
    transition: all .3s ease;
}

.tooltip-slide-enter, .tooltip-slide-leave-to {
    opacity: 0;
    top: 40px;
}