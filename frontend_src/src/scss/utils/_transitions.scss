$transition-time: .5s;

$basic-animation: $transition-time ease-in-out;

.fade-enter-active, .fade-leave-active {
  transition: opacity $transition-time;
  opacity: 1 !important;
}
.fade-enter, .fade-leave-to {
  opacity: 0 !important;
}

// CHECKOUT TRANSITIONS
@for $i from 1 through 7 {
  .expand-#{$i * 100}-enter-active, .expand-#{$i * 100}-leave-active {
    transition: all $transition-time;
    max-height: #{$i * 100}px;
  }
  .expand-#{$i * 100}-enter, .expand-#{$i * 100}-leave-to {
    max-height: 0px;
  }
  .expand-#{$i * 100}-fade-enter-active, .expand-#{$i * 100}-fade-leave-active {
    transition: all $transition-time;
    max-height: #{$i * 100}px;
    opacity: 1;
  }
  .expand-#{$i * 100}-fade-enter, .expand-#{$i * 100}-fade-leave-to {
    max-height: 0px;
    opacity: 0;
  }
}
