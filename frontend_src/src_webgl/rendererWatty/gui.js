import dat from 'dat.gui';
import { shadowItems, hinges } from '@src_webgl/build3dWatty/buildFunctions/config';
import * as THREE from 'three';

export const datGui = (elements, modelsConfigByMaterial) => {
    const materialInterior = {
        color: modelsConfigByMaterial.box_drawer_back.color,
    };
    const materialExterior = {
        color: modelsConfigByMaterial.box_left_right_frame.color,
    };
    const globalParams = {
        reflectivity: 0,
        lightMapIntensity: 1.2,
    };

    const hingesParams = {
        reflectivity: 0,
        lightMapIntensity: 1.2,
        color: 0xd7d7d7,
    };
    const gui = new dat.GUI();

    const folderInterior = gui.addFolder('MATERIAL - INTERIOR');

    folderInterior.addColor(materialInterior, 'color')
        .listen()
        .onChange(e => {
            Object.keys(elements).forEach(key => {
                if (!shadowItems.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            if (modelsConfigByMaterial[key].colorInterior) {
                                child.material.color = new THREE.Color(e);
                            }
                        }
                    });
                }
            });
        });

    folderInterior.open();

    const folderExterior = gui.addFolder('MATERIAL - EXTERIOR');
    const addColor = folderExterior.addColor(materialExterior, 'color');
    addColor.onChange(e => {
        Object.keys(elements).forEach(key => {
            if (!shadowItems.some(i => i === key)) {
                elements[key].traverse(child => {
                    if (child instanceof THREE.Mesh) {
                        if (!modelsConfigByMaterial[key].colorInterior) {
                            child.material.color = new THREE.Color(e);
                        }
                    }
                });
            }
        });
    });

    folderExterior.open();


    const folderGlobal = gui.addFolder('GLOBAL');

    folderGlobal.add(globalParams, 'reflectivity')
        .listen()
        .min(0).max(1)
        .step(0.01)
        .onChange(e => {
            Object.keys(elements).forEach(key => {
                if (!shadowItems.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            child.material.reflectivity = e;
                        }
                    });
                }
            });
        });

    folderGlobal.add(globalParams, 'lightMapIntensity')
        .min(0).max(2)
        .step(0.01)
        .onChange(e => {
            Object.keys(elements).forEach(key => {
                if (!shadowItems.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            child.material.lightMapIntensity = e;
                        }
                    });
                }
            });
        });

    folderGlobal.open();


    const folderHinges = gui.addFolder('HINGES');

    folderHinges.add(hingesParams, 'reflectivity')
        .listen()
        .min(0).max(1)
        .step(0.01)
        .onChange(e => {
            Object.keys(elements).forEach(key => {
                if (hinges.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            child.material.reflectivity = e;
                        }
                    });
                }
            });
        });

    const addHingesColor = folderHinges.addColor(hingesParams, 'color');
    addHingesColor.onChange(e => {
        console.log(123, e)
        Object.keys(elements).forEach(key => {
            if (hinges.some(i => i === key)) {
                elements[key].traverse(child => {
                    console.log('fire')
                    if (child instanceof THREE.Mesh) {
                        if (!modelsConfigByMaterial[key].color) {
                            child.material.color = new THREE.Color(e);
                        }
                    }
                });
            }
        });
    });

    folderHinges.open();


    const newDiv = document.createElement('div');
    newDiv.innerHTML = "<ul class='custom-gui' style='margin-top: 160px'>"
                + "<li><label>Interior Color <input type='text' placeholder='ffffff' id='customGuiInteriorColor'></label></li>"
                + "<li><label>Exterior Color <input type='text'  placeholder='000000' id='customGuiExteriorColor'></label></li>"
                + '</ul>';
    document.body.appendChild(newDiv);

    document.getElementById('customGuiInteriorColor').addEventListener('keyup', e => {
        if (e.key === 'Enter' || e.keyCode === 13) {
            const hex = parseInt(e.target.value, 16);
            Object.keys(elements).forEach(key => {
                if (!shadowItems.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            if (modelsConfigByMaterial[key].colorInterior) {
                                child.material.color = new THREE.Color(hex);
                            }
                        }
                    });
                }
            });
        }
    });
    document.getElementById('customGuiExteriorColor').addEventListener('keyup', e => {
        if (e.key === 'Enter' || e.keyCode === 13) {
            const hex = parseInt(e.target.value, 16);
            Object.keys(elements).forEach(key => {
                if (!shadowItems.some(i => i === key)) {
                    elements[key].traverse(child => {
                        if (child instanceof THREE.Mesh) {
                            if (!modelsConfigByMaterial[key].colorInterior) {
                                child.material.color = new THREE.Color(hex);
                            }
                        }
                    });
                }
            });
        }
    });
};
