import { Group } from 'three';

const createDrawers = type => function({
    geom,
    scene,
    elements,
    sceneItems,
}) {
    const { m_config_id, pivot } = geom;
    const modelScale = 10;
    const elementName = 'drawer_group';
    const elementType = 'drawers';
    const drawerGroup = new Group();

    drawerGroup.name = `${elementName}:${m_config_id}`;
    drawerGroup.position.set(pivot.x, pivot.y, pivot.z);

    const createDrawerElement = (mesh, item) => {
        const element = mesh.clone();
        element.scale.x = item.scale.x / modelScale;
        element.scale.y = item.scale.y / modelScale;
        // TODO: animation z-position issue
        element.scale.z = (item.scale.z / modelScale);
        element.position.set(item.local_pivot.x, item.local_pivot.y, item.local_pivot.z + 6);
        drawerGroup.add(element);
    };

    const frontDrawers = type === 'interior' ? elements.box_drawer_front_interior : elements.box_drawer_front_exterior;

    createDrawerElement(frontDrawers, geom.front);
    createDrawerElement(elements.box_drawer_back, geom.back);
    createDrawerElement(elements.box_drawer_bottom, geom.bottom);
    createDrawerElement(elements.box_drawer_left_side, geom.left);
    createDrawerElement(elements.box_drawer_right_side, geom.right);

    sceneItems[elementType].push(drawerGroup);
    scene.add(drawerGroup);
};
const createDrawersInterior = createDrawers('interior');
const createDrawersExterior = createDrawers('exterior');

export { createDrawersInterior, createDrawersExterior };
