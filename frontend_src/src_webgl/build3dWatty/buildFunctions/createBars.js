export default ({
    geom,
    scene,
    elements,
    sceneItems,
}) => {
    const elementName = 'bar';
    const elementType = 'bars';
    const element = elements[elementName].clone();
    const scale = 10;

    // element.scale.x = geom.width / scale;
    // element.position.x = (geom.x1 + geom.x2) / 2;
    // element.position.y = (geom.y1 + geom.y2) / 2;
    // element.position.z = geom.z1;

    element.scale.x = geom.scale.x / scale;
    element.position.x = geom.centroid.x;
    element.position.y = geom.centroid.y;
    element.position.z = geom.z1;

    sceneItems[elementType].push(element);
    scene.add(element);
};
