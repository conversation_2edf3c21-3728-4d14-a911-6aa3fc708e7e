import createBacks from './createBacks';
import createBars from './createBars';
import createButtons from './createButtons';
import createDoorsExteriors from './createDoorsExteriors';
import createDoorsRaiser from './createDoorsRaiser';
import { createDrawersInterior, createDrawersExterior } from './createDrawers';
import createFrame from './createFrame';
import createHinges from './createHinges';
import createInterior from './createInterior';
import createMaskingBars from './createMaskingBars';
import createSlabs from './createSlabs';
import createWalls from './createWalls';
import createHoverBoxes from './createHoverBoxes';
import createHoverBoxesRaiser from './createHoverBoxesRaiser';

class SceneItems {
    constructor() {
        this.items = {
            walls: [],
            backs: [],
            slabs: [],
            frame: [],
            bars: [],
            drawers: [],
            drawers_exterior: [],
            doors_exterior: [],
            doors_raiser: [],
            hinges: [],
            buttons: [],
            masking_bars: [],
            interiors: [],
            components: [],
            hovers: [],
        };
    }
}

const itemFunctions = {
    walls: 'createWalls',
    backs: 'createBacks',
    slabs: 'createSlabs',
    frame: 'createFrame',
    bars: 'createBars',
    drawers: 'createDrawersInterior',
    drawers_exterior: 'createDrawersExterior',
    doors_exterior: 'createDoorsExteriors',
    doors_raiser: 'createDoorsRaiser',
    hinges: 'createHinges',
    masking_bars: 'createMaskingBars',
    interiors: 'createInterior',
    buttons: 'createButtons',
    components: 'createHoverBoxes',
    hovers: 'createHoverBoxesRaiser',
};

const buildFunctions = {
    createBacks,
    createBars,
    createButtons,
    createDoorsExteriors,
    createDoorsRaiser,
    createDrawersInterior,
    createDrawersExterior,
    createFrame,
    createHinges,
    createInterior,
    createMaskingBars,
    createSlabs,
    createWalls,
    createHoverBoxes,
    createHoverBoxesRaiser,
};

const hinges = [
    'hinge_door_part_left',
    'hinge_door_part_right',
    'hinge_frame_part_left',
    'hinge_frame_part_right',
];

const shadowItems = [
    'shadow_wall_left',
    'shadow_wall_right',
    'shadow_wall_middle',
    'shadow_floor_left',
    'shadow_floor_right',
    'shadow_floor_middle',
    'typek',
];

export {
    SceneItems,
    itemFunctions,
    buildFunctions,
    shadowItems,
    hinges,
};
