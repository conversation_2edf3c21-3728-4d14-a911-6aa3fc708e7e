export default ({
    geom,
    scene,
    elements,
    sceneItems,
}) => {
    const elementName = 'wall';
    const elementType = 'walls';
    const element = elements[elementName].clone();
    const scale = 10;

    element.scale.y = geom.scale.y / scale;
    element.scale.x = geom.scale.x / scale;
    element.scale.z = geom.scale.z / scale;
    element.position.x = geom.centroid.x;
    element.position.y = geom.centroid.y;
    element.position.z = geom.z1;
    element.name = elementName;

    sceneItems[elementType].push(element);
    scene.add(element);
};
