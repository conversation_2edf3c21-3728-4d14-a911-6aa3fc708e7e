import {
    Bone,
    Group,
    Mesh,
    Source,
    Texture,
    FrontSide,
    BackSide,
    DoubleSide,
    LessEqualDepth,
    MeshStandardMaterial,
    CubeTexture,
    Color,
    MeshBasicMaterial,
    ColorRepresentation,
    LineBasicMaterial,
    RepeatWrapping,
    ClampToEdgeWrapping,
    MirroredRepeatWrapping,
    DepthModes,
    SRGBColorSpace,
    LinearSRGBColorSpace
} from "three";
import {Axis, CubeFace, Direction, VectorXYZ, UVInfo} from "./models/geometry";

export type GeometryTransformFn = (geometryWrapper: Group | Mesh) => Group | Mesh;
export type MaterialTransformFn = (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial;
export type TextureTransformFn = (texture: Texture) => Texture;

export namespace Modifier {
    export const name = (name: string): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            geometryWrapper.name = name;
            return geometryWrapper;
        }
    };

    export const push = (children: (Group | Mesh)[]): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            geometryWrapper.add(...children);
            return geometryWrapper;
        }
    };

    export const scale = (scale: VectorXYZ): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            geometryWrapper.scale.set(scale.x, scale.y, scale.z);
            return geometryWrapper;
        }
    };

    export const move = (position: VectorXYZ): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh): Group | Mesh => {
            geometryWrapper.position.set(position.x, position.y, position.z);
            return geometryWrapper;
        }
    };

    export const mirror = (axis: Axis | null): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh): Group | Mesh => {
            if (axis) geometryWrapper.scale[axis] = -geometryWrapper.scale[axis];
            return geometryWrapper;
        }
    };

    export const rotate = (rotation: { axis: Axis, angle: number } | null): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh): Group | Mesh => {
            if (rotation) geometryWrapper.rotation[rotation.axis] = rotation.angle;
            return geometryWrapper;
        }
    };

    export const toggleVisibility = (visible: boolean): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh): Group | Mesh => {
            geometryWrapper.visible = visible;
            return geometryWrapper;
        }
    };

    export const addTagInfo = (metaData: Record<string, any>): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh): Group | Mesh => {
            geometryWrapper.userData = {
                ...geometryWrapper.userData,
                ...metaData
            }
            return geometryWrapper;
        }
    };

    export const stretchCubeBones = (size: VectorXYZ, anchorName: string = "root"): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            const cubeBones = geometryWrapper.children.find((child) => child.name === anchorName) as Bone;
            cubeBones.children.forEach(bone => {
                if (CubeFace.TOP.includes(bone.name)) bone.position.y = size.y * 0.5;
                if (CubeFace.BOTTOM.includes(bone.name)) bone.position.y = size.y * -0.5;
                if (CubeFace.RIGHT.includes(bone.name)) bone.position.x = size.x * 0.5;
                if (CubeFace.LEFT.includes(bone.name)) bone.position.x = size.x * -0.5;
                if (CubeFace.FRONT.includes(bone.name)) bone.position.z = size.z * 0.5;
                if (CubeFace.BACK.includes(bone.name)) bone.position.z = size.z * -0.5;
            })
            return geometryWrapper;
        }
    };

    export const extendLineBones = (size: VectorXYZ, anchorName: string = "root"): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            const cubeBones = geometryWrapper.children.find((child) => child.name === anchorName) as Bone;
            cubeBones.children.forEach(bone => {
                if (Direction.TOP === bone.name) bone.position.y = (size.y - 0.002) * 0.5;
                if (Direction.BOTTOM === bone.name) bone.position.y = (size.y - 0.002) * -0.5;
                if (Direction.RIGHT === bone.name) bone.position.x = (size.x - 0.002) * 0.5;
                if (Direction.LEFT === bone.name) bone.position.x = (size.x - 0.002) * -0.5;
            })
            return geometryWrapper;
        }
    };

    export const updateShadowSettings = (settings: { castShadow: boolean, receiveShadow: boolean }): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            geometryWrapper.traverse((child) => {
                if (child instanceof Mesh) {
                    child.castShadow = settings.castShadow;
                    child.receiveShadow = settings.receiveShadow;
                }
            })
            return geometryWrapper;
        }
    }

    export const setMaterial = (material: MeshStandardMaterial | MeshBasicMaterial): GeometryTransformFn => {
        return (geometryWrapper: Group | Mesh) => {
            geometryWrapper.traverse((child) => {
                if (child instanceof Mesh) {
                    child.material = material;
                }
            })
            return geometryWrapper;
        }
    }




    // MATERIAL TRANSFORMS

    export const setNameMaterialProperties = (name: string): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            material.name = name;
            return material;
        }
    }

    export const setAlbedoMaterialProperties = (color: ColorRepresentation, map: Texture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            material.color = new Color(color);
            (<MeshStandardMaterial | MeshBasicMaterial>material).map = map;
            material.needsUpdate = true;
            return material;
        }
    }

    export const setMetalnessMaterialProperties = (scale: number, map: Texture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (<MeshStandardMaterial>material).metalness = scale;
            (<MeshStandardMaterial>material).metalnessMap = map;
            return material;
        }
    }

    export const setRoughnessMaterialProperties = (scale: number, map: Texture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (<MeshStandardMaterial>material).roughness = scale;
            (<MeshStandardMaterial>material).roughnessMap = map;
            return material;
        }
    }

    export const setNormalMaterialProperties = (scale: { x: number, y: number }, map: Texture| null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (<MeshStandardMaterial>material).normalScale.set(scale.x, scale.y);
            (<MeshStandardMaterial>material).normalMap = map;
            return material;
        }
    }

    export const setBumpMaterialProperties = (scale: number, map: Texture| null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (<MeshStandardMaterial>material).bumpScale = scale;
            (<MeshStandardMaterial>material).bumpMap = map;
            return material;
        }
    }

    export const setEnvironmentMaterialProperties = (scale: number, map: CubeTexture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (<MeshStandardMaterial>material).envMapIntensity = scale;
            (<MeshStandardMaterial>material).envMap = map;
            return material;
        }
    }

    export const setOpacityMaterialProperties = (scale: number, map: Texture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            material.opacity = scale;
            material.transparent = scale < 1;
            if (!(material instanceof LineBasicMaterial)) material.alphaMap = map;
            return material;
        }
    }

    export const setSideMaterialProperties = (side: 'front' | 'back' | 'double'): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            material.side = 
                side === 'front' ? FrontSide : 
                side === 'back' ? BackSide : DoubleSide;
            return material;
        }
    }

    export const setDepthMaterialProperties = (write: boolean, test: boolean = true, mode: DepthModes = LessEqualDepth): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            material.depthWrite = write;
            material.depthTest = test;
            material.depthFunc = mode;
            return material;
        }
    }

    export const setEmissiveMaterialProperties = (scale: number,  color: ColorRepresentation, map: Texture | null): MaterialTransformFn => {
        return (material: MeshStandardMaterial | MeshBasicMaterial | LineBasicMaterial) => {
            (material as MeshStandardMaterial).emissive = new Color(color);
            (material as MeshStandardMaterial).emissiveIntensity = scale;
            (material as MeshStandardMaterial).emissiveMap = map;
            return material;
        }
    }



    // TEXTURE TRANSFORMS

    export const assignImageTexture = (imageSource: Source): TextureTransformFn => {
        return (texture: Texture) => {
            texture.source = imageSource;
            texture.anisotropy = 4;
            texture.colorSpace = SRGBColorSpace
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };

    export const nameTexture = (name: string,): TextureTransformFn => {
        return (texture: Texture) => {
            texture.name = name;
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };

    export const repeatTexture = (scale: { x: number , y: number }, mode: 'repeat' | 'clamp' | 'mirror' = 'repeat'): TextureTransformFn => {
        return (texture: Texture) => {
            texture.wrapS = texture.wrapT = 
                mode === 'repeat' ? RepeatWrapping : 
                mode === 'clamp' ? ClampToEdgeWrapping : MirroredRepeatWrapping;
            texture.repeat.set(scale.x, scale.y);
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };

    export const rotateTexture = (angle: number): TextureTransformFn => {
        return (texture: Texture) => {
            texture.rotation = angle;
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };

    export const offsetTexture = (offset: { x: number , y: number }): TextureTransformFn => {
        return (texture: Texture) => {
            texture.offset.set(offset.x, offset.y)
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };

    export const unwrapUVTexture = (uvSetup: UVInfo): TextureTransformFn => {
        return (texture: Texture) => {
            texture.wrapS = texture.wrapT = MirroredRepeatWrapping;
            texture.offset.set(uvSetup.offset.x, uvSetup.offset.y)
            texture.repeat.set(uvSetup.repeat.x * 2, uvSetup.repeat.y * 2)
            texture.rotation = uvSetup.rotation
            if (texture.source) texture.needsUpdate = true;
            return texture;
        }
    };
}
