import {Mesh, MeshBasicMaterial, Group, Light} from 'three';
import { Space} from '../space';
import {rotateDoorWing, slideDrawer, transitFillLight, transitLight} from './motion';
import {RendererSettings} from "../common/config";

export namespace Animation {
  export const fullHighlightSection = (sectionId: string) => {
    Space.getShelf().traverse((part) => {
      if (part.name === "boxGeometry"
          && part!.parent!.userData.hoverIsActive
          && part!.parent!.parent!.userData.segmentId === sectionId
          && part!.parent!.parent!.userData.selectable) {
        ((part as Mesh).material as MeshBasicMaterial).opacity = .5;
      }
    })
  }

  export const halfHighlightSection = (sectionId: string) => {
    Space.getShelf().traverse((part) => {
      if (part.name === "boxGeometry"
          && part!.parent!.userData.hoverIsActive
          && part!.parent!.parent!.userData.segmentId === sectionId
          && part!.parent!.parent!.userData.selectable) {
        ((part as Mesh).material as MeshBasicMaterial).opacity = .2;
      }
    })
  }

  export const unHighlightSection = (sectionId: string) => {
    Space.getShelf().traverse((part) => {
      if (part.name === "boxGeometry"
          && part!.parent!.userData.hoverIsActive
          && part!.parent!.parent!.userData.segmentId === sectionId) {
        ((part as Mesh).material as MeshBasicMaterial).opacity = 0;
      }
    })
  }

  export const hideSegment = (shelfPart: string, sectionId: string | null = null) => {
    Space.getShelf().traverse((part) => {
      if (part.name === shelfPart && part.userData.sectionId === sectionId) {
        part.visible = false;
      }
    })
  }

  export const showSegment = (shelfPart: string, sectionId: string | null = null) => {
    Space.getShelf().traverse((part) => {
      if (part.name === shelfPart && part.userData.sectionId === sectionId) {
        part.visible = true;
      }
    })
  }

  export const showDimensions = (dimensionContext: ('interior' | 'exterior' | 'feature' | 'silhouette')[], visibility: 'whenOpen' | 'whenClosed') => {
    Space.getShelf().traverse((part) => {
      if (part.name === 'dimensions') {
        part.children.forEach((dimension) => {
          dimension.visible = dimensionContext.includes(dimension.userData.context) && 
          (dimension.userData.visibility === 'always' || dimension.userData.visibility === visibility)
        })
      }
    })
  }

  export const hideDimensions = () => {
    Space.getShelf().traverse((part) => {
      if (part.name === 'dimensions') {
        part.children.forEach((dimension) => { dimension.visible = false })
      }
    })
  }

  export const openDoorsInSectionInstant = (sectionId: string, instant: boolean = false) =>
    doorWingMotionPromise(sectionId, 'open', 0, 0, true);

  export const ajarDoorsInSectionInstant = (sectionId: string, instant: boolean = false) =>
    doorWingMotionPromise(sectionId, 'ajar', 0, 0, true);

  export const closeDoorsInSectionInstant = (sectionId: string, instant: boolean = false) =>
    doorWingMotionPromise(sectionId, 'close', 0, 0, true);

  export const openDrawersInSectionInstant = (sectionId: string, instant: boolean = false) =>
    drawerMotionPromise(sectionId, 'open', 0, 0, true);

  export const ajarDrawersInSectionInstant = (sectionId: string, instant: boolean = false) =>
    drawerMotionPromise(sectionId, 'ajar', 0, 0, true);

  export const closeDrawersInSectionInstant = (sectionId: string, instant: boolean = false) =>
    drawerMotionPromise(sectionId, 'close', 0, 0, true);


  export const openDoorsInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    doorWingMotionPromise(sectionId, 'open', timming.duration, timming.delay, false);

  export const ajarDoorsInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    doorWingMotionPromise(sectionId, 'ajar', timming.duration, timming.delay, false);

  export const closeDoorsInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    doorWingMotionPromise(sectionId, 'close', timming.duration, timming.delay, false);

  export const openDrawersInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    drawerMotionPromise(sectionId, 'open', timming.duration, timming.delay, false);

  export const ajarDrawersInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    drawerMotionPromise(sectionId, 'ajar', timming.duration, timming.delay, false);

  export const closeDrawersInSection = (sectionId: string, timming: { duration: number, delay: number }) =>
    drawerMotionPromise(sectionId, 'close', timming.duration, timming.delay, false);

  export const setDayLight = (instant: boolean) => {
    const primaryLight = Space.getLights().getObjectByName('primary') as Light;
    const primaryDayLightConfig = RendererSettings.getLightsConfig()['primary']
    transitLight(primaryLight, {
      instant: false,
      duration: 0.6,
      delay: 0,
      target: {
        intensity: primaryDayLightConfig.intensity,
        color: primaryDayLightConfig.color,
        position: primaryDayLightConfig.position,
      },
    })

    const fillLight = Space.getLights().getObjectByName('fill') as Light;
    const fillDayLightConfig = RendererSettings.getLightsConfig()['fill'];
    transitFillLight(fillLight, {
      instant: false,
      duration: 0.6,
      delay: 0,
      target: {
          intensity: fillDayLightConfig.intensity,
          color: fillDayLightConfig.color,
          position: { x: 0, y: 0, z: 0 },
      },
    })
  }

  export const setEveningLight = (instant: boolean) => {
    const primaryLight = Space.getLights().getObjectByName('primary') as Light;
    transitLight(primaryLight, {
      instant: false,
      duration: 0.6,
      delay: 0,
      target: {
        intensity: 1.5,
        color: 0xffffff,
        position: { x: 4, y: .5, z: 3.8 },
      },
    })

    const fillLight = Space.getLights().getObjectByName('fill') as Light;
    transitFillLight(fillLight, {
      instant: false,
      duration: 0.6,
      delay: 0,
      target: {
        intensity: 0.69,
        color: 0xffffff,
        position: { x: 0, y: 0, z: 0 },
      },
    })
  }
}

const drawerMotionPromise = (sectionId: string, action: 'open' | 'ajar' | 'close', duration: number, delay: number, instant: boolean) => {
  const drawersToTranslate: any[] = [];
  Space.getShelf().traverse((part) => {
    if (part.name === 'drawer' && part.userData.sectionId === sectionId) {
      drawersToTranslate.push({ 
        geometry: part,
        motionSettings: {
          instant,
          duration,
          delay,
          target: part.userData.motion[action],
        }
      });
    }
  });

  let updatedCount = 0;
  return new Promise<void>((resolve) => {
    if (drawersToTranslate.length === 0) resolve();
    drawersToTranslate.forEach((drawer, idx) => { 
      slideDrawer(drawer.geometry, drawer.motionSettings, () => {
        updatedCount++;
        if (updatedCount === drawersToTranslate.length) resolve();
      });
    });
  })
}

const doorWingMotionPromise = (sectionId: string, action: 'open' | 'ajar' | 'close', duration: number, delay: number, instant: boolean) => {
  const doorsToTranslate: any[] = [];
  Space.getShelf().traverse((part) => {
    if (part.name === 'door' && part.userData.sectionId === sectionId) {
      doorsToTranslate.push({ 
        geometry: part,
        motionSettings: {
          instant,
          duration,
          delay,
          target: part.userData.motion[action],
        }
      });
    }
  });

  let updatedCount = 0;
  return new Promise<void>((resolve) => {
    if (doorsToTranslate.length === 0) resolve();
    doorsToTranslate.forEach((door, idx) => {
      rotateDoorWing(door.geometry, door.motionSettings, () => {
        updatedCount++;
        if (updatedCount === doorsToTranslate.length) resolve();
      })
    });
  })
}
