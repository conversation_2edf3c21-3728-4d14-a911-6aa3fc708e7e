import * as THREE from 'three'
import { defaultSettings } from './tylko-camera-settings';
import { OrbitControls2 } from './tylko-camera-orbit-perspective'

export function TylkoCamera({
    view,
    settings = {},
}) {
    this.settings = settings;
    this.defaultSettings = defaultSettings;
    this.currentOffsetTopValue = 0;

    this.range = this.settings.range || this.defaultSettings.range;
    this.target = { x: 0, y: .005, z: 0 };
    this.position = { x: 0, y: 1, z: 2 };

    this.geometryOffset = this.settings.geometryMargins || this.defaultSettings.geometryMargins;
    this.shelfScreenEdgeOffset = this.settings.screenMargins || this.defaultSettings.screenMargins;
    this.shelfPolarAngles = this.settings.shelfPolarAngles || this.defaultSettings.shelfPolarAngles;
    this.globalPhi = this.settings.globalPhi || this.defaultSettings.globalPhi;
    this.componentScreenEdgeOffset = {
        left: 0, right: 0, top: 0, bottom: 0,
    };
    this.geometryBox = new THREE.Box3();
    this.camera = new THREE.PerspectiveCamera(
        this.settings.fov || this.defaultSettings.fov,
        2,
        this.range.min,
        this.range.max,
    );

    this.controls = new OrbitControls2(this.camera, view);
    this.controls.polarAngle = { min: Math.PI / 4, max: Math.PI / 2 };
    this.controls.azimuthAngle = { min: -Math.PI, max: Math.PI / 2 };
    this.controls.noTransitionAnimation = true;
    this.noTransitionAnimation = true;

    this.camera.near = this.range.min;
    this.camera.far = this.range.max;
    this.camera.updateProjectionMatrix();

    this.camera.position.set( 0, 1.7, 6 );
    this.controls.target.set(0, .005, 0);
    this.controls.new_theta = -0.52;
    this.controls.update();

    this.updateGeometry = function(min, max, fixed) {
        this.controls.resetAnimation();
        if (!this.dynamicTarget) {
            this.target.y = (0.5 * max.y) + (55 * 0.001);
            this.controls.new_target = new THREE.Vector3(
                this.target.x + ((this.geometryOffset.right - this.geometryOffset.left) * 0.5),
                this.target.y + ((this.geometryOffset.bottom - this.geometryOffset.top + this.currentOffsetTopValue) * 0.5),
                this.target.z,
            );
        }

        this.updatePoints(min, max);

        if (fixed) {
            this.controls.noTransitionAnimation = true;
        }
        this.controls.updateZoom();
        this.controls.update();
        this.controls.noTransitionAnimation = this.noTransitionAnimation;
    };

    this.updatePoints = function(min, max) {
        const o = this.geometryOffset;
        const coor = [
            min.x - o.left,
            max.x + o.right,
            min.y - o.bottom,
            max.y + o.top,
            min.z - o.back,
            max.z + o.front,
        ];


        this.controls.points[0].set(coor[0], coor[2], coor[4]); // 000
        this.controls.points[1].set(coor[1], coor[3], coor[5]); // 111
        this.controls.points[2].set(coor[0], coor[2], coor[5]); // 001
        this.controls.points[3].set(coor[0], coor[3], coor[4]); // 010
        this.controls.points[4].set(coor[0], coor[3], coor[5]); // 011
        this.controls.points[5].set(coor[1], coor[2], coor[4]); // 100
        this.controls.points[6].set(coor[1], coor[2], coor[5]); // 101
        this.controls.points[7].set(coor[1], coor[3], coor[4]); // 110
    };

    this.updateAspect = function(aspect) {
        this.camera.aspect = aspect;
        this.camera.updateProjectionMatrix();
    };

    this.setShelfViewFinal = function(min, max, fixed, tripleSnap) {
        this.controls.noTransitionAnimation = fixed === true ? true : this.noTransitionAnimation;
        this.setShelfMode(tripleSnap);
        if (min !== undefined) {
            this.updateGeometry(min, max)
        }
        this.controls.noTransitionAnimation = this.noTransitionAnimation;
    };

    this.setComponentViewFinal = function(min, max, center) {
        this.controls.resetAnimation();
        this.setComponentMode();
        center = center === undefined ? min.x + (max.x - min.x) / 2 : center;
        const side = Math.max(center - min.x, max.x - center); // NOTE: jeśli lokal edge: 800;
        const tarX = center + this.geometryOffset.right - this.geometryOffset.left;
        const tarY = (min.y + max.y) / 2 + (this.geometryOffset.top - this.geometryOffset.bottom);
        this.controls.new_target = new THREE.Vector3(
            tarX, tarY, this.controls.target.z,
        );
        this.controls.screenEdgeOffset = this.componentScreenEdgeOffset;

        this.controls.snapTo = [
            { theta: 0, phi: 1.52, x: tarX },
        ];

        this.updatePoints(
            { x: center - side, y: min.y, z: min.z },
            { x: center + side, y: max.y, z: max.z },
        );
        this.controls.updateZoom();
        this.controls.update();
    };

    /**
     * @type function
     */
    this.setShelfMode = function(tripleSnap = true) {
        this.controls.noZoom = true;
        this.controls.noPan = true;
        this.controls.noRotate = false;
        this.controls.noLifeZoom = true;
        this.controls.noAutoZoom = true;
        this.controls.animationStepParameters.distance = 0.08;
        this.controls.rotateSpeed = 0.5;
        this.controls.azimuthAngle = { min: -Math.PI / 6 * 2.7, max: Math.PI / 6 * 2.7 };
        this.controls.polarAngle = this.shelfPolarAngles;
        this.controls.screenEdgeOffset = this.settings.screenMargins || this.shelfScreenEdgeOffset;
        this.geometryOffset = this.settings.geometryMargins || this.geometryOffset;

        this.controls.mouseButtons = {
            ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT,
        };
        this.controls.touchMode = {
            ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4,
        };

        if (tripleSnap) this.setTripleSnap();
        this.controls.noSnap = false;
        this.controls.noSnapReal = false;
    };

    /**
    * @type function
    */
    this.setComponentMode = function() {
        this.controls.noZoom = true;
        this.controls.noPan = false;
        this.controls.noRotate = true;
        this.controls.noLifeZoom = true;
        this.controls.noAutoZoom = true;
        this.controls.animationStepParameters.distance = 0.08;
        this.controls.rotateSpeed = 0.4;
        this.controls.azimuthAngle = { min: -Math.PI / 6 * 5, max: Math.PI / 6 * 5 };
        this.controls.polarAngle = { min: Math.PI * 0.22, max: Math.PI * 0.55 };
        this.controls.screenEdgeOffset = this.componentScreenEdgeOffset;
        this.geometryOffset = this.settings.component.geometryMargins || this.defaultSettings.component.geometryMargins;

        this.controls.mouseButtons = {
            PAN: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, ORBIT: THREE.MOUSE.RIGHT,
        };
        this.controls.touchMode = {
            ORBIT: 4, ZOOM: 2, PAN: 3, ZOOMPAN: 1,
        };

        this.controls.noSnap = false;
        this.controls.noSnapReal = false;
        this.controls.noTransitionAnimation = this.noTransitionAnimation;
    };

    this.setFxedMode = function() {
        this.controls.noZoom = true;
        this.controls.noPan = true;
        this.controls.noRotate = true;
        this.controls.noLifeZoom = true;
        this.controls.noAutoZoom = true;
        this.controls.noSnap = false;
        this.controls.noSnapReal = false;
        this.controls.noTransitionAnimation = this.noTransitionAnimation;
    };

    this.setViewFinal = function(viewName) {
        switch (viewName) {
        case 'frontTripleSnap':
            this.controls.new_theta = 0;
            this.controls.new_phi = this.globalPhi;
            break;
        case 'leftTripleSnap':
            this.controls.new_theta = -0.52;
            this.controls.new_phi = this.globalPhi;
            break;
        case 'front':
            this.controls.snapTo = [
                { theta: 0, phi: this.globalPhi },
            ];
            break;
        case 'side':
            this.controls.snapTo = [
                { theta: -0.43, phi: this.globalPhi },
            ];
            break;
        case 'deepSide':
            this.controls.snapTo = [
                { theta: -1.0, phi: this.globalPhi },
            ];
            break;
        case 'lowSide':
            this.controls.snapTo = [
                { theta: -0.8, phi: 1.6 },
            ];
            break;
        default:
            return
        }
        this.controls.update();
        if (viewName.includes('TripleSnap')) {
            this.setTripleSnap();
        }
    };

    this.setTripleSnap = function() {
        if (this.settings.snapping && this.settings.snapping.length > 0) {
            this.controls.snapTo = [...this.settings.snapping];
        } else {
            this.controls.snapTo = this.defaultSettings.snapping;
        }
    };

    this.setOffsetTop = (offsetTopValue) => {
        this.currentOffsetTopValue = offsetTopValue;
    }

    this.updateSettings = settings => {
        this.settings = settings;
        this.controls.snapTo = settings.snapping;
    }

    this.setShelfMode();
    this.controls.noTransitionAnimation = this.noTransitionAnimation;
}
