const defaultSettings = {
    range: { min: 100, max: 30000 },
    fov: 30,
    snapping: [
        { theta: 0.52, phi: 1.52, x: 0 },
        { theta: -0.52, phi: 1.52, x: 0 },
        { theta: 0, phi: 1.52, x: 0 },
    ],
    globalPhi: 1.52,
    geometryMargins: {
        left: 100, right: 100, top: 0, bottom: 50, front: 0, back: 0,
    },
    screenMargins: {
        left: 0, right: 0, top: 0, bottom: 0,
    },
    component: {
        geometryMargins: {
            left: 60, right: 60, top: 60, bottom: 60, front: 500, back: 0,
        },
    },
    animationSpeed: 1,
    shelfPolarAngles: { min: Math.PI / 6, max: Math.PI / 2 },
};

export { defaultSettings };
