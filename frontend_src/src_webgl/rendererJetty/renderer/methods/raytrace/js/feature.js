import shadowsRaymarcherCaster from 'inline:./../build/shaders/shelf.vert.perspective.glsl';

export function softShadows(
    canvasElement,
    sceneInformation,
    cameraObject,
    sceneObject,
    pivotObject,
    getIvySize,
    build3d
) {

    var settings = null;

    var rl = regl(canvasElement);
    let time = 0;

    let dynamicUniforms = {

    };

    var _uniforms = {
        color: rl.prop('color'),
        time: rl.prop('time'),
        viewport: [800,600],

        
        viewMatrix: rl.prop('viewMatrix'),
        three_scene_matrix: rl.prop('three_scene_matrix'),
        sceneM: rl.prop('sceneM'),
        pivot: rl.prop('pivot'),
        data: rl.prop('data'),
        lp: rl.prop('lp'),
        gap: rl.prop('gap'),
        iterations: rl.prop('iter'),

    };

  

    var uniformsIdents = {};

    for(let j=0; j < MAX_IDENTS; j++) {
        uniformsIdents[`idents[${j}]`] = rl.prop(`idents${j}`);
    }

    var uniforms = { 

        cameraPosition: rl.prop('cameraPosition'),
        viewProjectionMatrix: rl.prop('cameraProjection'),
        ivybb: rl.prop('ivybb'),

        ..._uniforms,
        ...uniformsIdents
     };
     
    const castShadows = rl({
        frag: shadowsRaymarcher,
        vert: shadowsRaymarcherCaster,
        attributes: {
            position: rl.buffer(quad.verts),
            uvs: rl.buffer(quad.uvs)
        },
        uniforms: uniforms,
        count: 6
    });



    let updateTransforms = () => {

        cameraObject.position.z = settings.cameraz;

      //  pivotObject.updateMatrix();
        cameraObject.updateMatrix();
        cameraObject.updateProjectionMatrix();
        inverseProjectionMatrix.getInverse(cameraObject.projectionMatrix.clone());

    };

    /*rl.frame(() => {
      
    });

*/

    const render = () => {
        rl.clear({
                depth: 1,
                color: [0, 0, 0, 1]
        });
            
        updateTransforms();

        let idents = build3d.getIndents();
        let parsedIdents = {};

        for(let v=0; v < MAX_IDENTS; v++) {
            if(idents.length > v-1) {
                parsedIdents[`idents${v}`] = idents[v];
            } else {
                parsedIdents[`idents${v}`] = [0,0,0,0];
            }
        }
        window.parsedIdents = parsedIdents;

        castShadows({ 

            time: time+=0.1,

            viewport: [canvasElement.width, canvasElement.height],
            cameraProjection: cameraObject
                            .matrix
                            .clone()
                            .multiply(inverseProjectionMatrix)
                            .toArray(),

            three_scene_matrix: inverseProjectionMatrix,
            viewMatrix: cameraObject.projectionMatrix.toArray(),
            cameraPosition: [
                cameraObject.position.x,
                cameraObject.position.y,
                cameraObject.position.z
            ],
            data: [settings.x,settings.y,settings.z ],
            lp: settings.power,
            ivybb: getIvySize(),
            gap: settings.shelfGap,
            iter: settings.iterations,
            ...parsedIdents
        });
    };

    return { render }
};

