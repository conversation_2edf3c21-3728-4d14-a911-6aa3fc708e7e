import {
    PlaneGeometry,
    Vector3,
    MeshBasicMaterial,
    Mesh,
    Raycaster,
    DoubleSide,
} from 'three';

class LocalEdge {
    constructor(scene, camera, container) {
        this.scene = scene;
        this.camera = camera;
        this.container = container;
        this.settings = {
            lineWidth: 0.018,
            // for white furniture: 0x7C7D81
            lineStateColor: 0xffffff,
            lineStateName: 'localEdge:lineState',
            lineMarginColor: 0xc6c8d0,
            lineMarginMaxName: 'localEdge:lineMax',
            lineMarginMinName: 'localEdge:lineMin',
            ballDimensions: 0.050,
            ballColor: 0xfffff,
            ballName: 'localEdge:ball',
        };
        this.shapes = [];
        this.raycaster = new Raycaster();
        this.state = []
    }

    getCoordinatesFor2d() {
        const tempV = new Vector3();
        const coords2d = {
            state: [],
            marginMax: [],
            marginMin: [],
            minLine: [[], []],
            maxLine: [[], []],
        };

        const calculateProjection = item => {
            item.updateMatrixWorld(true, false);
            item.getWorldPosition(tempV);
            tempV.project(this.camera);
            const x = (tempV.x * 0.5 + 0.5) * this.container.clientWidth;
            const y = (tempV.y * -0.5 + 0.5) * this.container.clientHeight;
            return { x, y }
        };

        this.scene.children.forEach(item => {
            // localEdge button
            if (item.name === this.settings.ballName) coords2d.state.push(calculateProjection(item));

            // localEdge max
            if (item.name === this.settings.lineMarginMaxName) coords2d.marginMax.push(calculateProjection(item));
            if (item.name === 'localEdge:dotTopMax:0') {
                coords2d.maxLine[0].x1 = calculateProjection(item).x;
                coords2d.maxLine[0].y1 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotTopMax:1') {
                coords2d.maxLine[1].x1 = calculateProjection(item).x;
                coords2d.maxLine[1].y1 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotBottomMax:0') {
                coords2d.maxLine[0].x2 = calculateProjection(item).x;
                coords2d.maxLine[0].y2 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotBottomMax:1') {
                coords2d.maxLine[1].x2 = calculateProjection(item).x;
                coords2d.maxLine[1].y2 = calculateProjection(item).y;
            }

            // localEdge min
            if (item.name === this.settings.lineMarginMinName) coords2d.marginMin.push(calculateProjection(item));
            if (item.name === 'localEdge:dotTopMin:0') {
                coords2d.minLine[0].x1 = calculateProjection(item).x;
                coords2d.minLine[0].y1 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotTopMin:1') {
                coords2d.minLine[1].x1 = calculateProjection(item).x;
                coords2d.minLine[1].y1 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotBottomMin:0') {
                coords2d.minLine[0].x2 = calculateProjection(item).x;
                coords2d.minLine[0].y2 = calculateProjection(item).y;
            }
            if (item.name === 'localEdge:dotBottomMin:1') {
                coords2d.minLine[1].x2 = calculateProjection(item).x;
                coords2d.minLine[1].y2 = calculateProjection(item).y;
            }
        });
        return coords2d;
    }

    removeShapes() {
        this.shapes.forEach(i => this.scene.remove(i));
        this.shapes = []
    }

    multiplyBySceneScale(coords) {
        if (!coords) return null;
        const SCEEN_SCALE = 0.001;
        return coords.map(coord => ({
            ...coord,
            x: coord.x * SCEEN_SCALE,
            y: coord.y * SCEEN_SCALE,
            z: coord.z * SCEEN_SCALE,
            top: coord.top * SCEEN_SCALE,
            bottom: coord.bottom * SCEEN_SCALE,
            left: coord.left * SCEEN_SCALE,
            right: coord.right * SCEEN_SCALE,
        }));
    }

    drawLocalEdgeHelpers(originCoords) {
        const coords = this.multiplyBySceneScale(originCoords);

        this.removeShapes();
        this.state = coords;
        if (!coords) return;
        coords.forEach((coord, index) => {
            this.drawBall({ coords: coord });
            // state
            this.drawLine({
                positionX: coord.x,
                positionY: coord.y,
                positionZ: coord.z,
                height: coord.top - coord.bottom,
                color: this.settings.lineStateColor,
                name: this.settings.lineStateName,
                thickness: 9,
            });
            // min
            this.drawLine({
                positionX: coord.left,
                positionY: coord.y,
                positionZ: coord.z,
                height: coord.top - coord.bottom,
                color: this.settings.lineMarginColor,
                name: this.settings.lineMarginMinName,
                thickness: 3,
            });
            this.drawDots({
                positionX: coord.left,
                positionZ: coord.z,
                top: coord.top,
                bottom: coord.bottom,
                color: this.settings.lineMarginColor,
                suffix: 'Min',
                index,
            });
            // max
            this.drawLine({
                positionX: coord.right,
                positionY: coord.y,
                positionZ: coord.z,
                height: coord.top - coord.bottom,
                color: this.settings.lineMarginColor,
                name: this.settings.lineMarginMaxName,
                thickness: 3,
            });
            this.drawDots({
                positionX: coord.right,
                positionZ: coord.z,
                top: coord.top,
                bottom: coord.bottom,
                color: this.settings.lineMarginColor,
                suffix: 'Max',
                index,
            });
        });
        const coords2d = this.getCoordinatesFor2d();

        return { coords3d: coords, ...coords2d }
    }

    drawBall({
        coords,
    }) {
        const geometry = new PlaneGeometry(this.settings.ballDimensions, this.settings.ballDimensions, 2);
        const material = new MeshBasicMaterial({ color: this.settings.ballColor, side: DoubleSide });
        const plane = new Mesh(geometry, material);
        plane.position.set(coords.x, coords.y, coords.z + 0.003);
        plane.name = this.settings.ballName;
        plane.visible = false;
        this.shapes.push(plane);
        this.scene.add(plane);
    }

    drawLine({
        positionX,
        positionY,
        positionZ,
        height,
        color,
        thickness = this.settings.lineWidth,
        name = 'localEdge:line',
    }) {
        const geometry = new PlaneGeometry(thickness, height, 2);
        const material = new MeshBasicMaterial({ color, side: DoubleSide });
        const plane = new Mesh(geometry, material);
        plane.position.set(positionX, positionY, positionZ + 0.004);
        plane.name = name;
        plane.visible = false;
        this.shapes.push(plane);
        this.scene.add(plane);
    }

    drawDots({
        positionX,
        positionZ,
        top,
        bottom,
        color,
        suffix,
        index,
    }) {
        const geometry = new PlaneGeometry(.010, .010, 1);
        const material = new MeshBasicMaterial({ color, side: DoubleSide });
        material.needsUpdate = true;
        const plane = new Mesh(geometry, material);

        plane.position.set(positionX, top, positionZ + 0.005);
        plane.name = `localEdge:dotTop${suffix}:${index}`;
        plane.visible = false;
        this.shapes.push(plane);
        this.scene.add(plane);

        const planeClone = plane.clone();
        planeClone.position.set(positionX, bottom, positionZ + 0.004);
        planeClone.name = `localEdge:dotBottom${suffix}:${index}`;
        planeClone.visible = false;
        this.shapes.push(planeClone);
        this.scene.add(planeClone);
    }
}

export default LocalEdge;
