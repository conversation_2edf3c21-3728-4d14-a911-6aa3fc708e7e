import { snap } from "../helpers/snapping_box_version";
import { getRandomInt } from '../helpers/utils'

export function backend_strategy(scene, items, groups, strategy, shelf) {
    console.time('strategy start')

    scene.clearItems(items)
    // for counting already added items
    let addedElements = []

    //randomization domain
    let shelfWidth = shelf.getWidth()
    let shelfHeight = shelf.getHeight()

    let numberOfRandomElements;

    if (strategy.number_of_items_approach === "ABSOLUTE") {
        numberOfRandomElements = strategy.number_of_items
    } else if (strategy.number_of_items_approach === "PERCENTAGE OF OPENINGS") {
        numberOfRandomElements = Math.ceil(strategy.number_of_items/100 * shelf.getOpeningsNumber())
    }

    // if there is no rules in strategy - lets take all items
    if (strategy.rules.length == 0) {
        const randomElements = items.sort(() => Math.random() - Math.random())

        for (let i=0; i < randomElements.length && addedElements.length < numberOfRandomElements; i++) {
            let el = randomElements[i];
            el.controller.position.x = getRandomInt(-shelfWidth / 2, shelfWidth / 2);
            el.controller.position.y = getRandomInt(0, shelfHeight);
            if (snap(el, scene, items, shelf, strategy)) {
                scene.addItem(el.controller)
                addedElements.push(el)
            }
        }
    } else {
        let groupNumberCounter = {}
        // TODO: right now is FIRST SLOT TAKES ALL, this should go to strategy, for even check or FIFO checking
        // TODO: now those steps are almost identical - if it will survive first feedback loop - needs to be rewrited
        // we have rules, so first approach is to fill minimum for each category, with original order
        strategy.rules.forEach(rule => {
            let itemGroup = groups.find(x=> x.id ===  rule.item_group)

            groupNumberCounter[`group_${itemGroup.id}`] = (groupNumberCounter[`group_${itemGroup.id}`] || 0)

            let elementIds = itemGroup.items.map(item => items.find(x=> x.controller.data.id == item)).filter(x=>typeof x !== 'undefined')
            let randomElements = elementIds.sort(() => Math.random() - Math.random())
            for (let i=0; i < randomElements.length && addedElements.length < numberOfRandomElements
                            && groupNumberCounter[`group_${itemGroup.id}`] < rule.min_number_of_items; i++) {
                let el = randomElements[i];
                if (!addedElements.includes(el)) {
                    el.controller.position.x = getRandomInt(-shelfWidth / 2, shelfWidth / 2);
                    el.controller.position.y = getRandomInt(0, shelfHeight);
                    if (snap(el, scene, items, shelf, strategy)) {
                        scene.addItem(el.controller)
                        addedElements.push(el)
                        groupNumberCounter[`group_${itemGroup.id}`] += 1
                    }
                }
            }
        })
        console.log('after step min', groupNumberCounter, 'items:', addedElements)
        // second stage, if min is not enough to fill whole strategy, lets add other items, with respect to all groups
        // if not, we are ok already.
        // so, its similar step as first one, but its trying to fill till
        if (addedElements.length < numberOfRandomElements) {
            strategy.rules.forEach(rule => {
                let itemGroup = groups.find(x=> x.id ===  rule.item_group)

                groupNumberCounter[`group_${itemGroup.id}`] = (groupNumberCounter[`group_${itemGroup.id}`] || 0)

                let elementIds = itemGroup.items.map(item => items.find(x=> x.controller.data.id == item)).filter(x=>typeof x !== 'undefined')
                let randomElements = elementIds.sort(() => Math.random() - Math.random())
                for (let i=0; i < randomElements.length && addedElements.length < numberOfRandomElements
                                && groupNumberCounter[`group_${itemGroup.id}`] < rule.proposed_number_of_items; i++) { // proposed_number_of_items <- only thing that changed in this loop
                    let el = randomElements[i];
                    if (!addedElements.includes(el)) {
                        el.controller.position.x = getRandomInt(-shelfWidth / 2, shelfWidth / 2);
                        el.controller.position.y = getRandomInt(0, shelfHeight);
                        if (snap(el, scene, items, shelf, strategy)) {
                            scene.addItem(el.controller)
                            addedElements.push(el)
                            groupNumberCounter[`group_${itemGroup.id}`] += 1
                        }
                    }
                }
            })
        }
        console.log('after step proposed', groupNumberCounter, 'items:', addedElements)
        // wow, we are in stage three, minimum is done, proposed is done, time for trying to max things
        if (addedElements.length < numberOfRandomElements) {
            strategy.rules.forEach(rule => {
                let itemGroup = groups.find(x=> x.id ===  rule.item_group)
                groupNumberCounter[`group_${itemGroup.id}`] = (groupNumberCounter[`group_${itemGroup.id}`] || 0)

                let elementIds = itemGroup.items.map(item => items.find(x=> x.controller.data.id == item)).filter(x=>typeof x !== 'undefined')
                let randomElements = elementIds.sort(() => Math.random() - Math.random())
                for (let i=0; i < randomElements.length && addedElements.length < numberOfRandomElements
                                && groupNumberCounter[`group_${itemGroup.id}`] < rule.max_number_of_items; i++) { // max_number_of_items <- only thing that changed in this loop
                    let el = randomElements[i];
                    if (!addedElements.includes(el)) {
                        el.controller.position.x = getRandomInt(-shelfWidth / 2, shelfWidth / 2);
                        el.controller.position.y = getRandomInt(0, shelfHeight);
                        if (snap(el, scene, items, shelf, strategy)) {
                            scene.addItem(el.controller)
                            addedElements.push(el)
                            groupNumberCounter[`group_${itemGroup.id}`] += 1
                        }
                    }
                }
            })
        }
        console.log('after step max', groupNumberCounter, 'items:', addedElements)

    }
    console.timeEnd('strategy start')
}
