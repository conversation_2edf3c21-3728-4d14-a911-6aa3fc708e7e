module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {
    var Point3d = THREE.Vector3;
    var Vector3d = THREE.Vector3;
    var maxWidth = 2400;
    var dVertMinWidth = 145;
    var dVertThreshold = 145;
    var singleVerticalThreshold = 155;
    var singleBackThreshold = 134;
    var sideUndercut = 125;
    var backWidth = 125;
    var mat = 18;
    var matHalf = mat / 2;
    var maxRows = 8;
    var shelfMax = 2400;
    var snapParamSide = 5;
    var factorsingle = 0.4;
    var stretch = 1.15;
    var stretchDouble = 1.15;

    var dVertRows = [1, 5, 0, 3, 5, 4, 1];
    var doubleVertLocXTemp = [375, 0, 824, 702, 1268, 1276, 1445];
    var dVertSize = [290, 400, 385, 470, 325, 375, 317];
    var dVertLeftSpeed = [0, 0, 1, 1, 1, 1, 2];
    var dVertRightSpeed = [1, 1, 1, 1, 1, 1, 2];
    var sVertLocXTemp = [305, 1105, 2058, 950, 1726, 1059, 2000, 427, 439, 1668, 470, 1849, 500, 1055, 1920];
    var sVertSpeed = [1, 1, 0, 1, 0, 3, 0, 1, 3, 0, 6, 5, 2, 1, 0];

    var wciecieBorderLeft = [1, 1, 2, 1, 2, 1, 1, 2];
    var wciecieBorderRight = [1, 1, 1, 2, 1, 1, 2, 1];
    var i = 0;
    var j = 0;
    var index1 = 0;
    var index2 = 0;
    var indexDoubleVert = 0;
    var indexDoubleVert2 = 0;
    var k = 0;
    var locali = 0;
    var shelfMaxHalf = shelfMax / 2;
    var locY = 0;
    var locX = 0;
    var locZakres = 0;
    var factordouble = 0.2;
    var pointLeft = new Point3d();
    var pointRight = new Point3d();
    var ptLoc = new Point3d();

    var indexHor = [3,7,8];
    var sVertRows = [0,0,0,2,2,3,3,4,5,5,6,6,7,7,7];
    var indexSingleVertical = [0,1,3,8,10,13];
    var sVertOrientation = [-1,1,-1,1,-1,1];
    var backRows = [0,0,2,5,6,7];
    var doubleRowLocation = [1,5,0,3,5,4,1];
    var arri = [0,1,2,3,4,5,6];
    var undercutOpening = [0,sideUndercut,0,sideUndercut,sideUndercut,0,0];

    var boolBorderRight = [];
    var singleVertExists = [];
    var singleBackExists = [];
    var locXLeftArr = [];
    var locXRightArr = [];
    var zakresy = [];
    var sVertAdd = [];
    var dVertLeftBase = [];
    var dVertRightBase = [];
    var doubleVertLeftALL = [];
    var doubleVertRightALL = [];
    var ptLeftTemp = [];
    var ptRightTemp = [];
    var doubleBackBottomCURRENT = [];
    var doubleBackTopCURRENT = [];

    var doubleVertLeftCURRENTTop = [];
    var doubleVertLeftCURRENTBottom = [];
    var doubleVertRightCURRENTTop = [];
    var doubleVertRightCURRENTBottom = [];
    var doubleVertExists = [];
    var doubleVertClosed = [];
    var sideRightBool = [];
    var basePointHorLeft = [];
    var basePointHorRight = [];
    var basePointHorLeftFull = [];
    var basePointHorRightFull = [];
    var ptSideLeftBot = [];
    var pointSideLeftTop = [];
    var pointSideRightBottom = [];
    var ptSideRightTop = [];
    var widthBorderLeft = [];
    var heightBorderLeft = [];
    var widthBorderRight = [];
    var heightBorderRight = [];
    var widthMiddle = [];
    var heightMiddle = [];
    var supportsLeft = [];
    var supportsRight = [];
    var ptSupportsLeft = [];
    var ptSupportsRight = [];
    var pointHorizontalLeft = [];
    var pointHorizontalRight = [];
    var pointBorderLeftBottom = [];
    var pointBorderLeftTop = [];
    var pointBorderRightBottom = [];
    var pointBorderRightTop = [];
    var singlePointBackBottom = [];
    var singlePointBackTop = [];
    var pointSingleBottom = [];
    var pointSingleTop = [];
    var pointSingleBottomCURRENT = [];
    var pointSingleTopCURRENT = [];
    var pointSingleBackBottom = [];
    var pointSingleBackTop = [];
    var singleBackBottomCURRENT = [];
    var singleBackTopCURRENT = [];
    var verticalsPoints = [];
    var verticalsPointsRow0 = [];
    var verticalsPointsRow1 = [];
    var verticalsPointsRow2 = [];
    var verticalsPointsRow3 = [];
    var verticalsPointsRow4 = [];
    var verticalsPointsRow5 = [];
    var verticalsPointsRow6 = [];
    var verticalsPointsRow7 = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];
    var shadowDoubleSize = [];
    var shadowDoubleLocation = [];
    var shadowLeftLocation = [];
    var shadowRightLocation = [];
    var shadowLeftSize = [];
    var shadowRightSize = [];
    var shadowSideLeftSize = [];
    var shadowSideLeftLocation = [];
    var shadowSideRightSize = [];
    var shadowSideRightLocation = [];
    var stretchDomain = [1.0, 1.55];
    var stretchWidth = (parseFloat(shelfWidth) - 700) / (2400 - 700) * (stretchDomain[1] - stretchDomain[0]) + stretchDomain[0];
    var stretchMotion = (parseFloat(parameter) - 100) / (0 - 100) * (0.85 - 1) + 1;
    stretchMotion = ((parseFloat(shelfWidth) - 700) / (2400 - 700) * (1 - 1.2) + (1.2)) * stretchMotion;
    stretch =  stretchWidth  * stretchMotion;
    var stretchDomainDouble = [1.0, 1.35];
    var stretchWidthDouble = (parseFloat(shelfWidth) - 700) / (2400 - 700) * (stretchDomainDouble[1] - stretchDomainDouble[0]) + stretchDomainDouble[0];
    var stretchMotionDouble = (parseFloat(parameter) - 100) / (0 - 100) * (0.85 - 1) + 1;
    stretchMotionDouble = ((parseFloat(shelfWidth) - 700) / (2400 - 700) * (1 - 1.2) + (1.2)) * stretchMotionDouble;
    stretchDouble =  stretchWidthDouble  * stretchMotionDouble;
    for (i = 0; i < sVertLocXTemp.length; i ++)
    {
        if ( sVertLocXTemp[i] != 0)
        {
            sVertLocXTemp[i] = sVertLocXTemp[i] * stretch;
        }
    }
    for (i = 0; i < doubleVertLocXTemp.length; i ++)
    {
        if ( doubleVertLocXTemp[i] != 0)
            {
                doubleVertLocXTemp[i] = doubleVertLocXTemp[i] * stretchDouble;
            }
        }
  var sVertLocX = sVertLocXTemp;
  var doubleVertLocX = doubleVertLocXTemp;
    if (parameter < (0 + snapParamSide))
    {
      parameter = 0;
    }
    if (parameter > (100 - snapParamSide))
    {
      parameter = 100;
    }
    var currentWidth = (parseInt(shelfWidth / 10)) * 10;
    var minX = -(currentWidth / 2);

    ptLoc = new Point3d(parseInt((maxWidth / 2) - (parseFloat(currentWidth) / 2)), 0, 0);
    basePointHorLeft.push(ptLoc);
    for ( i = 0; i <= maxRows - 1; i++)
    {
      locX = parseInt((parseFloat(maxWidth) / 2) - (parseFloat(currentWidth) / 2));
      locY = parseInt(ptLoc.y) + rowsH[i];
      ptLoc = new Point3d(locX, locY, 0);
      basePointHorLeft.push(ptLoc);
    }
    for ( i = 0; i <= maxRows; i++)
    {
      ptLoc = new Point3d(parseInt((parseFloat(maxWidth) / 2) + (parseFloat(currentWidth) / 2)), basePointHorLeft[i].y, 0);
      basePointHorRight.push(ptLoc);
    }
    var rowsAbsolute = [];
    for ( i = 0; i < basePointHorLeft.length; i++)
    {
      rowsAbsolute.push(parseInt(basePointHorLeft[i].y));
    }
    locX = 0;
    locY = 0;
    for (i = 0; i < wciecieBorderLeft.length; i++)
    {
      if (wciecieBorderLeft[i] == 2)
      {
        locX = parseInt(basePointHorLeft[i].x + parseFloat(sideUndercut));
      }
      else
      {
        locX = parseInt(basePointHorLeft[i].x);
      }
      locY = parseInt(basePointHorLeft[i].y + mat / 2);
      ptSideLeftBot.push(new Point3d(locX + mat / 2, locY, 0));

      locY = parseInt(basePointHorLeft[i + 1].y - mat / 2);
      pointSideLeftTop.push(new Point3d(locX + mat / 2, locY, 0));
    }
    for (i = 0; i < wciecieBorderRight.length; i++)
    {
      if (wciecieBorderRight[i] == 2)
      {
        locX = parseInt(basePointHorRight[i].x - sideUndercut);
      }
      else
      {
        locX = parseInt(basePointHorRight[i].x);
      }
      locY = parseInt(basePointHorRight[i].y + mat / 2);
      pointSideRightBottom.push(new Point3d(locX - mat / 2, locY, 0));

      locY = parseInt(basePointHorRight[i + 1].y - mat / 2);
      ptSideRightTop.push(new Point3d(locX - mat / 2, locY, 0));
    }
    locX = 0;
    locY = 0;
    for(i = 0; i <= doubleVertLocX.length; i++)
    {
      locZakres = 100 / (doubleVertLocX.length);
      zakresy.push(locZakres * i);
    }
    for (i = 0; i < dVertRows.length; i++)
    {
      var locXLeft = 0;
      var locXRight = 0;
      locXLeft = doubleVertLocX[i] + (maxWidth / 2 - currentWidth / 2);
      locXRight = locXLeft;
      if (parameter > zakresy[i + 1])
      {
        locXRight += dVertSize[i];
      }
      else if (parameter >= zakresy[i] && parameter <= zakresy[i + 1])
      {
        locXRight += parseInt(((parameter - zakresy[i]) * dVertSize[i]) / locZakres);
      }
      else
      {}
      locXLeft += dVertLeftSpeed[i] * parameter;
      locXRight += dVertRightSpeed[i] * parameter;
      locXLeftArr.push(locXLeft);
      locXRightArr.push(locXRight);
    }
    locXLeftArr[2] += parseInt(doubleVertLocX[0] * factordouble);
    locXLeftArr[4] += parseInt(doubleVertLocX[1] * factordouble);
    locXLeftArr[5] += parseInt(doubleVertLocX[1] * factordouble + doubleVertLocX[3] * factordouble + doubleVertLocX[4] * factordouble);
    locXLeftArr[6] += parseInt(doubleVertLocX[0] * factordouble + doubleVertLocX[2] * factordouble);
    locXRightArr[2] += parseInt(doubleVertLocX[0] * factordouble);
    locXRightArr[4] += parseInt(doubleVertLocX[1] * factordouble);
    locXRightArr[5] += parseInt(doubleVertLocX[1] * factordouble + doubleVertLocX[3] * factordouble + doubleVertLocX[4] * factordouble);
    locXRightArr[6] += parseInt(doubleVertLocX[0] * factordouble + doubleVertLocX[2] * factordouble);
    for (i = 0; i < dVertRows.length; i++)
    {
      if (locXRightArr[i] - locXLeftArr[i] < dVertMinWidth)
      {
        locXRightArr[i] = locXLeftArr[i];
      }
      var locvalX = 0;
      if (pointSideRightBottom[dVertRows[i]].x < pointSideRightBottom[dVertRows[i] + 1].x)
      {
        locvalX = parseFloat(pointSideRightBottom[dVertRows[i]].x);
      }
      else
      {
        locvalX = parseFloat(pointSideRightBottom[dVertRows[i] + 1].x);
      }
      if (locvalX - locXRightArr[i] <= dVertThreshold)
      {
        locXRightArr[i] = parseInt(locvalX);
      }
      if (locvalX - locXLeftArr[i] <= dVertThreshold)
      {
        locXLeftArr[i] = parseInt(locvalX);
        doubleVertExists.push(false);
      }
      else
      {
        doubleVertExists.push(true);
      }
      if (locXLeftArr[i] == locXRightArr[i])
      {
        doubleVertClosed.push(true);
      }
      else
      {
        doubleVertClosed.push(false);
      }
      dVertLeftBase.push(new Point3d(locXLeftArr[i] + mat / 2, basePointHorLeft[dVertRows[i]].y + mat / 2, 0));
      dVertRightBase.push(new Point3d(locXRightArr[i], basePointHorLeft[dVertRows[i]].y + mat / 2, 0));
    }
    for (i = 0; i < dVertLeftBase.length; i++)
    {
      if (doubleVertExists[i] == true)
      {
        doubleVertLeftALL.push(dVertLeftBase[i]);
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 1].y - mat / 2, 0));
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 1].y + mat / 2, 0));
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 2].y - mat / 2, 0));
      }
    }
    for (i = 0; i < dVertRightBase.length; i++)
    {
      if (doubleVertExists[i] == true && doubleVertClosed[i] == false)
      {
        doubleVertRightALL.push(dVertRightBase[i]);
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 1].y - mat / 2, 0));
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 1].y + mat / 2, 0));
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 2].y - mat / 2, 0));
      }
    }
    for (k = 0; k < doubleVertLeftALL.length; k += 4)
    {
      i = k / 4;
      if (doubleVertExists[i] == true)
      {
        var localH1 = parseFloat(doubleVertLeftALL[locali].y + rowsH[doubleRowLocation[i]] + rowsH[doubleRowLocation[i] + 1]);
        var localH2 = parseFloat(doubleVertLeftALL[locali].y + rowsH[doubleRowLocation[i]]);
        if (i == 1 && doubleVertClosed[i] == false && rows >= 7)
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 3]);
        }
        else if (localH1 < (basePointHorLeft[rows].y + mat))
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 1]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 2]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 3]);
        }
        else if (localH2 < (basePointHorLeft[rows].y + mat))
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 1]);
        }
        if (doubleVertClosed[i] == false)
        {
          if (i == arri[i] && doubleVertClosed[i] == false && rows == (doubleRowLocation[i] + 1))
          {
            if (doubleVertRightALL[locali].x == basePointHorLeft[0].x + currentWidth - mat / 2 - undercutOpening[i])
            {
            }
            else
            {

              ptRightTemp.push(doubleVertRightALL[locali]);
              ptRightTemp.push(doubleVertRightALL[locali + 1]);
            }
          }
          else if (i == arri[i] && doubleVertClosed[i] == false && rows >= (doubleRowLocation[i] + 2))
          {
            if (doubleVertRightALL[locali].x == basePointHorLeft[0].x + currentWidth - mat / 2 - undercutOpening[i])
            {

              ptRightTemp.push(doubleVertRightALL[locali]);
              ptRightTemp.push(doubleVertRightALL[locali + 3]);
            }
            else
            {

              ptRightTemp.push(doubleVertRightALL[locali]);
              ptRightTemp.push(doubleVertRightALL[locali + 1]);
              ptRightTemp.push(doubleVertRightALL[locali + 2]);
              ptRightTemp.push(doubleVertRightALL[locali + 3]);
            }
          }
          else if (localH1 < (basePointHorLeft[rows].y + mat))
          {
            ptRightTemp.push(doubleVertRightALL[locali]);
            ptRightTemp.push(doubleVertRightALL[locali + 1]);
            ptRightTemp.push(doubleVertRightALL[locali + 2]);
            ptRightTemp.push(doubleVertRightALL[locali + 3]);
          }
          else if (localH2 < (basePointHorLeft[rows].y + mat))
          {
            ptRightTemp.push(doubleVertRightALL[locali]);
            ptRightTemp.push(doubleVertRightALL[locali + 1]);
          }
        }
      }
      locali += 4;
    }
    var doubleBack1Exists = false;
    if (doubleVertExists[1] == true && doubleVertClosed[1] == false)
    {
      if ((locXRightArr[1] - basePointHorLeft[6].x) > dVertThreshold * 2)
      {
        if (rows >= 7)
        {
          doubleBack1Exists = true;
          doubleBackBottomCURRENT.push(new Point3d(basePointHorLeft[5].x + mat, basePointHorLeft[5].y + mat / 2, 0));
          doubleBackTopCURRENT.push(new Point3d(basePointHorLeft[5].x + mat + backWidth, basePointHorLeft[7].y - mat / 2, 0));
        }
        else if (rows == 6)
        {
          doubleBackBottomCURRENT.push(new Point3d(basePointHorLeft[5].x + mat, basePointHorLeft[5].y + mat / 2, 0));
          doubleBackTopCURRENT.push(new Point3d(basePointHorLeft[5].x + mat + backWidth, basePointHorLeft[6].y - mat / 2, 0));
        }
      }
    }
    if (doubleVertExists[4] == true && doubleVertClosed[4] == false)
    {
      if ((locXRightArr[4] - locXLeftArr[4]) > dVertThreshold * 2)
      {
        if (rows >= 7)
        {
          doubleBackBottomCURRENT.push(new Point3d(locXRightArr[4] - mat / 2 - backWidth, pointSideRightBottom[4].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locXRightArr[4] - mat / 2, pointSideRightBottom[6].y - mat, 0));
        }
        else if (rows == 6)
        {
          doubleBackBottomCURRENT.push(new Point3d(locXRightArr[4] - mat / 2 - backWidth, pointSideRightBottom[4].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locXRightArr[4] - mat / 2, pointSideRightBottom[5].y - mat, 0));
        }
      }
    }
    if (doubleVertExists[6] == true && doubleVertClosed[6] == false)
    {
      if ((locXRightArr[6] - locXLeftArr[6]) > dVertThreshold * 2)
      {
        if (rows >= 3)
        {
          doubleBackBottomCURRENT.push(new Point3d(locXRightArr[6] - mat / 2 - backWidth, pointSideRightBottom[1].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locXRightArr[6] - mat / 2, pointSideRightBottom[3].y - mat, 0));
        }
        else if (rows == 2)
        {
          doubleBackBottomCURRENT.push(new Point3d(locXRightArr[6] - mat / 2 - backWidth, pointSideRightBottom[1].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locXRightArr[6] - mat / 2, pointSideRightBottom[2].y - mat, 0));
        }
      }
    }
    if (doubleVertExists[4] == true && doubleVertClosed[4] == false)
    {
      if ((locXRightArr[4] - locXLeftArr[4]) > dVertThreshold * 2)
      {
        if (rows > 6)
        {
          doubleBackBottomCURRENT.push(new Point3d(locXRightArr[4] - mat / 2 - backWidth, pointSideRightBottom[5].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locXRightArr[4] - mat / 2, pointSideRightBottom[7].y - mat, 0));
      }
      else if (rows == 6)
          {
            doubleBackBottomCURRENT.push(new Point3d(locXRightArr[4] - mat / 2 - backWidth, pointSideRightBottom[5].y, 0));
            doubleBackTopCURRENT.push(new Point3d(locXRightArr[4] - mat / 2, pointSideRightBottom[7].y - mat, 0));
          }
    }
    }
    for (i = 0; i < ptLeftTemp.length; i += 2)
    {
      doubleVertLeftCURRENTBottom.push(new Point3d(ptLeftTemp[i].x, ptLeftTemp[i].y, ptLeftTemp[i].z));
    }
    for (i = 1; i < ptLeftTemp.length; i += 2)
    {
      doubleVertLeftCURRENTTop.push(new Point3d(ptLeftTemp[i].x, ptLeftTemp[i].y, ptLeftTemp[i].z));
    }
    for (i = 0; i < ptRightTemp.length; i += 2)
    {
      doubleVertRightCURRENTBottom.push(new Point3d(ptRightTemp[i].x, ptRightTemp[i].y, ptRightTemp[i].z));
    }
    for (i = 1; i < ptRightTemp.length; i += 2)
    {
      doubleVertRightCURRENTTop.push(new Point3d(ptRightTemp[i].x, ptRightTemp[i].y, ptRightTemp[i].z));
    }
    sVertAdd.push(sVertSpeed[0] * parameter);
    sVertAdd.push(doubleVertLocX[2] * factorsingle + sVertSpeed[1] * parameter);
    sVertAdd.push(doubleVertLocX[2] * factorsingle + sVertSpeed[2] * parameter);
    sVertAdd.push(doubleVertLocX[0] * factorsingle + sVertSpeed[3] * parameter);
    sVertAdd.push(doubleVertLocX[0] * factorsingle + doubleVertLocX[6] * factorsingle + sVertSpeed[4] * parameter);
    sVertAdd.push(doubleVertLocX[3] * factorsingle + sVertSpeed[5] * parameter);
    sVertAdd.push(doubleVertLocX[3] * factorsingle + sVertSpeed[6] * parameter);
    sVertAdd.push(sVertSpeed[7] * parameter);
    sVertAdd.push(doubleVertLocX[1] * factorsingle + sVertSpeed[8] * parameter);
    sVertAdd.push(doubleVertLocX[1] * factorsingle + doubleVertLocX[4] * factorsingle + doubleVertLocX[5] * factorsingle + sVertSpeed[9] * parameter);
    sVertAdd.push(doubleVertLocX[1] * factorsingle + sVertSpeed[10] * parameter);
    sVertAdd.push(doubleVertLocX[1] * factorsingle + doubleVertLocX[4] * factorsingle + sVertSpeed[11] * parameter);
    sVertAdd.push(sVertSpeed[12] * parameter);
    sVertAdd.push(sVertSpeed[13] * parameter);
    sVertAdd.push(sVertSpeed[14] * parameter);
    for (i = 0; i <= sVertLocX.length - 1; i++)
    {
      var locvalX = 0;
      var locvalY = 0;
      locvalX = parseInt(sVertLocX[i] + sVertAdd[i] + basePointHorLeft[0].x);
      locvalY = parseInt(basePointHorLeft[sVertRows[i]].y + mat / 2);
      pointSingleBottom.push(new Point3d(locvalX, locvalY, 0));
      locvalY = parseInt(basePointHorLeft[sVertRows[i] + 1].y - mat / 2);
      pointSingleTop.push(new Point3d(locvalX, locvalY, 0));
      singleVertExists.push(true);
    }
    for (i = 0; i <= pointSingleTop.length - 1; i++)
    {
      if (pointSingleTop[i].y > basePointHorLeft[rows].y)
      {
        singleVertExists[i] = false;
      }
      if (pointSingleTop[i].x > (pointSideRightBottom[sVertRows[i]].x - singleVerticalThreshold))
      {
        singleVertExists[i] = false;
      }
    }
    for (i = 0; i <= singleVertExists.length - 1; i++)
    {
      if (singleVertExists[i] == true)
      {
        pointSingleBottomCURRENT.push(pointSingleBottom[i]);
        pointSingleTopCURRENT.push(pointSingleTop[i]);
      }
    }
    for (i = 0; i <= indexSingleVertical.length - 1; i++)
    {
      if (sVertOrientation[i] == -1)
      {
        pointSingleBackTop.push(new Point3d(pointSingleTop[indexSingleVertical[i]].x - mat / 2, pointSingleTop[indexSingleVertical[i]].y, 0));
        pointSingleBackBottom.push(new Point3d(pointSingleBottom[indexSingleVertical[i]].x - mat / 2 - backWidth, pointSingleBottom[indexSingleVertical[i]].y, 0));
      }
      else
      {
        pointSingleBackTop.push(new Point3d(pointSingleTop[indexSingleVertical[i]].x + mat / 2 + backWidth, pointSingleTop[indexSingleVertical[i]].y, 0));
        pointSingleBackBottom.push(new Point3d(pointSingleBottom[indexSingleVertical[i]].x + mat / 2, pointSingleBottom[indexSingleVertical[i]].y, 0));
      }
      singleBackExists.push(true);
    }
    for (i = 0; i <= pointSingleBackTop.length - 1; i++)
    {
      if (pointSingleBackTop[i].y > basePointHorLeft[rows].y)
      {
        singleBackExists[i] = false;
      }
      if (pointSingleBackTop[i].x > (pointSideRightBottom[backRows[i]].x - singleBackThreshold))
      {
        singleBackExists[i] = false;
      }
    }
    for (i = 0; i <= singleBackExists.length - 1; i++)
    {
      if (singleBackExists[i] == true)
      {
        singleBackBottomCURRENT.push(pointSingleBackBottom[i]);
        singleBackTopCURRENT.push(pointSingleBackTop[i]);
      }
    }

    locX = 0;
    locY = 0;
    for (i = 0; i <= basePointHorLeft.length - 2; i++)
    {
      boolBorderRight.push(true);
    }
    pointHorizontalLeft.push(new Point3d((maxWidth / 2 - currentWidth / 2), locY, 0));
    pointHorizontalRight.push(new Point3d((maxWidth / 2 + currentWidth / 2), locY, 0));
    for (i = 0; i <= indexHor.length - 1; i++)
    {
      if (rows >= indexHor[i])
      {
        locY = parseFloat(pointSideLeftTop[indexHor[i] - 1].y + mat / 2);
        pointHorizontalLeft.push(new Point3d((maxWidth / 2 - currentWidth / 2), locY, 0));
        pointHorizontalRight.push(new Point3d((maxWidth / 2 + currentWidth / 2), locY, 0));
      }
    }

    index1 = 2;
    pointLeft = basePointHorLeft[1];
    pointRight = basePointHorRight[1];
    indexDoubleVert = 8;
    if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 1))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && indexDoubleVert <= doubleVertRightALL.length && (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[0] = false;
      boolBorderRight[1] = false;
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x < pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    index1 = 3;
    pointLeft = basePointHorLeft[4];
    pointRight = basePointHorRight[4];
    indexDoubleVert = 12;
    if (rows <= 3)
    {
    }
    else if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 4) | (doubleVertRightALL.length == indexDoubleVert))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL.length > indexDoubleVert))
    {
      if (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x - sideUndercut)
      {
        pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
        boolBorderRight[3] = false;
        boolBorderRight[4] = false;
      }
      else
      {
        pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
        pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
      }
    }
    index1 = 5;
    pointLeft = basePointHorLeft[5];
    pointRight = basePointHorRight[5];
    indexDoubleVert = 20;
    if (rows <= 4)
    {
    }
    else if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 5))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[4] = false;
      boolBorderRight[5] = false;
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x < pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }

    index1 = 0;
    index2 = 6;
    pointLeft = basePointHorLeft[2];
    pointRight = basePointHorRight[2];
    indexDoubleVert = 0;
    indexDoubleVert2 = 24;
    if (rows <= 1)
    {
    }
    else if (doubleVertExists[index1] == false | (doubleVertExists[index1] == true && doubleVertClosed[index1] == true && doubleVertExists[index2] == false) | (doubleVertClosed[index1] == true && doubleVertClosed[index2] == true) | rows == 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x == pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[1] = false;
      boolBorderRight[2] = false;
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && doubleVertRightALL[indexDoubleVert2].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert2].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && doubleVertRightALL[indexDoubleVert2].x == pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      boolBorderRight[1] = false;
      boolBorderRight[2] = false;
    }
    index1 = 1;
    index2 = 4;
    pointLeft = basePointHorLeft[6];
    pointRight = basePointHorRight[6];
    indexDoubleVert = 4;
    indexDoubleVert2 = 16;
    if (rows < 6)
    {
    }
    else if ((rows == 6) | (doubleVertExists[index1] == false) | (doubleVertExists[index1] == true && doubleVertClosed[index1] == true && doubleVertExists[index2] == false) | (doubleVertClosed[index1] == true && doubleVertClosed[index2] == true))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x == pointRight.x - mat / 2 - sideUndercut)
    {
      boolBorderRight[5] = false;
      boolBorderRight[6] = false;
    }
    else if ( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && ((pointRight.x - mat / 2 - sideUndercut) - doubleVertRightALL[indexDoubleVert2].x > 1))
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert2].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && ((pointRight.x - mat / 2 - sideUndercut) - doubleVertRightALL[indexDoubleVert2].x < 1)))
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      boolBorderRight[5] = false;
      boolBorderRight[6] = false;
    }

    for ( i = 0; i <= rows - 1; i++)
    {
      if ( i < 5 | i > 6)
      {
        if (wciecieBorderLeft[i] == 1)
        {
          pointBorderLeftBottom.push(new Point3d(basePointHorLeft[i].x + mat / 2, basePointHorLeft[i].y + mat / 2, 0));
          pointBorderLeftTop.push(new Point3d(basePointHorLeft[i].x + mat / 2, basePointHorLeft[i + 1].y - mat / 2, 0));
        }
        else
        {
          pointBorderLeftBottom.push(new Point3d(basePointHorLeft[i].x + mat / 2 + sideUndercut, basePointHorLeft[i].y + mat / 2, 0));
          pointBorderLeftTop.push(new Point3d(basePointHorLeft[i].x + mat / 2 + sideUndercut, basePointHorLeft[i + 1].y - mat / 2, 0));
        }
      }
    }
    for (i = 0; i <= rows - 1; i++)
    {
      if (boolBorderRight[i] == true)
      {
        if (wciecieBorderRight[i] == 1)
        {
          pointBorderRightBottom.push(new Point3d(basePointHorRight[i].x - mat / 2, basePointHorRight[i].y + mat / 2, 0));
          pointBorderRightTop.push(new Point3d(basePointHorRight[i].x - mat / 2, basePointHorRight[i + 1].y - mat / 2, 0));
        }
        else
        {
          pointBorderRightBottom.push(new Point3d(basePointHorRight[i].x - mat / 2 - sideUndercut, basePointHorRight[i].y + mat / 2, 0));
          pointBorderRightTop.push(new Point3d(basePointHorRight[i].x - mat / 2 - sideUndercut, basePointHorRight[i + 1].y - mat / 2, 0));
        }
      }
    }
    for (i = 0; i <= rows - 1; i++)
    {
      if(i == 1 | i == 3)
      {
        singlePointBackBottom.push(new Point3d(basePointHorLeft[i].x + mat, basePointHorLeft[i].y + mat / 2, 0));
        singlePointBackTop.push(new Point3d(basePointHorLeft[i].x + mat + backWidth, basePointHorLeft[i + 1].y - mat / 2, 0));
      }
      else if (i == 4 | i == 7)
      {
        singlePointBackBottom.push(new Point3d(basePointHorLeft[i].x + mat + sideUndercut, basePointHorLeft[i].y + mat / 2, 0));
        singlePointBackTop.push(new Point3d(basePointHorLeft[i].x + mat + backWidth + sideUndercut, basePointHorLeft[i + 1].y - mat / 2, 0));
      }
    }
    if ((doubleVertExists[6] == true) && (doubleVertClosed[6] == false) && (rows > 1))
    {
      singlePointBackBottom.push(new Point3d(doubleVertLeftALL[24].x - mat / 2 - backWidth, doubleVertLeftALL[24].y, 0));
      singlePointBackTop.push(new Point3d(doubleVertLeftALL[24].x - mat / 2, doubleVertLeftALL[24 + 1].y, 0));
    }
    if ((doubleVertExists[5] == true) && (doubleVertClosed[5] == false) && (rows > 5))
    {
      singlePointBackBottom.push(new Point3d(doubleVertLeftALL[22].x - mat / 2 - backWidth, doubleVertLeftALL[22].y, 0));
      singlePointBackTop.push(new Point3d(doubleVertLeftALL[22].x - mat / 2, doubleVertLeftALL[22 + 1].y, 0));
    }
    if ((doubleVertExists[0] == true) && (doubleVertClosed[0] == false) && (rows > 2))
    {
      if ((doubleVertLeftALL[2].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold))
      {
        singlePointBackBottom.push(new Point3d(doubleVertLeftALL[2].x - mat / 2 - backWidth, doubleVertLeftALL[2].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertLeftALL[2].x - mat / 2, doubleVertLeftALL[2 + 1].y, 0));
      }
    }
    else if (doubleVertExists[0] == true && doubleVertClosed[0] == true && rows > 2)
    {
      if ((doubleVertLeftALL[2].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold))
      {
        singlePointBackBottom.push(new Point3d(doubleVertLeftALL[2].x + mat / 2 + backWidth, doubleVertLeftALL[2].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertLeftALL[2].x + mat / 2, doubleVertLeftALL[2 + 1].y, 0));
      }
    }
    if (doubleVertExists[4] == true && doubleVertClosed[4] == false && rows > 6)
    {
      if((doubleVertRightALL[18].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold - sideUndercut))
      {
        singlePointBackBottom.push(new Point3d(doubleVertRightALL[18].x + mat / 2 + backWidth, doubleVertRightALL[18].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertRightALL[18].x + mat / 2, doubleVertRightALL[18 + 1].y, 0));
      }
      else if (doubleVertExists[4] == true && doubleVertClosed[4] == true && rows > 6)
      {
        if ((doubleVertLeftALL[18].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold - sideUndercut))
        {
          singlePointBackBottom.push(new Point3d(doubleVertLeftALL[18].x + mat / 2 + backWidth, doubleVertLeftALL[18].y, 0));
          singlePointBackTop.push(new Point3d(doubleVertLeftALL[18].x + mat / 2, doubleVertLeftALL[18 + 1].y, 0));
        }
      }
    }

    var ptHorizontalsAll = [];
    for (i = 0; i < pointHorizontalLeft.length; i++)
    {
      ptHorizontalsAll.push(new Point3d(pointHorizontalLeft[i].x - shelfMaxHalf, pointHorizontalLeft[i].y, pointHorizontalLeft[i].z));
      ptHorizontalsAll.push(new Point3d(pointHorizontalRight[i].x - shelfMaxHalf, pointHorizontalRight[i].y, pointHorizontalRight[i].z));
    }
    var supportsShift = 2;
    var ptSupportsAll = [];
    for ( i = 0; i < singlePointBackTop.length; i++)
    {
      ptSupportsAll.push(new Point3d(singlePointBackTop[i].x - shelfMaxHalf, singlePointBackTop[i].y, supportsShift));
      ptSupportsAll.push(new Point3d(singlePointBackBottom[i].x - shelfMaxHalf, singlePointBackBottom[i].y, supportsShift));
    }
    for ( i = 0; i < singleBackTopCURRENT.length; i++)
    {
      ptSupportsAll.push(new Point3d(singleBackTopCURRENT[i].x - shelfMaxHalf, singleBackTopCURRENT[i].y, supportsShift));
      ptSupportsAll.push(new Point3d(singleBackBottomCURRENT[i].x - shelfMaxHalf, singleBackBottomCURRENT[i].y, supportsShift));
    }
    for ( i = 0; i < doubleBackTopCURRENT.length; i++)
    {
      ptSupportsAll.push(new Point3d(doubleBackTopCURRENT[i].x - shelfMaxHalf, doubleBackTopCURRENT[i].y, supportsShift));
      ptSupportsAll.push(new Point3d(doubleBackBottomCURRENT[i].x - shelfMaxHalf, doubleBackBottomCURRENT[i].y, supportsShift));
    }

    var ptVerticalsAll = [];
    for ( i = 0; i < pointSingleTopCURRENT.length; i++)
    {
      ptVerticalsAll.push(new Point3d(pointSingleTopCURRENT[i].x - shelfMaxHalf, pointSingleTopCURRENT[i].y, pointSingleTopCURRENT[i].z));
      ptVerticalsAll.push(new Point3d(pointSingleBottomCURRENT[i].x - shelfMaxHalf, pointSingleBottomCURRENT[i].y, pointSingleBottomCURRENT[i].z));
    }
    for ( i = 0; i < pointBorderLeftTop.length; i++)
    {
      ptVerticalsAll.push(new Point3d(pointBorderLeftTop[i].x - shelfMaxHalf, pointBorderLeftTop[i].y, pointBorderLeftTop[i].z));
      ptVerticalsAll.push(new Point3d(pointBorderLeftBottom[i].x - shelfMaxHalf, pointBorderLeftBottom[i].y, pointBorderLeftBottom[i].z));
    }
    for ( i = 0; i < pointBorderRightTop.length; i++)
    {
      ptVerticalsAll.push(new Point3d(pointBorderRightTop[i].x - shelfMaxHalf, pointBorderRightTop[i].y, pointBorderRightTop[i].z));
      ptVerticalsAll.push(new Point3d(pointBorderRightBottom[i].x - shelfMaxHalf, pointBorderRightBottom[i].y, pointBorderRightBottom[i].z));
    }
    for ( i = 0; i < doubleVertLeftCURRENTTop.length; i++)
    {
      ptVerticalsAll.push(new Point3d(doubleVertLeftCURRENTTop[i].x - shelfMaxHalf, doubleVertLeftCURRENTTop[i].y, doubleVertLeftCURRENTTop[i].z));
      ptVerticalsAll.push(new Point3d(doubleVertLeftCURRENTBottom[i].x - shelfMaxHalf, doubleVertLeftCURRENTBottom[i].y, doubleVertLeftCURRENTBottom[i].z));
    }
    for ( i = 0; i < doubleVertRightCURRENTTop.length; i++)
    {
      ptVerticalsAll.push(new Point3d(doubleVertRightCURRENTTop[i].x - shelfMaxHalf, doubleVertRightCURRENTTop[i].y, doubleVertRightCURRENTTop[i].z));
      ptVerticalsAll.push(new Point3d(doubleVertRightCURRENTBottom[i].x - shelfMaxHalf, doubleVertRightCURRENTBottom[i].y, doubleVertRightCURRENTBottom[i].z));
    }

    locX = 0;
    locY = 0;
    for (i = 0; i < pointBorderLeftBottom.length; i++)
    {
      if ( i < rows && (pointBorderLeftBottom[i].x == pointBorderLeftBottom[0].x + sideUndercut))
      {
        widthBorderLeft.push(sideUndercut);
        heightBorderLeft.push(parseInt(pointBorderLeftTop[i].y - pointBorderLeftBottom[i].y));
        locX = parseInt(pointBorderLeftBottom[i].x - sideUndercut / 2 - mat / 2);
        locY = parseInt(pointBorderLeftTop[i].y - (pointBorderLeftTop[i].y - pointBorderLeftBottom[i].y) / 2);
      }
    }
    for (  i = 0; i < pointBorderRightBottom.length; i++ )
    {
      if ( pointBorderRightBottom[i].x == pointBorderRightBottom[0].x - sideUndercut )
      {
        widthBorderRight.push(sideUndercut);
        heightBorderRight.push(parseInt(pointBorderRightTop[i].y - pointBorderRightBottom[i].y));
        locX = parseInt(pointBorderRightBottom[i].x + sideUndercut / 2 + mat / 2);
        locY = parseInt(pointBorderRightTop[i].y - (pointBorderRightTop[i].y - pointBorderRightBottom[i].y) / 2);
      }
    }
    for (var i = 0, point; (point = pointBorderLeftBottom[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = pointSingleBottomCURRENT[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = pointBorderRightBottom[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = doubleVertLeftALL[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = doubleVertRightALL[i]); i++)
      verticalsPoints.push(point);
    var verticalsPointsClear = verticalsPoints._distinctPoints();
    verticalsPointsClear.sort(function(a,b) { return a.x - b.x });;

    for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
    {
      if ( parseInt(point.y) == rowsAbsolute[0] + mat / 2)
      {
        verticalsPointsRow0.push(point);
      }
    }
    if ( rows > 1 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[1] + mat / 2)
        {
          verticalsPointsRow1.push(point);
        }
      }
    }
    if ( rows > 2 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[2] + mat / 2)
        {
          verticalsPointsRow2.push(point);
        }
      }
    }
    if ( rows > 3 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[3] + mat / 2)
          verticalsPointsRow3.push(point);
      }
    }
    if ( rows > 4 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[4] + mat / 2)
          verticalsPointsRow4.push(point);
      }
    }
    if ( rows > 5 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[5] + mat / 2)
        {
          verticalsPointsRow5.push(point);
        }
      }
    }
    if ( rows > 6 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[6] + mat / 2)
        {
          verticalsPointsRow6.push(point);
        }
      }
    }
    var levelsAll = [];
    var levelsUnique = [];
    var shadowsLevels = [];
    var horiLevels = [];
    var vertLevels = [];
    var shadows = [];

    for (i = 0; i < ptHorizontalsAll.length; i++)
    {
        levelsAll.push(ptHorizontalsAll[i].y);
    }
    for (i = 0; i < levelsAll.length; i++)
    {
if (!(levelsUnique.indexOf(levelsAll[i]) > -1))
        {
            levelsUnique.push(levelsAll[i]);
        }
    }

levelsUnique.sort(function(a,b) { return a-b });
    for (i = 0; i < levelsUnique.length - 1; i++)
    {
        var temp = [];
        for (j = 0; j < ptVerticalsAll.length; j++)
        {
                if ((ptVerticalsAll[j].y - mat / 2) == levelsUnique[i])
                {
                        temp.push(ptVerticalsAll[j - 1]);
                        temp.push(ptVerticalsAll[j]);
                }
        }
        vertLevels.push(temp);
    }
    for ( i = 0; i < levelsUnique.length; i++)
    {
        var temp = [];
        for (j = 0; j < ptHorizontalsAll.length; j++)
        {
                if ((ptHorizontalsAll[j].y) == levelsUnique[i])
                {
                        temp.push(ptHorizontalsAll[j]);
                }
        }
        horiLevels.push(temp);
    }
    for (j = 0; j < vertLevels.length; j++)
    {
        vertLevels[j].sort(function(a,b) {return a.x - b.x});
        for (i = 0; i < vertLevels[j].length/2; i++)
        {
                if (vertLevels[j][i * 2].y > vertLevels[j][i * 2 + 1].y)
                {
                        var tmp = vertLevels[j][i * 2];
                        vertLevels[j][i * 2] = vertLevels[j][i * 2 + 1];
                        vertLevels[j][i * 2 + 1] = tmp;
                }
        }
    }
    for (j = 0; j < vertLevels.length; j++)
    {
        var temp = [];
        for (i = 0; i < vertLevels[j].length/2 - 1; i++)
        {
                temp.push(vertLevels[j][i * 2]);
                temp.push(vertLevels[j][i * 2 + 3]);
                if (vertLevels[j][i * 2 + 1].y > vertLevels[j][i * 2 + 3].y)
                {
                        temp[i * 2 + 1] = new Point3d(temp[i * 2 + 1].x, vertLevels[j][i * 2 + 1].y, shelfDepth / 2 );
                }
        }
        shadowsLevels.push(temp);
    }
    for (j = 0; j < horiLevels.length; j++)
    {
        if (horiLevels[j].length > 2)
        {
            for(i = 0; i < shadowsLevels[j - 1].length / 2 - 1; i++)
            {
                if ((shadowsLevels[j - 1][i * 2 + 1].x - mat / 2 == horiLevels[j][2].x))
                {
                    shadowsLevels[j - 1][i * 2 + 1] = new Point3d(shadowsLevels[j - 1][i * 2 + 1].x, horiLevels[j + 1][0].y - mat / 2, shelfDepth / 2);
                    for (k =0; k < shadowsLevels[j].length; k++)
                    {
                        if ( shadowsLevels[j][k].x == shadowsLevels[j - 1][i * 2 + 1].x)
                        {
                            shadowsLevels[j].splice(k,1);
                            shadowsLevels[j].splice(k - 1,1);
                        }
                    }
                }
            }
        }
    }
    for (j = 0; j < shadowsLevels.length; j++)
    {
        for (i = 0; i < shadowsLevels[j].length/2; i++)
        {
                shadowsLevels[j][i * 2] = new Point3d(shadowsLevels[j][i * 2].x + mat / 2, shadowsLevels[j][i * 2].y, shelfDepth / 2);
                shadowsLevels[j][i * 2 + 1] = new Point3d(shadowsLevels[j][i * 2 + 1].x - mat / 2, shadowsLevels[j][i * 2 + 1].y, shelfDepth / 2);
        }
    }
    for (j = 0; j < shadowsLevels.length; j++)
    {
        for (i = 0; i < shadowsLevels[j].length / 2; i++)
        {
            shadowMiddleLocation.push(new Point3d((shadowsLevels[j][i * 2].x + shadowsLevels[j][i * 2 + 1].x) / 2, (shadowsLevels[j][i * 2 + 1].y + shadowsLevels[j][i * 2].y) / 2, shelfDepth / 2));
            shadowMiddleSize.push(new Point3d(Math.abs(shadowsLevels[j][i * 2].x - shadowsLevels[j][i * 2 + 1].x), Math.abs(shadowsLevels[j][i * 2 + 1].y - shadowsLevels[j][i * 2].y), shelfDepth));
        }
    }
    for (j = 0; j < shadowsLevels.length; j++)
    {
        if ((Math.abs(shadowsLevels[j][0].x) + 19 < Math.abs(horiLevels[0][0].x)) && ((Math.abs(shadowsLevels[j][0].x) > Math.abs(horiLevels[0][0].x) - (backWidth + mat + 2))))
        {

            shadowsLevels[j].splice(0, 0, new Point3d(horiLevels[0][0].x, shadowsLevels[j][0].y, shelfDepth / 2));
            shadowsLevels[j].splice(1, 0, new Point3d(shadowsLevels[j][1].x - mat, shadowsLevels[j][2].y, shelfDepth / 2));
            shadowLeftLocation.push(new Point3d((shadowsLevels[j][0].x + shadowsLevels[j][1].x) / 2, (shadowsLevels[j][0].y + shadowsLevels[j][1].y) / 2, shelfDepth / 2));
            shadowLeftSize.push(new Point3d(Math.abs(shadowsLevels[j][0].x - shadowsLevels[j][1].x), Math.abs(shadowsLevels[j][1].y - shadowsLevels[j][0].y), shelfDepth));
        }
    }
    for (j = 0; j < shadowsLevels.length; j++)
    {
        if ((Math.abs(shadowsLevels[j][shadowsLevels[j].length - 1].x) + mat + 1 < Math.abs(horiLevels[0][1].x)) && ((Math.abs(shadowsLevels[j][shadowsLevels[j].length - 1].x) > Math.abs(horiLevels[0][1].x) - (backWidth + mat + 2))))
        {
            shadowsLevels[j].push(new Point3d(shadowsLevels[j][shadowsLevels[j].length - 1].x + mat, shadowsLevels[j][shadowsLevels[j].length - 2].y, shelfDepth / 2));
            shadowsLevels[j].push(new Point3d(horiLevels[0][1].x, shadowsLevels[j][shadowsLevels[j].length - 2].y, shelfDepth / 2));
            shadowRightLocation.push(new Point3d((shadowsLevels[j][shadowsLevels[j].length - 2].x + shadowsLevels[j][shadowsLevels[j].length - 1].x) / 2, (shadowsLevels[j][shadowsLevels[j].length - 2].y + shadowsLevels[j][shadowsLevels[j].length - 1].y) / 2, shelfDepth / 2));
            shadowRightSize.push(new Point3d(Math.abs(shadowsLevels[j][shadowsLevels[j].length - 2].x - shadowsLevels[j][shadowsLevels[j].length - 1].x), Math.abs(shadowsLevels[j][shadowsLevels[j].length - 1].y - shadowsLevels[j][shadowsLevels[j].length - 2].y), shelfDepth));
        }
    }
    for (j = 0; j < vertLevels.length; j++)
    {
        for (i = 0; i < vertLevels[j].length / 2; i ++)
        {
            if (vertLevels[j][i * 2].x - mat / 2 == horiLevels[0][0].x)
            {
                shadowSideLeftLocation.push(new Point3d(horiLevels[0][0].x, (vertLevels[j][i * 2 + 1].y + vertLevels[j][i * 2].y) / 2, shelfDepth / 2));
                shadowSideLeftSize.push(new Point3d(1, Math.abs(vertLevels[j][i * 2 + 1].y - vertLevels[j][i * 2].y), shelfDepth));
            }
        }
    }
    var supportsAdditionalBottom = [];
    var supportsAdditionalTop = [];
    var point10Exists = false;
    var point10 = new Point3d((pointSingleTop[indexSingleVertical[4]].x - mat / 2), (pointSingleTop[indexSingleVertical[4]].y), 0);
    for ( i = 0; i < singleBackTopCURRENT.length; i++ ){
      if ( singleBackTopCURRENT[i].x - point10.x == 0){
        point10Exists = true;
      }
    }
    if (rows > 6 && point10Exists == false && doubleBack1Exists == false){
      ptLoc = new Point3d(verticalsPointsRow6[verticalsPointsRow6.length - 1].x - matHalf - backWidth - shelfMaxHalf, verticalsPointsRow6[verticalsPointsRow6.length - 1].y, supportsShift);
      ptSupportsAll.push(ptLoc);
      ptLoc = new Point3d(verticalsPointsRow6[verticalsPointsRow6.length - 1].x - matHalf - shelfMaxHalf, verticalsPointsRow6[verticalsPointsRow6.length - 1].y + rowsH[6] - mat, supportsShift);
      ptSupportsAll.push(ptLoc);
    }

    var point8Exists = false;
    var point8 = new Point3d((pointSingleTop[indexSingleVertical[3]].x + matHalf + backWidth), (pointSingleTop[indexSingleVertical[3]].y), 0);
    for ( i = 0; i < singleBackTopCURRENT.length; i++ ){
      if ( singleBackTopCURRENT[i].x - point8.x == 0){
        point8Exists = true;
      }
    }
    if (rows > 5 && point8Exists == false && doubleBack1Exists == false){
      if(singleVertExists[8] == true){
        ptLoc = new Point3d(verticalsPointsRow5[verticalsPointsRow5.length - 2].x - matHalf - backWidth - shelfMaxHalf, verticalsPointsRow5[verticalsPointsRow5.length - 2].y, supportsShift);
        ptSupportsAll.push(ptLoc);
        ptLoc = new Point3d(verticalsPointsRow5[verticalsPointsRow5.length - 2].x - matHalf - shelfMaxHalf, verticalsPointsRow5[verticalsPointsRow5.length - 2].y + rowsH[5] - mat, supportsShift);
        ptSupportsAll.push(ptLoc);
      }
      else{
        ptLoc = new Point3d(verticalsPointsRow5[verticalsPointsRow5.length - 1].x - matHalf - backWidth - shelfMaxHalf, verticalsPointsRow5[verticalsPointsRow5.length - 1].y, supportsShift);
        ptSupportsAll.push(ptLoc);
        ptLoc = new Point3d(verticalsPointsRow5[verticalsPointsRow5.length - 1].x - matHalf - shelfMaxHalf, verticalsPointsRow5[verticalsPointsRow5.length - 1].y + rowsH[5] - mat, supportsShift);
        ptSupportsAll.push(ptLoc);
      }
    }
    if (verticalsPointsRow0.length >= 5 && doubleVertClosed[2] == false)
    {
        if (currentWidth - Math.abs(verticalsPointsRow0[3].x) > singleBackThreshold / 2)
        {
            if (Math.abs(verticalsPointsRow0[3].x) < shelfWidth * 10 - 9)
            {
                ptSupportsAll.push(new Point3d(verticalsPointsRow0[3].x + matHalf - shelfMaxHalf + backWidth, rowsH[0] - matHalf, supportsShift));
                ptSupportsAll.push(new Point3d(verticalsPointsRow0[3].x + matHalf - shelfMaxHalf, verticalsPointsRow0[3].y, supportsShift));
            }
            else{
            }
        }
    }
    var ptSupportAdded1Top = new Point3d();
    var ptSupportAdded1Bottom = new Point3d();
    if (verticalsPointsRow1.length == 6 && rows > 1 && verticalsPointsRow1[verticalsPointsRow1.length - 1].x - verticalsPointsRow1[verticalsPointsRow1.length - 2].x > singleBackThreshold * 2)
    {
            if (Math.abs(verticalsPointsRow1[verticalsPointsRow1.length - 1].x) < shelfWidth * 10 - 9)
            {
                ptSupportAdded1Top = new Point3d(verticalsPointsRow1[verticalsPointsRow1.length - 1].x - matHalf - shelfMaxHalf,verticalsPointsRow1[verticalsPointsRow1.length - 1].y + rowsH[1] - mat, supportsShift);
                ptSupportAdded1Bottom = new Point3d(verticalsPointsRow1[verticalsPointsRow1.length - 1].x - matHalf - backWidth - shelfMaxHalf, verticalsPointsRow1[verticalsPointsRow1.length - 1].y, supportsShift);
                ptSupportsLeft.push(ptSupportAdded1Top);
                ptSupportsLeft.push(ptSupportAdded1Bottom);

            }
            else{
            }
    }
    var ptSupportAdded2Top = new Point3d();
    var ptSupportAdded2Bottom = new Point3d();
    if (verticalsPointsRow2.length == 4 && rows > 2 && verticalsPointsRow2[verticalsPointsRow2.length - 1].x - verticalsPointsRow2[verticalsPointsRow2.length - 2].x > singleBackThreshold * 2 && singleVertExists[3] == false)
    {
            if (Math.abs(verticalsPointsRow2[verticalsPointsRow2.length - 1].x) < shelfWidth * 10 - 9)
            {
                ptSupportAdded2Top = new Point3d(verticalsPointsRow2[verticalsPointsRow2.length - 1].x - matHalf - shelfMaxHalf,verticalsPointsRow2[verticalsPointsRow2.length - 1].y + rowsH[2] - mat, supportsShift);
                ptSupportAdded2Bottom = new Point3d(verticalsPointsRow2[verticalsPointsRow2.length - 1].x - matHalf - backWidth - shelfMaxHalf, verticalsPointsRow2[verticalsPointsRow2.length - 1].y, supportsShift);
                ptSupportsLeft.push(ptSupportAdded2Top);
                ptSupportsLeft.push(ptSupportAdded2Bottom);
            }
            else{
            }
    }
    var distanceX = 0;
    var distanceY = 0;
    var ptSupportCenter = new Point3d();
    var ptVerticalCenter = new Point3d();
    for (i = 0; i < ptSupportsAll.length; i += 2)
    {
      ptSupportCenter = new Point3d((ptSupportsAll[i].x + ptSupportsAll[i + 1].x) / 2, (ptSupportsAll[i].y + ptSupportsAll[i + 1].y) / 2, 0);
      for ( j = 0; j < ptVerticalsAll.length; j += 2)
      {
        ptVerticalCenter = new Point3d(ptVerticalsAll[j].x, (ptVerticalsAll[j].y + ptVerticalsAll[j + 1].y) / 2, 0);
        distanceX = parseInt(ptVerticalCenter.x - ptSupportCenter.x);
        distanceY = parseInt(ptVerticalCenter.y - ptSupportCenter.y);
        if (distanceX > 0 && distanceX <= (backWidth / 2 + matHalf + 1) && distanceY == 0)
        {
          supportsLeft.push(i);
          ptSupportsLeft.push(ptSupportsAll[i]);
          ptSupportsLeft.push(ptSupportsAll[i + 1]);
        }
        else if (distanceX < 0 && distanceX >= -(backWidth / 2 + matHalf + 1) && distanceY == 0)
        {
          supportsRight.push(i);
          ptSupportsRight.push(ptSupportsAll[i]);
          ptSupportsRight.push(ptSupportsAll[i + 1]);
        }
      }
    }


    var legsOffset = 20;
    var legsCount = 0;
    var ptLegsTemp = new Point3d(0, 0, 0);
    if ((parseInt(Math.abs(ptHorizontalsAll[1].x - ptHorizontalsAll[0].x))) <= 900)
      legsCount = 2;
    else if ((parseInt(Math.abs(ptHorizontalsAll[1].x - ptHorizontalsAll[0].x))) <= 1500)
      legsCount = 3;
    else
      legsCount = 4;
    var ptLegs = [];
    for (i = 0; i < legsCount; i++){
      ptLegsTemp = new Point3d(((shelfWidth - legsOffset * 2) / (legsCount - 1)) * i, 0, legsOffset);
      ptLegs.push(ptLegsTemp);
      ptLegsTemp = new Point3d(((shelfWidth - legsOffset * 2) / (legsCount - 1)) * i, 0, shelfDepth - legsOffset);
      ptLegs.push(ptLegsTemp);
    }
    for (i = 0; i < ptLegs.length; i++){
      ptLegs[i] = new Point3d(ptLegs[i].x - shelfWidth / 2 + legsOffset, ptLegs[i].y, ptLegs[i].z);
    }
    var shadowLeftLocationOut = shadowLeftLocation;
    var shadowLeftSizeOut = shadowLeftSize;
    var shadowRightLocationOut = shadowRightLocation;
    var shadowRightSizeOut = shadowRightSize;
    var shadowMiddleLocationOut = shadowMiddleLocation;
    var shadowMiddleSizeOut = shadowMiddleSize;
    var shadowSideLeftLocationOut = shadowSideLeftLocation;
    var shadowSideLeftSizeOut = shadowSideLeftSize;
    var pointHorizontalsAllOut = ptHorizontalsAll;
    var pointVerticalsAllOut = ptVerticalsAll;
    var ptSupportsLeftOut = ptSupportsLeft;
    var ptSupportsRightOut = ptSupportsRight;
    var ptLegsOut = ptLegs;
	return {
		ptHorizontalsAll: ptHorizontalsAll,
		ptVerticalsAll: ptVerticalsAll,
		ptSupportsLeft: ptSupportsLeft,
		ptSupportsRight: ptSupportsRight,
		shadowLeftLocation: shadowLeftLocation,
		shadowLeftSize: shadowLeftSize,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize,
		shadowRightLocation: shadowRightLocation,
		shadowRightSize: shadowRightSize,
		ptLegs: ptLegs
	};
}};