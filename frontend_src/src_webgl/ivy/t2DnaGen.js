// change to show console logs

const HEIGHT_MAP = {
    "1268": {"Rzedy": [198, 198, 198, 198, 198, 278], "Ilosc": 6},
    "2184": {"Rzedy": [198, 198, 198, 198, 198, 398, 398, 398], "Ilosc": 8},
    "3020": {"Rzedy": [198, 198, 198, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 10},
    "1546": {"Rzedy": [198, 198, 198, 198, 198, 278, 278], "Ilosc": 7},
    "1948": {"Rzedy": [198, 278, 278, 398, 398, 398], "Ilosc": 6},
    "1942": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278, 278], "Ilosc": 9},
    "1946": {"Rzedy": [198, 198, 198, 278, 278, 398, 398], "Ilosc": 7},
    "2268": {"Rzedy": [278, 398, 398, 398, 398, 398], "Ilosc": 6},
    "1548": {"Rzedy": [198, 198, 198, 278, 278, 398], "Ilosc": 6},
    "1468": {"Rzedy": [198, 198, 198, 198, 278, 398], "Ilosc": 6},
    "1466": {"Rzedy": [198, 198, 198, 198, 198, 198, 278], "Ilosc": 7},
    "2464": {"Rzedy": [198, 198, 198, 278, 398, 398, 398, 398], "Ilosc": 8},
    "994": {"Rzedy": [198, 398, 398], "Ilosc": 3},
    "2466": {"Rzedy": [198, 278, 398, 398, 398, 398, 398], "Ilosc": 7},
    "2460": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278, 398, 398], "Ilosc": 10},
    "990": {"Rzedy": [198, 198, 198, 198, 198], "Ilosc": 5},
    "2462": {"Rzedy": [198, 198, 198, 198, 198, 278, 398, 398, 398], "Ilosc": 9},
    "992": {"Rzedy": [198, 198, 198, 398], "Ilosc": 4},
    "2704": {"Rzedy": [278, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 8},
    "1790": {"Rzedy": [198, 398, 398, 398, 398], "Ilosc": 5},
    "2700": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 278, 398, 398], "Ilosc": 10},
    "2702": {"Rzedy": [198, 198, 278, 278, 278, 278, 398, 398, 398], "Ilosc": 9},
    "1828": {"Rzedy": [198, 278, 278, 278, 398, 398], "Ilosc": 6},
    "1706": {"Rzedy": [198, 198, 198, 278, 278, 278, 278], "Ilosc": 7},
    "1708": {"Rzedy": [198, 278, 278, 278, 278, 398], "Ilosc": 6},
    "1824": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 278], "Ilosc": 8},
    "1826": {"Rzedy": [198, 198, 198, 278, 278, 278, 398], "Ilosc": 7},
    "2068": {"Rzedy": [198, 278, 398, 398, 398, 398], "Ilosc": 6},
    "2146": {"Rzedy": [198, 198, 278, 278, 398, 398, 398], "Ilosc": 7},
    "2144": {"Rzedy": [198, 198, 198, 198, 278, 278, 398, 398], "Ilosc": 8},
    "1388": {"Rzedy": [198, 198, 198, 198, 198, 398], "Ilosc": 6},
    "2142": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278, 398], "Ilosc": 9},
    "1668": {"Rzedy": [198, 198, 198, 278, 398, 398], "Ilosc": 6},
    "2060": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 198, 278], "Ilosc": 10},
    "1666": {"Rzedy": [198, 198, 198, 198, 198, 278, 398], "Ilosc": 7},
    "2062": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278, 398], "Ilosc": 9},
    "1664": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278], "Ilosc": 8},
    "2064": {"Rzedy": [198, 198, 198, 198, 198, 278, 398, 398], "Ilosc": 8},
    "2066": {"Rzedy": [198, 198, 198, 278, 398, 398, 398], "Ilosc": 7},
    "2148": {"Rzedy": [278, 278, 398, 398, 398, 398], "Ilosc": 6},
    "594": {"Ilosc": 3, "Rzedy": [190, 198, 198]},
    "596": {"Rzedy": [198, 398], "Ilosc": 2},
    "3140": {"Rzedy": [198, 198, 198, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3142": {"Rzedy": [198, 278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "278": {"Ilosc": 1, "Rzedy": [270]},
    "2820": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 398, 398, 398], "Ilosc": 10},
    "2822": {"Rzedy": [198, 198, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 9},
    "2542": {"Rzedy": [198, 198, 198, 198, 278, 278, 398, 398, 398], "Ilosc": 9},
    "2540": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278, 398, 398], "Ilosc": 10},
    "3382": {"Rzedy": [198, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2546": {"Rzedy": [278, 278, 398, 398, 398, 398, 398], "Ilosc": 7},
    "834": {"Rzedy": [278, 278, 278], "Ilosc": 3},
    "2544": {"Rzedy": [198, 198, 278, 278, 398, 398, 398, 398], "Ilosc": 8},
    "2982": {"Rzedy": [198, 198, 198, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2980": {"Rzedy": [198, 198, 198, 198, 198, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3022": {"Rzedy": [198, 278, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2984": {"Rzedy": [198, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2666": {"Rzedy": [278, 398, 398, 398, 398, 398, 398], "Ilosc": 7},
    "2380": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 398, 398], "Ilosc": 10},
    "2664": {"Rzedy": [198, 198, 278, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2382": {"Rzedy": [198, 198, 198, 198, 198, 198, 398, 398, 398], "Ilosc": 9},
    "2662": {"Rzedy": [198, 198, 198, 198, 278, 398, 398, 398, 398], "Ilosc": 9},
    "2384": {"Rzedy": [198, 198, 198, 198, 398, 398, 398, 398], "Ilosc": 8},
    "2660": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 398, 398, 398], "Ilosc": 10},
    "2386": {"Rzedy": [198, 198, 398, 398, 398, 398, 398], "Ilosc": 7},
    "2262": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 398, 398], "Ilosc": 9},
    "2388": {"Rzedy": [398, 398, 398, 398, 398, 398], "Ilosc": 6},
    "2260": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 278, 398], "Ilosc": 10},
    "2266": {"Rzedy": [198, 198, 278, 398, 398, 398, 398], "Ilosc": 7},
    "2264": {"Rzedy": [198, 198, 198, 198, 278, 398, 398, 398], "Ilosc": 8},
    "3580": {"Rzedy": [198, 198, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3582": {"Rzedy": [398, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1230": {"Rzedy": [198, 198, 278, 278, 278], "Ilosc": 5},
    "1232": {"Rzedy": [278, 278, 278, 398], "Ilosc": 4},
    "2106": {"Rzedy": [198, 278, 278, 278, 278, 398, 398], "Ilosc": 7},
    "1944": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 398], "Ilosc": 8},
    "1908": {"Rzedy": [278, 278, 278, 278, 398, 398], "Ilosc": 6},
    "1906": {"Rzedy": [198, 198, 278, 278, 278, 278, 398], "Ilosc": 7},
    "1904": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 278], "Ilosc": 8},
    "1588": {"Rzedy": [198, 198, 198, 198, 398, 398], "Ilosc": 6},
    "1428": {"Rzedy": [198, 198, 198, 278, 278, 278], "Ilosc": 6},
    "1586": {"Rzedy": [198, 198, 198, 198, 198, 198, 398], "Ilosc": 7},
    "1584": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198], "Ilosc": 8},
    "1032": {"Rzedy": [198, 278, 278, 278], "Ilosc": 4},
    "2586": {"Rzedy": [198, 398, 398, 398, 398, 398, 398], "Ilosc": 7},
    "2584": {"Rzedy": [198, 198, 198, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2582": {"Rzedy": [198, 198, 198, 198, 198, 398, 398, 398, 398], "Ilosc": 9},
    "2580": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 398, 398, 398], "Ilosc": 10},
    "2420": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278, 278, 398], "Ilosc": 10},
    "1352": {"Rzedy": [278, 278, 398, 398], "Ilosc": 4},
    "2422": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 398, 398], "Ilosc": 9},
    "1350": {"Rzedy": [198, 198, 278, 278, 398], "Ilosc": 5},
    "2424": {"Rzedy": [198, 198, 278, 278, 278, 398, 398, 398], "Ilosc": 8},
    "2426": {"Rzedy": [278, 278, 278, 398, 398, 398, 398], "Ilosc": 7},
    "1862": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 278], "Ilosc": 9},
    "1864": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 398], "Ilosc": 8},
    "1866": {"Rzedy": [198, 198, 198, 198, 278, 398, 398], "Ilosc": 7},
    "1868": {"Rzedy": [198, 198, 278, 398, 398, 398], "Ilosc": 6},
    "1990": {"Rzedy": [398, 398, 398, 398, 398], "Ilosc": 5},
    "1626": {"Rzedy": [198, 198, 198, 198, 278, 278, 278], "Ilosc": 7},
    "1628": {"Rzedy": [198, 198, 278, 278, 278, 398], "Ilosc": 6},
    "2102": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 278, 278], "Ilosc": 9},
    "1510": {"Rzedy": [278, 278, 278, 278, 398], "Ilosc": 5},
    "2104": {"Rzedy": [198, 198, 198, 278, 278, 278, 278, 398], "Ilosc": 8},
    "2860": {"Rzedy": [198, 198, 198, 198, 198, 278, 398, 398, 398, 398], "Ilosc": 10},
    "2862": {"Rzedy": [198, 198, 198, 278, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2864": {"Rzedy": [198, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "556": {"Ilosc": 2, "Rzedy": [270, 278]},
    "3062": {"Rzedy": [198, 198, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "3060": {"Rzedy": [198, 198, 198, 198, 278, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3064": {"Rzedy": [278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2944": {"Rzedy": [278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2942": {"Rzedy": [198, 198, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2940": {"Rzedy": [198, 198, 198, 198, 278, 278, 398, 398, 398, 398], "Ilosc": 10},
    "2344": {"Rzedy": [198, 198, 198, 278, 278, 398, 398, 398], "Ilosc": 8},
    "2346": {"Rzedy": [198, 278, 278, 398, 398, 398, 398], "Ilosc": 7},
    "2340": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278, 278, 398], "Ilosc": 10},
    "2342": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 398, 398], "Ilosc": 9},
    "2226": {"Rzedy": [198, 278, 278, 278, 398, 398, 398], "Ilosc": 7},
    "1190": {"Rzedy": [198, 198, 198, 198, 398], "Ilosc": 5},
    "874": {"Rzedy": [198, 278, 398], "Ilosc": 3},
    "1192": {"Rzedy": [198, 198, 398, 398], "Ilosc": 4},
    "2222": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 278, 398], "Ilosc": 9},
    "1194": {"Rzedy": [398, 398, 398], "Ilosc": 3},
    "2220": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 278, 278, 278], "Ilosc": 10},
    "3184": {"Rzedy": [398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "2740": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 398, 398, 398], "Ilosc": 10},
    "2742": {"Rzedy": [198, 198, 198, 278, 278, 398, 398, 398, 398], "Ilosc": 9},
    "3180": {"Rzedy": [198, 198, 198, 198, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "2744": {"Rzedy": [198, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 8},
    "3182": {"Rzedy": [198, 198, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "3860": {"Rzedy": [278, 398, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "1750": {"Rzedy": [278, 278, 398, 398, 398], "Ilosc": 5},
    "1270": {"Rzedy": [198, 198, 198, 278, 398], "Ilosc": 5},
    "1272": {"Rzedy": [198, 278, 398, 398], "Ilosc": 4},
    "3540": {"Rzedy": [198, 278, 278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3220": {"Rzedy": [198, 198, 278, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3222": {"Rzedy": [278, 278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1472": {"Rzedy": [278, 398, 398, 398], "Ilosc": 4},
    "3342": {"Rzedy": [278, 278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1470": {"Rzedy": [198, 198, 278, 398, 398], "Ilosc": 5},
    "3980": {"Rzedy": [398, 398, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "2224": {"Rzedy": [198, 198, 198, 278, 278, 278, 398, 398], "Ilosc": 8},
    "1788": {"Rzedy": [198, 198, 198, 398, 398, 398], "Ilosc": 6},
    "872": {"Rzedy": [198, 198, 198, 278], "Ilosc": 4},
    "1782": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 198], "Ilosc": 9},
    "1784": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 398], "Ilosc": 8},
    "1786": {"Rzedy": [198, 198, 198, 198, 198, 398, 398], "Ilosc": 7},
    "1074": {"Rzedy": [278, 398, 398], "Ilosc": 3},
    "1072": {"Rzedy": [198, 198, 278, 398], "Ilosc": 4},
    "1070": {"Rzedy": [198, 198, 198, 198, 278], "Ilosc": 5},
    "1670": {"Rzedy": [198, 278, 398, 398, 398], "Ilosc": 5},
    "674": {"Rzedy": [198, 198, 278], "Ilosc": 3},
    "676": {"Rzedy": [278, 398], "Ilosc": 2},
    "1150": {"Rzedy": [198, 198, 198, 278, 278], "Ilosc": 5},
    "1152": {"Rzedy": [198, 278, 278, 398], "Ilosc": 4},
    "1550": {"Rzedy": [198, 278, 278, 398, 398], "Ilosc": 5},
    "2902": {"Rzedy": [198, 278, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 9},
    "2900": {"Rzedy": [198, 198, 198, 278, 278, 278, 278, 398, 398, 398], "Ilosc": 10},
    "396": {"Ilosc": 2, "Rzedy": [190, 198]},
    "398": {"Ilosc": 1, "Rzedy": [390]},
    "3300": {"Rzedy": [198, 278, 278, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 10},
    "796": {"Rzedy": [398, 398], "Ilosc": 2},
    "794": {"Rzedy": [198, 198, 398], "Ilosc": 3},
    "792": {"Rzedy": [198, 198, 198, 198], "Ilosc": 4},
    "2782": {"Rzedy": [198, 198, 198, 198, 398, 398, 398, 398, 398], "Ilosc": 9},
    "2300": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278, 278, 278], "Ilosc": 10},
    "2302": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 278, 398], "Ilosc": 9},
    "2304": {"Rzedy": [198, 198, 278, 278, 278, 278, 398, 398], "Ilosc": 8},
    "2306": {"Rzedy": [278, 278, 278, 278, 398, 398, 398], "Ilosc": 7},
    "1392": {"Rzedy": [198, 398, 398, 398], "Ilosc": 4},
    "1390": {"Rzedy": [198, 198, 198, 398, 398], "Ilosc": 5},
    "1986": {"Rzedy": [198, 198, 198, 198, 398, 398, 398], "Ilosc": 7},
    "3660": {"Rzedy": [198, 278, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "2140": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 278, 278], "Ilosc": 10},
    "3740": {"Rzedy": [278, 278, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3260": {"Rzedy": [198, 198, 198, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3262": {"Rzedy": [198, 278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1386": {"Rzedy": [198, 198, 198, 198, 198, 198, 198], "Ilosc": 7},
    "3462": {"Rzedy": [278, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1430": {"Rzedy": [198, 278, 278, 278, 398], "Ilosc": 5},
    "3460": {"Rzedy": [198, 198, 278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3500": {"Rzedy": [278, 278, 278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "1310": {"Rzedy": [198, 278, 278, 278, 278], "Ilosc": 5},
    "3340": {"Rzedy": [198, 198, 278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "1348": {"Rzedy": [198, 198, 198, 198, 278, 278], "Ilosc": 6},
    "1870": {"Rzedy": [278, 398, 398, 398, 398], "Ilosc": 5},
    "198": {"Ilosc": 1, "Rzedy": [190]},
    "1630": {"Rzedy": [278, 278, 278, 398, 398], "Ilosc": 5},
    "1984": {"Rzedy": [198, 198, 198, 198, 198, 198, 398, 398], "Ilosc": 8},
    "1982": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 398], "Ilosc": 9},
    "1980": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 198, 198], "Ilosc": 10},
    "1988": {"Rzedy": [198, 198, 398, 398, 398, 398], "Ilosc": 6},
    "1508": {"Rzedy": [198, 198, 278, 278, 278, 278], "Ilosc": 6},
    "2622": {"Rzedy": [198, 198, 198, 278, 278, 278, 398, 398, 398], "Ilosc": 9},
    "2620": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 278, 398, 398], "Ilosc": 10},
    "2624": {"Rzedy": [198, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 8},
    "754": {"Rzedy": [198, 278, 278], "Ilosc": 3},
    "2182": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 398, 398], "Ilosc": 9},
    "2180": {"Rzedy": [198, 198, 198, 198, 198, 198, 198, 198, 198, 398], "Ilosc": 10},
    "2186": {"Rzedy": [198, 198, 198, 398, 398, 398, 398], "Ilosc": 7},
    "1592": {"Rzedy": [398, 398, 398, 398], "Ilosc": 4},
    "2188": {"Rzedy": [198, 398, 398, 398, 398, 398], "Ilosc": 6},
    "1188": {"Rzedy": [198, 198, 198, 198, 198, 198], "Ilosc": 6},
    "2024": {"Rzedy": [198, 198, 198, 198, 278, 278, 278, 398], "Ilosc": 8},
    "2026": {"Rzedy": [198, 198, 278, 278, 278, 398, 398], "Ilosc": 7},
    "1748": {"Rzedy": [198, 198, 278, 278, 398, 398], "Ilosc": 6},
    "2022": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278, 278], "Ilosc": 9},
    "1744": {"Rzedy": [198, 198, 198, 198, 198, 198, 278, 278], "Ilosc": 8},
    "1746": {"Rzedy": [198, 198, 198, 198, 278, 278, 398], "Ilosc": 7},
    "2028": {"Rzedy": [278, 278, 278, 398, 398, 398], "Ilosc": 6},
    "2824": {"Rzedy": [278, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 8},
    "952": {"Rzedy": [198, 198, 278, 278], "Ilosc": 4},
    "3100": {"Rzedy": [198, 198, 278, 278, 278, 278, 398, 398, 398, 398], "Ilosc": 10},
    "954": {"Rzedy": [278, 278, 398], "Ilosc": 3},
    "3102": {"Rzedy": [278, 278, 278, 278, 398, 398, 398, 398, 398], "Ilosc": 9},
    "1112": {"Rzedy": [278, 278, 278, 278], "Ilosc": 4},
    "2504": {"Rzedy": [198, 278, 278, 278, 278, 398, 398, 398], "Ilosc": 8},
    "2502": {"Rzedy": [198, 198, 198, 278, 278, 278, 278, 398, 398], "Ilosc": 9},
    "2500": {"Rzedy": [198, 198, 198, 198, 198, 278, 278, 278, 278, 398], "Ilosc": 10},
    "2784": {"Rzedy": [198, 198, 398, 398, 398, 398, 398, 398], "Ilosc": 8},
    "3620": {"Rzedy": [278, 278, 278, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "2786": {"Rzedy": [398, 398, 398, 398, 398, 398, 398], "Ilosc": 7},
    "2780": {"Rzedy": [198, 198, 198, 198, 198, 198, 398, 398, 398, 398], "Ilosc": 10},
    "3780": {"Rzedy": [198, 398, 398, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3380": {"Rzedy": [198, 198, 198, 398, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "3420": {"Rzedy": [198, 278, 278, 278, 398, 398, 398, 398, 398, 398], "Ilosc": 10},
    "476": {"Ilosc": 2, "Rzedy": [190, 278]},
    "1590": {"Rzedy": [198, 198, 398, 398, 398], "Ilosc": 5}
}


const DEBUG = false;

const MODIFIERS = [
    {
        'dimension': 'width',
        'min': 700,
        'max': 4500,
        'minimal_change': 10,
        'type': 'size',
    },
    {
        'dimension': 'motion',
        'min': 0,
        'max': 100,
        'minimal_change': 10,
        'type': 'style',
    },
    {
        'dimension': 'height',
        'min': 1,
        'max': 10,
        'minimal_change': 1,
        'type': 'size',
    },
    {
        'dimension': 'pattern',
        'min': 0,
        'max': 3,
        'custom_function': function(value) {return [0,1,2,3].filter(x=>x!==value)[getRandomInt(0,2)]},
        'type': 'style',
    },
    {
        'dimension': 'color',
        'min': 0,
        'max': 3,
        'custom_function': function(value) {return [0,1,3].filter(x=>x!==value)[getRandomInt(0,1)]},
        'type': 'style',
    },
    {
        'dimension': 'row_styles',
        'min': 0,
        'max': 0,
        'custom_function': function(value) {
            return _.range(10).map(x=>getRandomInt(1,3));
        },
        'type': 'style',
    },
    {
        'dimension': 'row_height',
        'min': 0,
        'max': 0,
        'custom_function': function(value) {
            return _.range(10).map(x=> { return [208, 278, 398][getRandomInt(0,2)]});
        },
        'type': 'row_sizes',
    }

];

const SIZE_MODIFIERS = MODIFIERS.filter(x=>x['type']==='size');

const STYLE_MODIFIERS = MODIFIERS.filter(x=>x['type']==='style');

let self = null;
class DnaGenerator {
    constructor(){
        self = this;
        this.latestChanges = [];
        
        this.shelfParameters = [];

        this.tmpChanges = [];

        let debounced_screenshots = _.debounce(this.createItems, 500);
        // TODO: setcolor, set row heights, set row styles
        // TODO: actually lets use last actions
        PubSub.subscribe('inputChanged', (msg, data) => {
            if (data.prop !== 'camera'){
                self.tmpChanges.push(data);
            }
            if (data.prop === 'camera' || data.prop === 'pattern') {
                self.latestChanges.push(self.tmpChanges[self.tmpChanges.length-1]);
                self.tmpChanges = [];
            }

        });
        PubSub.subscribe('shelfDataChanged', (msg, data) => {
          self.shelfParameters = data;
          debounced_screenshots();
        });

    }

    createItems(){
        //window.ivyScreenshot.gridGenerator(self.getShelves(4, ...self.shelfParameters), self.shelfParameters[4]);
    }

    addAction(interaction){
        this.latestChanges.push(interaction);
    }

    getShelf(pattern, motion, width, height, color, row_height, row_styles, random_modifiers, exclude_modifiers){
        // spread for copy arrays
        let response = {'pattern': pattern, "motion": motion, "width": width, "height": height, "color": color, "row_height": [...row_height], "row_styles":[...row_styles]};

        let actual_modifiers = [];
        if (exclude_modifiers !== undefined && exclude_modifiers.length > 0) {
            actual_modifiers = MODIFIERS.filter(x=> exclude_modifiers.indexOf(x['dimension']) === -1);
        } else {
            actual_modifiers = MODIFIERS;
        }
        //lets modify the initial shelf x times, where x is 5
        for (let i=0; i<random_modifiers; i++){
          // here it should filter by last Y actions. 1-2?
          let random_modifier = actual_modifiers[getRandomInt(0,actual_modifiers.length-1)];

          response[random_modifier['dimension']] = useModificator(response[random_modifier['dimension']], random_modifier);
        }
        return response;
    }
    
    getShelves(how_many, pattern, motion, width, height, color, row_height, row_styles) {
        if (window.location.href.indexOf("modelrandom") > -1){
          return this.getShelvesModelRandom(how_many, pattern, motion, width, height, color, row_height, row_styles);
        } else {
            let resp = this.getShelvesModelModel1(how_many, pattern, motion, width, height, color, row_height, row_styles);
            return resp;
        }
    }


    getShelvesModelRandom(how_many, pattern, motion, width, height, color, row_height, row_styles){
      let initial_shelf = {'pattern': pattern, "motion": motion, "width": width, "height": height, "color": color, "row_height": [...row_height], "row_styles":[...row_styles]};

        // here option to evaluate what would be the best x to show, require to get distance on shelf vectors
        let response = [];
        let sorted_response = [];
        // lets create 5x how_many shelfs, by random.
        let excluded_entries = []; // ['width','height']

        ['width','height'].forEach(x=>{
           if (this.latestChanges.map(change=>change['prop']).indexOf(x) > -1) {
               excluded_entries.push(x);
           }
        });
        for (let i=0; i< how_many*5; i++){
          response.push(this.getShelf(pattern, motion, width, height, color, row_height, row_styles, 5, excluded_entries));
        }

        // lets choose the most diffrent ones, for each initial and each chosen.
        while (sorted_response.length < how_many){
          let max_distance = -1;
          let best_shelf;
          for (let i=0; i< response.length; i++){
            let tmp_distance = shelvesDistance(initial_shelf, response[i]);
            for (let j=0; j< sorted_response.length; j++){
              tmp_distance += shelvesDistance(sorted_response[j], response[i])
            }
            if (tmp_distance > max_distance){
              max_distance = tmp_distance;
              best_shelf = response[i];
            }
          }
          DEBUG && console.log("Max distance in this loop: ", max_distance, " checked shelves: ", sorted_response.length);
          sorted_response.push(best_shelf);
        }

        sorted_response = sorted_response.sort((a,b)=> { return getHeight(a) !== getHeight(b) ? getHeight(a) - getHeight(b) : a['width'] - b['width']});
        return sorted_response;
    }

    getShelvesModelModel1(how_many, pattern, motion, width, height, color, row_height, row_styles){
        // tutaj w jednej z szafek robimy zmiane większą na width/height , na pozostalych 3 zmiany male na width/height
        // dodatkowo dorzucamy po 2 zmiany na pozostałym parametrze
        // wersja bez pamieci
        let large_change_items = 2;
        let large_change_ratio_max = 0.6;
        let large_change_ratio_min = 0.2;

        let style_changes = 3;

        let height_margin = 250;


        //let initial_shelf = {'pattern': pattern, "motion": motion, "width": width, "height": height, "color": color, "row_height": [...row_height], "row_styles":[...row_styles]};

        // here option to evaluate what would be the best x to show, require to get distance on shelf vectors
        let response = [];
        let sorted_response = [];
        // lets create how_many shelves, just copies
        for (let i=0; i< how_many; i++){
            response.push(this.getShelf(pattern, motion, width, height, color, [...row_height], [...row_styles], 0));
        }
        // large change
        for (let i=0; i<= large_change_items; i++) {
            let random_modifier = SIZE_MODIFIERS[getRandomInt(0,SIZE_MODIFIERS.length-1)];
            let variation = Math.round(((random_modifier['max'] - random_modifier['min'])* large_change_ratio_max));
            let variation_min = Math.round(((random_modifier['max'] - random_modifier['min'])* large_change_ratio_min));
            let min = Math.max(random_modifier['min'], response[i][random_modifier['dimension']] - variation)/random_modifier['minimal_change'];
            let max = Math.min(random_modifier['max'], response[i][random_modifier['dimension']] + variation)/random_modifier['minimal_change'];
            // minimal change required?
            let value_to_change = getRandomInt(min,max) * Math.round(random_modifier['minimal_change']);
            let retry_tries = 5;
            while (Math.abs(value_to_change - response[i][random_modifier['dimension']]) < variation_min && retry_tries > 0){
                value_to_change = getRandomInt(min,max) * Math.round(random_modifier['minimal_change']);
                retry_tries--;
            }
            response[i][random_modifier['dimension']] = value_to_change;

            // UGLY UGLY HACK
            let current_height = getHeight(response[i]);
            let possible_heights = Object.keys(HEIGHT_MAP).map(x=>parseInt(x));
            let min_max_height = possible_heights.filter(x=> current_height - height_margin <= x && x <= current_height + height_margin);
            //lets choose one

            let chosen_heights = HEIGHT_MAP[min_max_height[getRandomInt(0,min_max_height.length-1)]];
            response[i]['height'] = chosen_heights['Ilosc'];
            response[i]['row_height'] = _.shuffle(chosen_heights['Rzedy']);

        }
        // style changes

        for (let i=0; i< response.length; i++) {
            for (let j=0; j < style_changes;j++){
                let random_modifier = STYLE_MODIFIERS[getRandomInt(0,STYLE_MODIFIERS.length -1)];
                response[i][random_modifier['dimension']] = useModificator(response[i][random_modifier['dimension']], random_modifier);
          }
        }
        // lets choose the most diffrent ones, for each initial and each chosen.


        // sorting - height, then width
        sorted_response = response.sort((a,b)=> { return getHeight(a) !== getHeight(b) ? getHeight(a) - getHeight(b) : a['width'] - b['width']});
        return sorted_response;
    }
}

function useModificator(actual_value, random_modifier){
    if (random_modifier['custom_function'] !== undefined) {
        return random_modifier['custom_function'](actual_value);
      } else {
        let min = Math.round(random_modifier['min']/random_modifier['minimal_change']);
        let max = Math.round(random_modifier['max']/random_modifier['minimal_change']);
        return getRandomInt(min,max) * random_modifier['minimal_change'];
      }
}

function shelvesDistance (shelf1, shelf2) {
  return distance(shelfToArray(shelf1),shelfToArray(shelf2));
}

function getHeight(item){
    if (item['height'] < 1 || item['row_height'].length == []) {
        return 0;
    }
    return item['row_height'].slice(0,item['height']).reduce((sum, x) => sum + x);
}

function shelfToArray (shelf) {
  let shelf_vector = [];
  // for patternt and color we should use diffrent normalize function
  [0,1,2,3,4].forEach(entry=>
      shelf_vector.push(normalize(shelf[MODIFIERS[entry]['dimension']], MODIFIERS[entry]['max'], MODIFIERS[entry]['min'] ))
  );
  return shelf_vector;
}

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function map(arg, from1, to1, from2, to2) {
    return (arg - from1) / (to1 - from1) * (to2 - from2) + from2
}

function normalize(val, max, min) {
    return (val - min) / (max - min);
}

function distance( x, y) {
	let len,
		val,
		abs,
		t,
		s,
		r,
		i;
	len = x.length;
	if ( len !== y.length ) {
		throw new Error( 'euclidean()::invalid input arguments. Input arrays must have the same length.' );
	}
	if ( !len ) {
		return null;
	}
	t = 0;
	s = 1;
	for ( i = 0; i < len; i++ ) {
		val = x[ i ] - y[ i ];
		abs = ( val < 0 ) ? -val : val;
		if ( abs > 0 ) {
			if ( abs > t ) {
				r = t / val;
				s = 1 + s*r*r;
				t = abs;
			} else {
				r = val / t;
				s += r*r;
			}
		}

	}
	return t * Math.sqrt( s );
}



module.exports = DnaGenerator;