const MAX_IDENTS = 42;

import regl from 'regl';
import quad from 'glsl-quad';

import { Vector3, Matrix4 } from 'three';

import shadowsRaymarcher from 'inline:./../build/shaders/flat/shelf.flat.glsl';
import shadowsRaymarcherFast from 'inline:./../build/shaders/flat/shelf.flat.fast.glsl';

import shadowsRaymarcherCasterOtho from 'inline:./../build/shaders/flat/shelf.vert.ortho.glsl';
import shadowsRaymarcherCasterOthoTop from 'inline:./../build/shaders/flat/shelf.vert.ortho.top.glsl';

var inverseProjectionMatrix = new Matrix4();
var worldToLocal = new Matrix4();

var emptyM4 = new Matrix4();
var mappedProjection = new Matrix4();

// Implement multiple commands for one render pass i.e. fast-blur  https://github.com/jwerle/regl-combine
// Add "shadow-right" and other elements from Ivy render -> proxy gemoetry


export function softShadowsTexturePass(
    renderer,
    canvasElement,
    isTopProjection
) {

    let cameraObject = renderer.camera;
    let build3d = renderer.geo;

    let blend = { 
        blend: {
          /*  enable: false,
            func: {
                srcRGB: 'one',
                srcAlpha: 'zero',
                dstRGB: 'one minus src alpha',
                dstAlpha: 'one minus src alpha'
            }*/
            enable: true,
            func: {
                srcRGB: 'src alpha',
                srcAlpha: 0,
                dstRGB: 'one minus src alpha',
                dstAlpha: 1
            },
            equation: {
                rgb: 'add',
                alpha: 'add'
            },
            color: [0, 0, 0, 0]
        }
    };

    const selfSize = () => { 
        return [ 
            renderer.ivy.width/2, 
            renderer.ivy.getHeight()/2, 320/2 
        ];
    }

    var rl = regl(canvasElement);

    var uniformsIdents = {};

    for(let j=0; j < MAX_IDENTS; j++) {
        uniformsIdents[`idents[${j}]`] = rl.prop(`idents${j}`);
    }

    var uniforms = { 

        // Tylko Renderer

        cameraPosition: rl.prop('cameraPosition'),

        shelfGap: rl.prop('shelfGap'),        
        shelfSize: rl.prop('shelfSize'),
        ...uniformsIdents,

        shadowSource: rl.prop('shadowSource'),
        shadowPower: rl.prop('shadowPower'),

        warp: rl.prop('warp')
    };

     const castFlatShadows = rl({

        frag: isTopProjection?
                shadowsRaymarcherFast :
                shadowsRaymarcher,

        vert: isTopProjection? 
                shadowsRaymarcherCasterOthoTop : 
                shadowsRaymarcherCasterOtho,

        attributes: {
            position: rl.buffer(quad.verts),
            uvs: rl.buffer(quad.uvs)
        },

        uniforms: uniforms,
        count: 6,

        ...blend
    });

    const render = ({
        settings
    }) => {

        rl.clear({
            depth: 1,
            color: [0, 0, 0, 1]
        });

        let idents = build3d.getIndents(selfSize());
        let parsedIdents = {};

        for(let v=0; v < MAX_IDENTS; v++) {
            if(idents[v]) {
                parsedIdents[`idents${v}`] = idents[v];
            } else {
                parsedIdents[`idents${v}`] = [0,0,0,0];
            }
        }
    
        castFlatShadows({ 
            
            shelfSize: selfSize(),

            shadowPower: settings.power,
            shelfGap: settings.shelfGap,

            cameraPosition: [ 0, 0, settings.cameraz * 3],
            
            shadowSource: [ settings.x, settings.y, settings.z, 0],
            warp: [ settings.warpx, settings.warpy ],

            ...parsedIdents
        });
    };

    const updateIndents = () => false;
    const updateSize = () => false;
    
    return { render, updateIndents, updateSize }
};