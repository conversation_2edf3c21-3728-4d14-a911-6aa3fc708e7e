const calculateBaseMargin = (price, coefs) => price * coefs.jetty_margin_base;

const calculateAdditionalMargin = (data, coefs) => {
    const area = (data.width * data.height / 1000 / 1000);
    const minArea = coefs.minimal_front_area_for_increase;
    const maxArea = coefs.maximal_front_area_for_increase;

    const areaRatio = (maxArea - area) / (maxArea - minArea);
    const areaRatioNormalized = Math.max(Math.min(areaRatio, 1), 0);

    return areaRatioNormalized * coefs.maximal_increase;
};

export const calculateMarginsPrice = (data, price, coefs) => {
    const marginsPrice = {
        marginBase: calculateBaseMargin(price, coefs),
        marginAdditional: calculateAdditionalMargin(data, coefs),
    };
    return Object.values(marginsPrice).reduce((total, item) => total + item, 0);
};

export const calculateRegionalIncrease = (price, coefs) => {
    const percentage_increase = coefs.type_03_additional_increase * price;
    return coefs.regional_increase + percentage_increase;
};

const calculateWattyDepthPrice = (depth, price, coefs) => {
    const depthM = depth / 1000;
    let depthPrice = (depthM - 0.53) * coefs.watty_base_depth_factor;
    if (depthM < 0.53) {
      depthPrice += (0.52 - depthM) * coefs.watty_depth_400_factor;
    } else if (depthM > 0.63) {
      depthPrice += (depthM - 0.63) * coefs.watty_depth_800_factor;
    }
    return depthPrice * price;
};

const calculateWattyMaterialPrice = (material, price, coefs) => {
    // stupid condition that will blow up in our faces one day :shrug:
    if (material >= 3) return coefs.duotone_factor * price;
    return 0;
}

export const calculateWattyMarginsPrice = (data, price, lightingPrice, coefs) => {

    const depthPrice = calculateWattyDepthPrice(data.depth, price - lightingPrice, coefs);
    const materialPrice = calculateWattyMaterialPrice(data.material, price - lightingPrice, coefs);
    return (price + depthPrice + materialPrice) * coefs.watty_margin_base;
};
