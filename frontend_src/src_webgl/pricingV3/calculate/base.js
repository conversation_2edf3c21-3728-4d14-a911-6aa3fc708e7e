const calculateFrontArea = data => {
    const width = data.width / 1000;
    const height = data.height / 1000;
    return width * height;
};

export const calculateBasePrice = (data, coefs) => {
    const frontArea = calculateFrontArea(data);
    return frontArea * coefs.base;
};

export const calculateWattyBasePrice = (data, coefs) => {
    const width = data.width / 1000;
    return coefs.watty_base_width * width + coefs.watty_base_unit;
};

export const calculateLogisticsPrice = (data, coefs) => {
    const frontArea = calculateFrontArea(data);
    return frontArea * coefs.logistic_front_area
        + data.drawers.length * coefs.logistic_drawers
        + coefs.logistic_base;
};

export const calculateWattyLogisticsPrice = 0;
