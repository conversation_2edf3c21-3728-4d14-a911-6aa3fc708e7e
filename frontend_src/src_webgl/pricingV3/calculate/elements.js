import { removeOverlappingElements } from '@src_webgl/pricingV3/helpers/geometryProcessing';

const MAX_DOOR_HEIGHT_FOR_TREX = 382;

export const computeLength = (element, axis) => Math.abs(element[`${axis}2`] - element[`${axis}1`]) / 1000;

export const sumLengths = (elements, axis) => elements.reduce((total, elem) => total + computeLength(elem, axis), 0);

const calculateHorizontalsPrice = (horizontals, multiplier, unit, axis) => {
    if (!horizontals) return 0;
    const lengths = sumLengths(horizontals, axis);
    const count = horizontals.length + horizontals.reduce((total, elem) => {
        const elemLength = computeLength(elem, axis);
        const countMod = elemLength >= 2.3 ? (elemLength - 2.3) / 0.1 : 0;
        return total + countMod;
    }, 0);
    const countNormalized = Math.round((count + Number.EPSILON) * 100) / 100;
    return lengths * multiplier + countNormalized * unit;
};

export const calculateLengthPrice = (elements, multiplier, unit, axis) => {
    if (!elements) return 0;
    const lengths = sumLengths(elements, axis);
    const count = elements.length;
    return lengths * multiplier + count * unit;
};

const calculateAreaPrice = (elements, multiplier, unit) => elements.reduce((total, elem) => {
    if (!elements) return 0;
    const width = computeLength(elem, 'x');
    const height = computeLength(elem, 'y');
    const perimeter = 2 * (width + height);
    return total + (perimeter * multiplier + unit);
}, 0);

const calculateDoorPrice = (elements, multiplier, unit, factor) => {
    if (!elements) return 0;
    let price = 0;
    const max_door_height = 0.382;
    elements.forEach(elem => {
        const width = computeLength(elem, 'x');
        const height = computeLength(elem, 'y');
        const perimeter = 2 * (width + height);
        const doorPrice = (perimeter * multiplier + unit) * (height > MAX_DOOR_HEIGHT_FOR_TREX ? factor : 1);
        const doorMod = height > max_door_height ? factor : 1;
        price += doorPrice * doorMod;
    });
    return price;
};

const calculateByCountPrice = (elements, multiplier, factor) => (elements ? elements.length * multiplier * factor : 0);

const calculatePlinthPrice = (elements, width, multiplier, factor) => {
    if (!elements || elements.length === 0) return 0;
    return (width / 1000) * multiplier * factor;
};

const calculateInsertsPrice = (elements, multiplier, unit, factor) => {
    if (!elements) return 0;
    let price = 0;
    elements.forEach(elem => {
        const axis = elem.subtype === 'v' ? 'y' : 'x';
        const length = computeLength(elem, axis);
        price += (length * multiplier + unit);
    });
    return price * factor;
};

const calculateWallsPrice = (elements, unit) => {
    if (!elements) return 0;
    const innerWallsCount = [...new Set(elements.map(item => item.x1))].length - 2;
    return innerWallsCount * unit;
};

const calculateExtraHeightPrice = (height, width, multiplierWidth, multiplierHeight, unit) => {
    if (height < 2370) return 0;
    const widthMod = width / 1000;
    const heightMod = (height - 2000) / 1000;
    return widthMod * multiplierWidth + heightMod * multiplierHeight + unit;
};

export const calculateElementsPrice = (data, coefs) => {
    const cleanSupportElements = removeOverlappingElements(data.supports, data.backs);
    const elementsPrice = {
        horizontals: calculateHorizontalsPrice(data.horizontals, coefs.horizontal_length, coefs.horizontal_unit, 'x'),
        verticals: calculateLengthPrice(data.verticals, coefs.vertical_length, coefs.vertical_unit, 'y'),
        supports: calculateLengthPrice(cleanSupportElements, coefs.support_length, coefs.support_unit, 'y'),
        backs: calculateAreaPrice(data.backs, coefs.backs_perimeter, coefs.backs_unit),
        doors: calculateDoorPrice(data.doors, coefs.doors_perimeter, coefs.doors_unit, coefs.raptor_factor),
        drawers: calculateAreaPrice(data.drawers, coefs.drawers_perimeter, coefs.drawers_unit),
        long_legs: calculateByCountPrice(data.long_legs, coefs.long_leg_unit, coefs.raptor_factor),
        plinth: calculatePlinthPrice(data.plinth, data.width, coefs.plinth_length, coefs.raptor_factor),
        inserts: calculateInsertsPrice(data.inserts, coefs.insert_length, coefs.insert_unit, coefs.raptor_factor),
        cable_management: calculateByCountPrice(data.cable_management, coefs.cable_management_unit, coefs.raptor_factor),
        desk_beams: calculateLengthPrice(data.desk_beams, coefs.desk_beam_length, coefs.desk_beam_unit, 'x'),
    };
    return Object.values(elementsPrice).reduce((total, item) => total + item, 0);
};

export const calculateDrawersPrice = (drawersData, coefs) => {
    const externalDrawers = drawersData.filter(drawer => drawer.subtype === 'e');
    const internalDrawers = drawersData.filter(drawer => drawer.subtype === 'i' || drawer.subtype === 'b');
    const externalDrawersPrice = calculateLengthPrice(
        externalDrawers,
        coefs.watty_drawer_length,
        coefs.watty_external_drawer_unit,
        'x',
    );
    const internalDrawersPrice = calculateLengthPrice(
        internalDrawers,
        coefs.watty_drawer_length,
        coefs.watty_internal_drawer_unit,
        'x',
    );
    return externalDrawersPrice + internalDrawersPrice;
};

function calculateLightingPrice(lighting, coefs) {
  const noOfModulesWithLight = new Set(lighting.map(({ x1, x2 }) => `${x1}-${x2}`)).size;
  const modulesPrice = noOfModulesWithLight * coefs.light_module;
  const lengthPrice = calculateLengthPrice(lighting, coefs.light_length, coefs.light_unit, 'x');
  return modulesPrice + lengthPrice;
}

export const calculateWattyElementsPrice = (data, coefs) => {
    const extraHeightPrice = calculateExtraHeightPrice(
        data.height,
        data.width,
        (data.height === 2370 ? coefs.watty_additional_height_length : coefs['watty_height+_length']),
        (data.height === 2370 ? coefs.watty_additional_height_height : coefs['watty_height+_height']),
        (data.height === 2370 ? coefs.watty_additional_height_unit : coefs['watty_height+_unit']),
    );
    const regularBars = data.bars.filter(bar => bar.subtype !== 'z');
    const crossbars = data.bars.filter(bar => bar.subtype === 'z');
    const elementsPrice = {
        bars: calculateLengthPrice(regularBars, coefs.bar_length, coefs.bar_unit, 'x'),
        crossbars: calculateByCountPrice(crossbars, coefs.crossbar_unit, 1),
        slabs: calculateLengthPrice(data.slabs, coefs.slab_length, coefs.slab_unit, 'x'),
        drawers: calculateDrawersPrice(data.drawers, coefs),
        walls: calculateWallsPrice(data.walls, coefs.wall_unit),
        extra_height: extraHeightPrice,
        lighting: calculateLightingPrice(data.lighting, coefs)
    };
    return [Object.values(elementsPrice).reduce((total, item) => total + item, 0), elementsPrice.lighting];
};
