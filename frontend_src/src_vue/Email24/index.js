/* eslint-disable camelcase */

/*
 *
 * Plugins
 *
 */

import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {
  extend,
  ValidationObserver,
  ValidationProvider,
  localize,
} from 'vee-validate';
import {
  required,
  min_value,
  max_value,
  numeric,
} from 'vee-validate/dist/rules';

import en from 'vee-validate/dist/locale/en.json';
import de from 'vee-validate/dist/locale/de.json';
import fr from 'vee-validate/dist/locale/fr.json';
import es from 'vee-validate/dist/locale/es.json';
import nl from 'vee-validate/dist/locale/nl.json';


import {
  TyApiPlugin,
  TyUiPlugin,
  TyNotifyPlugin,
  TyModalPlugin,
} from '@tylko_ui/plugins/';

/*
 *
 * App
 *
 */

import vClickOutside from 'v-click-outside';
import { CustomFormatter } from '@vue-locale/core';
import App from './App';
import router from './router';
import store from './store';
import getMail24Locales from '@vue-locale/locale-loaders/getMail24Locales';

/*
 *
 * Vue Config
 *
 */

if (process.env.NODE_ENV !== 'production') {
  Vue.config.debug = true;
  Vue.config.devtools = true;
  Vue.config.performance = true;
}

/*
 *
 * External extensions
 *
 */

Vue.use(VueI18n);
Vue.use(TyApiPlugin, { store });
Vue.use(TyUiPlugin, { store });
Vue.use(vClickOutside);
Vue.use(TyNotifyPlugin, { store });
Vue.use(TyModalPlugin, {
  store,
  options: {
    container: {
      id: 'vueModalWrapper',
    },
  },
});

Vue.component('ValidationProvider', ValidationProvider);
Vue.component('ValidationObserver', ValidationObserver);

extend('required', required);
extend('numeric', numeric);
extend('min_value', min_value);
extend('max_value', max_value);

localize({
  en, de, fr, es, nl,
});
localize(window.cstm_i18n.language);

const messages = getMail24Locales();
const { language } = window.cstm_i18n;

export const i18n = new VueI18n({
  locale: language === 'no' ? 'nb' : language,
  messages,
  formatter: new CustomFormatter(language === 'no' ? 'nb' : language),
});

/*
 *
 * Create app instance
 *
 */

new Vue({
  created() {
    this.$store.dispatch('email24/FETCH_DATA', { lat: this.$route.query.lat });
  },
  router,
  store,
  i18n,
  render(h) { return h(App); },
}).$mount('#vue-email-24');
