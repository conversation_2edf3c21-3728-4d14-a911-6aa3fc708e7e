<template>
    <ValidationObserver
        ref="observer"
        v-slot="{ invalid }"
        tag="div"
    >
        <h2 class="normal-24 text-offblack-800 mb-16 lg:mb-8">
            {{ $t('mail24_survey_question_2') }}
        </h2>
        <p class="normal-14 text-grey-900 mb-24 lg:mb-32 flex">
            {{ $t('mail24_survey_question_2_body') }}
        </p>
        <div class="email-24-floors__fields">
            <TySelect
                v-model="numberOfFloors"
                class="mb-32"
                rules="required"
                :name="$t('mail24_survey_answer_2_1')"
                :options="selectOptions"
                :label="$t('mail24_survey_answer_2_1')"
                :placeholder="$t('mail24_survey_answer_2_2')"
                :transition="300"
                @input="setFloor()"
            />
            <TyInput
                v-if="checkFloor()"
                v-model="exactNumberOfFloors"
                rules="required|numeric|min_value:10|max_value:100"
                class="mb-32"
                :name="$t('mail24_survey_answer_2_3')"
                type="number"
                :placeholder="$t('mail24_survey_answer_2_4')"
                @input="setFloor()"
            >
                <template slot="label">
                    {{ $t('mail24_survey_answer_2_3') }}
                </template>
            </TyInput>
        </div>
        <div class="flex between-xs middle-xs">
            <button
                class="flickity-button previous"
                @click="decreaseStep(step = 1)"
            />
            <button
                class="btn-cta"
                :disabled="invalid"
                @click="increaseStep(step = 1)"
            >
                {{ $t('mail24_survey_next_1') }}
            </button>
        </div>
    </ValidationObserver>
</template>
<script>
    import { mapGetters } from 'vuex';
    import { TySelect, TyInput } from '@tylko_ui/components/form/';
    import get from 'lodash/get';
    import { selectFloorOptions, FLOORS } from '../../../utils/consts';

    export default ({
        components: {
            TySelect,
            TyInput,
        },
        props: {
            increaseStep: {
                type: Function,
                default: () => {},
            },
            decreaseStep: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                numberOfFloors: null,
                exactNumberOfFloors: null,
                selectOptions: selectFloorOptions(this.$t('mail24_survey_answer_2_5')),
            };
        },
        computed: {
            ...mapGetters({
                floor: 'email24/floor',
            }),
        },
        methods: {
            checkFloor() {
                return get(this.numberOfFloors, 'value', null) === FLOORS.FLOOR_MORE_THAN_9;
            },
            setFloor() {
                this.$store.commit('email24/SET_FLOOR', this.checkFloor()
                    ? this.exactNumberOfFloors
                    : get(this.numberOfFloors, 'value', null));
            },
            checkDisabled() {
                return !this.floor;
            },
        },
    });
</script>
