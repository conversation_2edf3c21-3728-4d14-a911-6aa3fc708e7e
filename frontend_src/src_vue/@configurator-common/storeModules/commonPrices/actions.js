import axios from 'axios';
import { debounce } from 'lodash';
import { initializePriceFormatter, priceFormatter } from '../../helpers/priceFormatter';

export default {
  INITIALIZE_PRICE_FORMATTER() {
    initializePriceFormatter();
  },
  UPDATE_DISCOUNTS({ commit }, payload) {
    commit('updateDiscounts', payload);
  },
  SET_REGION({ commit }, { locale, currency }) {
    commit('setLocale', locale);
    commit('setCurrency', currency);
  },
  SET_DEFAULT_PRODUCT_PRICE({ commit }, payload) {
    commit('setDefaultProductPrice', payload);
  },
  SET_PROMO_DATA({ commit }, payload) {
    commit('setPromoData', payload);
  },
  SET_OMNIBUS_PRICE({ commit }, payload) {
    commit('setOmnibusPrice', payload);
  },
  SET_SEASON_SALE({ commit }, payload) {
    commit('setSeasonSale', payload);
  },
  async FETCH_PROMOTION({ commit, state, dispatch }, {
    material, furnitureType, shelfType, furnitureCategory, region,
  }) {
    if (!([material] in state.discounts)) {
      commit('setIsFetchingPromo', true);
      try {
        const { data: { value } } = await axios({
          method: 'GET',
          url: `/api/v1/global_promo/discount/?furniture_type=${furnitureType}&material=${material}&shelf_type=${shelfType}&shelf_category=${furnitureCategory}&region=${region}`,
        });
        dispatch('UPDATE_DISCOUNTS', { [material]: value });
        commit('setPromo', value);
        commit('setIsFetchingPromo', false);
      } catch (e) {
        commit('setPromo', 0);
        commit('setIsFetchingPromo', false);
        console.error(e);
      }
    } else {
      commit('setPromo', state.discounts[material]);
    }
  },

  FETCH_OMNIBUS_PRICE: debounce(({ commit, dispatch }, {
    payload,
  }) => {
    axios.post('/api/v1/pricing/omnibus/', {
      ...payload,
    })
      .then(response => {
        const { price, currency } = response.data;

        const formattedPrice = priceFormatter ? priceFormatter.format(price) : price + currency;
        commit('setOmnibusPrice', formattedPrice);
        dispatch('commonUI/UPDATE_OMNIBUS_LOADER_STATE', false, { root: true });

        PubSub.publish('updateOmnibusPrice', formattedPrice);
      })
      .catch(error => {
        console.log(error);
        throw new Error(`Fetch omnibus price error: ${error}`);
      });
  }, 5000),
};
