<template>
  <TylkoStepper
    v-model="activeColumn"
    class="column-stepper"
    :options="options"
    customImages
  >
    <img
      svg-inline
      src="@tylko_ui/icons-cplus/chevron_down-new.svg"
    >
  </TylkoStepper>
</template>

<script>
import { TylkoStepper } from '@componentsConfigurator';

export default {
  components: {
    TylkoStepper,
  },
  props: {
    components: {
      type: Array,
      default: null,
    },
  },
  computed: {
    options() {
      return this.components.map((value, index) => ({
        value: index,
        label: index + 1,
      }));
    },
    activeColumn: {
      get() {
        return this.$store.getters.GET_ACTIVE_COMPONENT_INDEX;
      },
      set(componentIndex) {
        this.$store.dispatch(
          'ACTIVE_COMPONENT',
          this.components[componentIndex],
        );
      },
    },
  },
  watch: {
    components() {
      if (this.activeColumn >= this.components.length) {
        this.activeColumn = this.components.length - 1;
      }
    },
  },
};
</script>
