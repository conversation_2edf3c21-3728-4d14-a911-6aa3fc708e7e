<template>
  <div
    ref="priceContainer"
    class="price-container notranslate"
  >
    <template v-if="!loader && price">
      <TylkoPrice
        v-if="shouldRender"
        :promotion="promo"
        :shelfType="shelfType"
        :activeMaterial="activeMaterial"
        v-bind="{ isInterior, breakPrice }"
        :hideOmnibusPrice="hideOmnibusPrice"
      />
      <div
        v-if="promo > 0 && promoData.code && !promoData.strikethroughPricing && displayPromoWithCoupon"
        class="price-coupon__container"
      >
        <p
          v-if="priceCoupon"
          class="mb-0"
        >
          <span
            class="price-coupon"
          >{{ priceCoupon }}</span> <span class="promo-text">{{ $t('common_with_code') }}</span><span class="promo-code">{{ promoData.code }}</span> <span
            class="promo-code-icon"
            @click="copyToClipboard(promoData.code)"
          >
            <CopyToClipboardIcon />
          </span>
        </p>
        <p class="regular-price-text">
          <span v-if="isMobile">{{ $t('common_regular_price') }}</span>
          <span
            class="regular-price---small"
            data-testid="configurator-price"
          >{{ price }}</span>
          <span
            v-if="region === 'france'"
            class="vat-info normal-12 ml-4"
          >{{ $t('common_eco_tax') }}</span>
          <span
            v-if="seasonSale && seasonSale.seasonSaleOn"
            class="season-sale-banner font-semibold text-12 ml-4 px-12 py-[3px] rounded-4 text-white relative"
            :style="{
              backgroundColor: seasonSale.primaryColor,
              color: seasonSale.secondaryColor,
            }"
          >
            {{ seasonSale.pdpBadgeLabel }}
          </span>
        </p>
      </div>
      <p
        v-if="promo === 0 || !promoData.code && !promoData.value && !promoData.percentage && !promoData.endDate || !displayPromoWithCoupon && !promoData.strikethroughPricing"
        class="configurator-price"
        :data-qa-value="rawPrice"
        data-qa-id="pricingv3"
        data-testid="configurator-price"
      >
        {{ price }}
        <span class="vat-info normal-12 inline-block ml-4">
          <span
            v-if="region === 'france'"
          >{{ $t('common_eco_tax') }}
          </span>
        </span>
      </p>
    </template>
    <div
      v-else
      class="price-container--loader-wrapper"
    >
      <img
        svg-inline
        src="@tylko_ui/icons-cplus/ic_loader.svg"
      >
    </div>
  </div>
</template>

<script>
import {
  TylkoPrice,
} from '@componentsConfigurator';
import CopyToClipboardIcon from '@componentsConfigurator/CopyToClipboardIcon';

export default {
  components: {
    CopyToClipboardIcon,
    TylkoPrice,
  },
  saleEnabled: window.cstm_i18n.is_global_promo_on,
  region: window.cstm_i18n.region_name,
  props: {
    hideOmnibusPrice: {
      required: false,
      type: Boolean,
    },
    materialConfig: {
      required: true,
      type: Array,
    },
    activeMaterial: {
      required: true,
      type: Number,
    },
    furnitureType: {
      required: true,
      type: String,
    },
    furnitureCategory: {
      required: true,
      type: String,
    },
    shelfType: {
      required: true,
      type: Number,
    },
    isInterior: {
      type: Boolean,
      default: false,
    },
    breakPrice: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loader: false,
    };
  },
  computed: {
    rawPrice() {
      return this.$store.getters['commonPrices/GET_RAW_PRICE'];
    },
    seasonSale() {
      return this.$store.getters['commonPrices/GET_SEASON_SALE'];
    },
    price() {
      return this.$store.getters['commonPrices/GET_PRICE'];
    },
    promo() {
      return this.$store.getters['commonPrices/GET_PROMO'];
    },
    discounts: {
      get() {
        return this.$store.getters['commonPrices/GET_DISCOUNTS'];
      },
      set(payload) {
        this.$store.dispatch('commonPrices/UPDATE_DISCOUNTS', payload);
      },
    },
    shouldRender() {
      return this.promoData.strikethroughPricing && this.promo > 0;
    },
    promoData() {
      return this.$store.getters['commonPrices/GET_PROMO_DATA'];
    },
    priceCoupon() {
      return this.$store.getters['commonPrices/GET_PRICE_COUPON'];
    },
    region() {
      return window.cstm_i18n.region_name;
    },
    isMobile() {
      return this.$store.getters['ui/GET_IS_MOBILE'];
    },
    isMaterialGreenMoss() {
      return this.activeMaterial === 11 && this.shelfType === 0;
    },
    displayPromoWithCoupon() {
      const { maxThreshold, minThreshold } = this.promoData;
      return this.rawPrice >= minThreshold && this.rawPrice <= maxThreshold;
    },
  },
  watch: {
    activeMaterial(material) {
      if (this.$options.saleEnabled || this.promoData.code !== '') {
        this.fetchPromotion(material, this.$options.region);
      }
    },
  },
  mounted() {
    if (this.$options.saleEnabled || this.promoData.code !== '') {
      this.fetchPromotion(this.activeMaterial, this.$options.region, true);
    }
  },
  methods: {
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
      }, err => {
        console.error('Could not copy text: ', err);
      });
    },
    async fetchPromotion(material, region, onMounted = false) {
      const { furnitureType, furnitureCategory, shelfType } = this;

      this.$refs.priceContainer.style.height = `${this.$refs.priceContainer.getBoundingClientRect().height}px`;
      this.loader = true;

      await this.$store.dispatch('commonPrices/FETCH_PROMOTION', {
        material, furnitureType, shelfType, furnitureCategory, region,
      });

      this.loader = false;
      this.$refs.priceContainer.style.height = 'auto';

      if (!onMounted) {
        this.$store.dispatch('FPM/UPDATE_PRODUCT_DETAILS', null, { root: true });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.configurator-price {
  font-weight: 600;
  font-size: 24px;
  line-height: 33px;
}
.vat-info {
  color: #6F7173
}
.season-sale-banner {
  bottom: 1px;
}
.price-coupon__container {
  display: flex;
  flex-direction: column;
}
.price-coupon {
  font-weight: 600;
  font-size: 24px;
  line-height: 33px;
  color: #C2002A
}
.promo-code {
  border: dashed 2px #1D1E1F;
  padding: 2px 4px;
  font-size: 14px;
  text-transform: uppercase;
  margin-left: 8px;
  display: inline-block;
  line-height: normal;
}
.promo-code-icon {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}
.regular-price---small {
  font-weight: 600;
}
.price-container__mobile {
  padding-top: 6px;
  padding-bottom: 6px;
  .regular-price-text {
    line-height: 17px;
  }
  .regular-price-text span:not(.regular-price---small) {
    color: #6F7173;
    font-size:  12px;
  }

  .regular-price---small {
    font-size: 12px;
    font-weight: 600;
    color: #000000;
  }

  .promo-code, .promo-text {
    font-size: 12px;
  }

  .price-with-discount, .price-coupon {
    font-size: 20px;
  }
}
</style>
