<template>
    <span
        :class="{
            'klarna-desktop-wrapper--as-fallback': shouldShowFallbackText,
        }"
    >
        <svg
            v-if="shouldShowFallbackText"
            class="klarna-icon"
            width="43"
            height="24"
            viewBox="0 0 43 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_747_16142)">
                <path
                    d="M38.871 0.0556641H4.12896C1.8486 0.0556641 0 1.90426 0 4.18462V19.8156C0 22.096 1.8486 23.9446 4.12896 23.9446H38.871C41.1514 23.9446 43 22.096 43 19.8156V4.18462C43 1.90426 41.1514 0.0556641 38.871 0.0556641Z"
                    fill="white"
                />
                <path
                    d="M38.9806 14.0512C38.418 14.0512 37.9619 14.5113 37.9619 15.0791C37.9619 15.6467 38.418 16.107 38.9806 16.107C39.5431 16.107 39.9993 15.6467 39.9993 15.0791C39.9993 14.5113 39.5432 14.0512 38.9806 14.0512ZM35.6291 13.2565C35.6291 12.4792 34.9708 11.8493 34.1586 11.8493C33.3465 11.8493 32.688 12.4793 32.688 13.2565C32.688 14.0337 33.3464 14.6638 34.1586 14.6638C34.9709 14.6638 35.6291 14.0337 35.6291 13.2565ZM35.6346 10.5215H37.2575V15.9913H35.6346V15.6417C35.1761 15.9572 34.6227 16.1427 34.0257 16.1427C32.4461 16.1427 31.1655 14.8505 31.1655 13.2564C31.1655 11.6624 32.4461 10.3703 34.0257 10.3703C34.6227 10.3703 35.1761 10.5558 35.6346 10.8714V10.5215V10.5215ZM22.6467 11.234V10.5216H20.9851V15.9913H22.6504V13.4375C22.6504 12.5759 23.5759 12.1128 24.218 12.1128C24.2246 12.1128 24.2307 12.1135 24.2373 12.1136V10.522C23.5782 10.522 22.9721 10.8067 22.6467 11.2341L22.6467 11.234ZM18.5076 13.2565C18.5076 12.4793 17.8492 11.8493 17.037 11.8493C16.2249 11.8493 15.5664 12.4793 15.5664 13.2565C15.5664 14.0337 16.2248 14.6638 17.037 14.6638C17.8492 14.6638 18.5076 14.0337 18.5076 13.2565ZM18.513 10.5215H20.136V15.9913H18.513V15.6417C18.0545 15.9572 17.501 16.1427 16.9042 16.1427C15.3245 16.1427 14.0438 14.8505 14.0438 13.2565C14.0438 11.6624 15.3245 10.3703 16.9042 10.3703C17.5011 10.3703 18.0545 10.5558 18.513 10.8714V10.5215V10.5215ZM28.2816 10.3744C27.6333 10.3744 27.0197 10.5775 26.6095 11.1378V10.5218H24.9936V15.9913H26.6294V13.1169C26.6294 12.2852 27.1821 11.8779 27.8477 11.8779C28.561 11.8779 28.9711 12.3078 28.9711 13.1056V15.9913H30.5921V12.513C30.5921 11.2401 29.5891 10.3745 28.2816 10.3745V10.3744ZM11.6798 15.9913H13.3785V8.08401H11.6798V15.9913ZM4.21777 15.9935H6.01651V8.08254H4.21777V15.9935ZM10.5092 8.08254C10.5092 9.79518 9.84773 11.3884 8.66913 12.5726L11.1555 15.9938H8.93382L6.2317 12.2757L6.92908 11.7487C8.08559 10.8745 8.74893 9.53831 8.74893 8.08252H10.5092L10.5092 8.08254Z"
                    fill="#0A0B09"
                />
            </g>
            <defs>
                <clipPath id="clip0_747_16142">
                    <rect
                        width="43"
                        height="23.8889"
                        fill="white"
                        transform="translate(0 0.0556641)"
                    />
                </clipPath>
            </defs>
        </svg>
        <KlarnaWrapper
            v-bind="{ price, region, shouldShowFallbackText }"
        />
    </span>
</template>

<script>
    import KlarnaWrapper from './KlarnaWrapper';

    export default {
        components: {
            KlarnaWrapper,
        },
        props: {
            price: {
                type: Number,
                required: true,
            },
            region: {
                type: String,
                required: true,
            },
        },
        computed: {
            shouldShowFallbackText() {
                return this.price > 10000;
            },
        },
    };
</script>

<style lang="scss">
    .klarna-icon {
        margin: 0px -3px -8px -3px;
    }

    .klarna-desktop-wrapper--as-fallback {
        margin-top: -4px;
    }
</style>
