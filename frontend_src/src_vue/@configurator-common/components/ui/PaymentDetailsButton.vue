<template>
  <div class="cta-button-wrapper cta-button-wrapper--hover flex-wrapper pt-12">
    <p
      class="cta-link"
      @click="$emit('onCTAClick')"
    >
      {{ $t(labelTranslationKey) }}
    </p>
  </div>
</template>
<script>
export default {
  props: {
    labelTranslationKey: {
      type: String,
      default: '',
    },
  },
};
</script>
<style scoped>
.cta-link {
  font-weight: 600;
  font-size: 14px;
  color: #1D1E1F;
  border-bottom: 1px solid #1D1E1F;
}
</style>
