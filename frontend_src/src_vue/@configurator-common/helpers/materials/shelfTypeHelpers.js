const shelfTypeMaterialConfigGenerator = (shelfTypeConfig, selectedMaterialsNames) => {
  // Filtering array with selected materials names
  const filteredArray = shelfTypeConfig.filter(material => selectedMaterialsNames.includes(material.material_name));
  // Sorting array by passed selected materials names
  filteredArray.sort((a, b) => selectedMaterialsNames.indexOf(a.material_name) - selectedMaterialsNames.indexOf(b.material_name));

  return filteredArray;
};

export {
  shelfTypeMaterialConfigGenerator,
};
