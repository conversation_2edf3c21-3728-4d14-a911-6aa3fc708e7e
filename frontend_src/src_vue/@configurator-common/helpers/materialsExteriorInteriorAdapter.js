export default class MaterialsExteriorInteriorAdapter {
  constructor(materialConfig) {
    this.materialConfig = materialConfig;
    this.formattedMaterialConfig = this.formatMaterialConfig(materialConfig);
  }

  getFormattedMaterialConfig() {
    return this.formattedMaterialConfig;
  }

  formatMaterialConfig() {
    const exteriorColorObjects = this.createArrayOfExteriorColors();

    return exteriorColorObjects.map(exteriorMaterialObject => {
      const { interiorColors, ...cleanExteriorObj } = exteriorMaterialObject;
      const interiorMaterialsMap = this.createArrayOfInternalColors(exteriorMaterialObject);
      exteriorMaterialObject.interiorMaterials = [...interiorMaterialsMap, cleanExteriorObj];

      return exteriorMaterialObject;
    });
  }

  createArrayOfExteriorColors() {
    return this.materialConfig.filter(materialObject => materialObject.exteriorMaterial === true);
  }

  createArrayOfInternalColors(exteriorMaterialObject) {
    return this.materialConfig.filter(materialObject => materialObject.exteriorMaterialValue === exteriorMaterialObject.value);
  }
}
