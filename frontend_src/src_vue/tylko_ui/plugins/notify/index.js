/*
 *
 * External plugins
 *
 */

/*
 *
 * Components
 *
 */

import TyNotify from '@tylko_ui/components/TyNotify'

/*
 *
 * Store
 *
 */

import notifyStoreModule from '@tylko_ui/plugins/notify/notify.store'

/*
 *
 * Plugin
 *
 */

export default {
    install: (Vue, { store }) => {
        Vue.component('TyNotify', TyNotify)

        if (!store) {
            throw Error('TyNotifyPlugin requires store to register module')
        }

        store.registerModule('notify', notifyStoreModule)
    },
}
