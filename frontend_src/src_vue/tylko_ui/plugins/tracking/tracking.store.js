/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */

/*
 *
 *            IMPORTANT !!!
 *
 * Currently this store contains only tracking methods for checkout.
 * We should consider keeping one generic tracking.store which
 * would have one action like "SEND_DATA_LAYER" or similiar and
 * some site specific stores like "tracking/checkout", "tracking/grid",
 * "tracking/pdp" and so on.
 */

const ecommerceData = {};
const localData = {};

const assemblyECommerceFormat = ({ price }) => ({
  name: 'Paid Assembly',
  id: 1,
  price,
  category: 'service',
  quantity: 1,
});

const makeDataLayerPush = data => {
  const mustHaveKeys = ['eventAction', 'eventLabel', 'eventValue'];

  if (typeof dataLayer !== 'undefined') {
    mustHaveKeys.forEach(key => {
      if (('event' in data) && !(key in data)) {
        data[key] = undefined;
      }
    });

    window.dataLayer.push(data);

    if (window.is_webview) {
      try {
        webkit.messageHandlers.atupaleCallback.postMessage({ event: data.event });
        console.log('wysłane');
      } catch (err) {
        console.log('error', err);
      }
    }
  }

  console.log('dataLayer: ', data);
};

const initialState = () => ({});

const actions = {
  SET_IMPRESSIONS({ rootGetters }) {
    console.log('tracking/SET_IMPRESSIONS');

    const mappedItems = rootGetters['checkout/items'].map(({
      name,
      id,
      price,
      brand,
      category,
      variant,
      position,
      color_name,
      localProductPrice,
      size_txt,
      cart_item_status_width,
      cart_item_status_height,
      cart_item_status_density,
      cart_item_status_depth,
      cart_item_status_drawers,
      cart_item_status_doors,
      timeToDelivery,
      dimension15,
    }) => ({
      name,
      id,
      price,
      brand,
      category,
      variant,
      position,
      color_name,
      localProductPrice,
      size_txt,
      cart_item_status_width,
      cart_item_status_height,
      cart_item_status_density,
      cart_item_status_depth,
      cart_item_status_drawers,
      cart_item_status_doors,
      timeToDelivery,
      dimension15,
    }));

    // TODO: CHECK IF DATA GOES PROPERLY
    // OLD ecommerceGtmData
    ecommerceData.impressions = (ecommerceData.impressions || []);
    mappedItems.forEach(item => (ecommerceData.impressions.push(item)));
  },
  SEND_DATA_LAYER(ctx, { event, additional }) {
    console.log('tracking/SEND_DATA_LAYER');
    let eventObject = { event };

    if (window.loadPresetFlag) {
      return;
    }
    if (typeof dataLayer !== 'undefined') {
      if (typeof additional !== 'undefined') {
        eventObject = Object.assign(eventObject, additional);
      }
      makeDataLayerPush(eventObject);
    }
  },
  SEND_MARKETING_AGREE() {
    console.log('tracking/SEND_MARKETING_AGREE');
    makeDataLayerPush({
      event: 'marketing_grant_1',
      marketing_grant: '1',
    });
  },
  TRACK_PHONE_CLICK() {
    console.log('tracking/TRACK_PHONE_CLICK');
    makeDataLayerPush({ event: 'telephone' });
  },
  TRACK_PAYMENT_METHOD_SELECTION(ctx, { paymentName }) {
    console.log('tracking/TRACK_PAYMENT_METHOD_SELECTION');

    let paymentMethod = paymentName;

    if (typeof paymentMethod === 'undefined') {
      if (typeof localData.lastPaymentMethod === 'undefined') {
        paymentMethod = 'credit_card';
      } else {
        return;
      }
    }

    localData.lastPaymentMethod = paymentMethod;

    makeDataLayerPush({
      event: 'ecommerce checkoutOption',
      optionType: 'paymentMethod',
      ecommerce: {
        checkout_option: {
          actionField: {
            step: 2,
            option: `paymentMethod: ${paymentMethod}`,
          },
        },
      },
    });
  },
  TRACK_ADD_TO_CART(ctx, { price, isAssembly = false }) {
    console.log('tracking/TRACK_ADD_TO_CART');
    console.warn('tracking/TRACK_ADD_TO_CART: we no longer track event: { ecommerce addToCart } - removed from code');
  },
  TRACK_REMOVE_FROM_CART(ctx, { price, isAssembly = false }) {
    console.log('tracking/TRACK_REMOVE_FROM_CART');
    if (isAssembly) {
      const data = assemblyECommerceFormat({ price });

      makeDataLayerPush({
        event: 'ecommerce removeFromCart',
        ecommerce: {
          remove: {
            products: [data],
          },
        },
      });
    }
  },
  TRACK_CHECKOUT_STEP(ctx, { stepNumber, stepName }) {
    console.log('tracking/TRACK_CHECKOUT_STEP');
    const data = {
      ecommerce: {
        checkout: {
          actionField: {
            step: stepNumber,
            option: stepName,
          },
          products: [],
        },
      },
    };

    data.ecommerce = Object.assign({}, data.ecommerce, ecommerceData);
    Object.defineProperty(data.ecommerce.checkout, 'products', Object.getOwnPropertyDescriptor(data.ecommerce, 'impressions'));
    delete data.ecommerce.impressions;
    makeDataLayerPush(data);
  },
  TRACK_CHECKOUT_CONFIRMATION({ rootGetters }) {
    const isProduction = rootGetters['env/isProduction'];
    const debug = rootGetters['env/debug'];
    const {
      order_id,
      base_total_value,
      base_total_value_net,
      base_promo_amount,
      base_promo_amount_net,
      base_vat_value,
      items,
    } = rootGetters['checkout/order'];

    if (!isProduction || debug) {
      return;
    }

    const transactionProducts = items.map(item => {
      const {
        order_item_id,
        furniture_type,
        category,
        base_price,
        price_net,
        base_price_without_discount,
        base_price_net_without_discount,
      } = item;

      return {
        sku: order_item_id,
        name: furniture_type,
        category,
        price: base_price,
        price_net,
        price_without_discount: base_price_without_discount,
        price_net_without_discount: base_price_net_without_discount,
        quantity: 1,
      };
    });

    window.dataLayer.push({
      transactionId: order_id,
      transactionAffiliation: '',
      transactionTotal: base_total_value,
      transactionTotalNet: base_total_value_net,
      transactionDiscount: base_promo_amount,
      transactionDiscountNet: base_promo_amount_net,
      transactionTax: base_vat_value,
      transactionShipping: 0,
      transactionProducts,
    });
  },
  TRACK_CHECKOUT_INPUT_INTERACTION({ dispatch }, { type, inputName }) {
    dispatch('SEND_DATA_LAYER', {
      event: 'userInteraction',
      additional: {
        eventType: 'NOEEC',
        eventCategory: 'form-input',
        eventAction: inputName,
        eventLabel: type,
        eventNonInteraction: false,
      },
    });
  },
};

const mutations = {};

const getters = {};

export default {
  state: initialState(),
  actions,
  mutations,
  getters,
  namespaced: true,
};
