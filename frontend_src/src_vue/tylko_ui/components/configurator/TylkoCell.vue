<template>
    <div
        class="tylko-cell"
    >
        <p :class="evaluatedClasses">{{ label }}</p>
        <slot/>
    </div>
</template>
<style lang="scss" scoped>
    .tylko-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .text {
            margin-bottom: 0;
        }
    }
</style>
<script>
    export default {
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            label: {
                type: String,
                required: true,
            },
        },
        computed: {
            evaluatedClasses() {
                return [
                    'normal-14',
                    'text-offblack-800',
                    'text',
                    'mr-8',
                    'md:mr-16',
                    { 't-color-grey_500': this.disabled },
                ]
            },
        }
    }
</script>
