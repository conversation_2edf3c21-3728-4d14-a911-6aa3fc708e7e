<template>
    <fragment>
        <div
            ref="toggleGroup"
            :class="[evaluatedClasses, className]"
        >
            <button
                v-for="(option, index) in options"
                :key="index"
                class="toggle-button"
                :class="evaluatedClassesButton(option,index)"
                :disabled="disabled || option.disabled"
                @click="() => { updateModel(option.value) }"
            >
                <span
                    v-if="displaySVG"
                    class="svg-wrapper-button"
                />
                <span class="button-label">{{ option.label }}</span>
            </button>
            <div
                ref="overlay"
                class="overlay"
                :style="{ left: overlayPosition + 'px' }"
            >
                <template v-if="displaySVG">
                    <span
                        v-for="(_, index) in options"
                        v-show="activeItemIndex() === index"
                        :key="index"
                        class="overlay-svg-wrapper"
                    />
                </template>
                <span class="overlay-label">{{ evaluatedLabel }}</span>
            </div>
        </div>
        <div
            v-if="displaySVG"
            ref="svgWrapper"
            class="svg-wrapper"
        >
            <slot />
        </div>
    </fragment>
</template>

<script>
    export default {
        props: {
            value: {
                type: [String, Number, Boolean],
            },
            className: {
                type: String,
                default: null,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            options: {
                type: Array,
                required: true,
            },
            size: {
                type: String,
                default: 't-sm',
            },
            noUppercase: {
                type: Boolean,
                default: false,
            },
            displaySVG: {
                type: Boolean,
                default: false,
            },
            fixedLabel: {
                type: Boolean,
                default: true,
            },
            trigger: {
                type: [Boolean],
                default: false,
            },
            panelToggle: {
                type: [Boolean],
                default: false,
            },
            buttonUnderLabels: {
                type: [Boolean],
                default: true,
            },
            isMobile: {
                type: [Boolean],
                default: false,
            },
        },
        data() {
            return {
                isMounted: false,
                widestButton: 0,
                evaluatedLabel: '',
                timeout: null,
            };
        },
        computed: {
            evaluatedClasses() {
                return [
                    this.size,
                    { 'no-uppercase': this.noUppercase },
                    { 'fixed-label': this.fixedLabel },
                    { disabled: this.disabled },
                    { 'display-svg': this.displaySVG },
                    { 'panel-toggle': this.panelToggle },
                    { 'toggle-group': !this.panelToggle },
                    { 'button-under-labels': this.buttonUnderLabels },
                ];
            },
            overlayPosition() {
                if (this.isMounted && this.widestButton) {
                    return this.triggerToggle();
                }
                return '';
            },
            // evaluatedLabel() {
            //     return this.options.reduce((prev, next) => (next.value === this.value ? prev + next.label : `${prev}`), '');
            // },
        },
        watch: {
            trigger(val) {
                if (val) {
                    this.init();
                    const offset = this.triggerToggle();
                    this.$refs.overlay.style.left = `${offset}px`;
                }
            },
            value(val) {
                window.clearTimeout(this.timeout);
                this.timeout = setTimeout(() => {
                    this.evaluatedLabel = this.options.reduce((prev, next) => (next.value === val ? prev + next.label : `${prev}`), '');
                }, 30);
            },
        },
        mounted() {
            this.$nextTick(() => {
                this.isMounted = true;
                if (this.displaySVG) {
                    Array.from(this.$refs.svgWrapper.children).forEach((svg, index) => {
                        const cloneSvg = svg.cloneNode(true);
                        this.toggleButtonsArray()[index].getElementsByClassName('svg-wrapper-button')[0].appendChild(svg);
                        this.$refs.toggleGroup.getElementsByClassName('overlay-svg-wrapper')[index].appendChild(cloneSvg);
                    });
                    // WHY THIS WRAPPER IS REMOVED?
                    if (this.isMobile) this.$refs.svgWrapper.remove();
                }
                this.init();
            });
        },
        methods: {
            init() {
                const buttons = this.toggleButtonsArray();
                this.widestButton = Math.max.apply(null, buttons.map(item => item.offsetWidth));
                this.evaluatedLabel = this.options.reduce((prev, next) => (next.value === this.value ? prev + next.label : `${prev}`), '');
            },
            triggerToggle() {
                const offset = -2;
                const index = this.activeItemIndex();
                if (index === 0) {
                    return offset;
                }
                const width = this.toggleButtonsArray().reduce((prev, next, i) => prev + (i < index ? next.offsetWidth : 0), 0);
                return width + offset;
            },
            activeItemIndex() {
                return this.options.findIndex(item => item.value === this.value);
            },
            updateModel(val) {
                this.$emit('input', val);
                setTimeout(() => {
                    const offset = this.triggerToggle();
                    this.$refs.overlay.style.left = `${offset}px`;
                }, 30);
            },
            toggleButtonsArray() {
                return Array.from(this.$refs.toggleGroup.getElementsByClassName('toggle-button'));
            },
            divider(index) {
                const activeIndex = this.activeItemIndex();
                if (activeIndex === index || (activeIndex - 1) === index) {
                    return true;
                }
                return false;
            },
            evaluatedClassesButton(item, index) {
                return [
                    { divider: this.divider(index) },
                    { active: item.value === this.value },
                    { disabled: this.disabled || item.disabled },
                ];
            },
            refreshImage() {
                const overlayImgWrapper = this.$refs.overlay.querySelectorAll('.overlay-svg-wrapper');
                Array.from(this.$refs.toggleGroup.children).forEach((btn, index) => {
                    if (btn.classList.contains('toggle-button')) {
                        if (!btn.querySelector('.svg-wrapper-button').children.length) return;
                        const clone = btn.querySelector('.svg-wrapper-button').children[0].cloneNode(true);
                        overlayImgWrapper[index].innerHTML = '';
                        overlayImgWrapper[index].appendChild(clone);
                    }
                });
            },
        },
    };
</script>
