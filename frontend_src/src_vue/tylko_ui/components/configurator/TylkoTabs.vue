<template>
  <section
    ref="container"
    class="tylko-tabs-container"
  >
    <span
      v-if="!hideShadow"
      ref="shadowLeft"
      class="shadow left"
    />
    <div
      ref="wrapper"
      class="tylko-tabs-wrapper"
    >
      <slot :init="init" />
    </div>
    <span
      v-if="!hideShadow"
      ref="shadowRight"
      class="shadow right"
    />
  </section>
</template>
<script>
import throttle from 'lodash/throttle';

function scrollTo(element, to, duration) {
  $(element).animate({
    scrollLeft: to,
  }, duration);
}

export default {
  props: {
    name: [String],
    activeTab: [Number],
    hideShadow: {
      type: [Boolean],
      default: false,
    },
    emitScroll: [Boolean],
    materialToggle: {
      type: [Boolean],
      default: false,
    },
  },
  data() {
    return {
      contentWidth: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
      this.scrollToActiveTab(this.activeTab, false);
    });
    if (this.emitScroll) {
      this.$refs.wrapper.addEventListener('scroll', throttle(e => {
        this.$emit('scrollHandler', e);
      }, 200));
    }
  },
  methods: {
    init() {
      const { shadowLeft, shadowRight } = this.$refs;
      const { wrapper } = this.$refs;
      if (!wrapper || wrapper.children[0].offsetWidth === 0) return;
      const contentWidth = [...wrapper.children].reduce((prev, item) => prev + item.offsetWidth, 0);
      if (contentWidth > wrapper.offsetWidth) {
        if (!this.hideShadow) {
          wrapper.addEventListener('scroll', () => {
            setTimeout(() => {
              if (wrapper.scrollLeft >= 5) {
                shadowLeft.style.opacity = 1;
              } else {
                shadowLeft.style.opacity = 0;
              }
              if (contentWidth < wrapper.scrollLeft + wrapper.offsetWidth + 5) {
                shadowRight.style.opacity = 0;
              } else {
                shadowRight.style.opacity = 1;
              }
            }, 30);
          });
        }
      } else if (!this.hideShadow) {
        shadowLeft.remove();
        shadowRight.remove();
      }
    },
    scrollToActiveTab(index, softScroll = true) {
      const el = this.$refs.wrapper;
      if (!el) return;
      const wrapperWidth = el.offsetWidth;
      let items;
      if (this.materialToggle) {
        if (!el.querySelector('.colors-wrapper')) return;
        const colorItems = el.querySelector('.colors-wrapper');
        items = [...colorItems.children].slice(0, index + 1);
      } else {
        items = [...el.children].slice(0, index + 1);
      }

      let distanceToScroll = items.reduce((prev, item, i) => prev + (i === index ? item.offsetWidth / 2 : item.offsetWidth), 0);
      distanceToScroll -= wrapperWidth / 2;
      if (softScroll) {
        scrollTo(this.$refs.wrapper, distanceToScroll, 300);
      } else {
        this.$refs.wrapper.scrollLeft = distanceToScroll;
      }
    },
    scrollToElement(position) {
      const { wrapper, container } = this.$refs;
      const el = this.$refs.wrapper;
      const centeredDistance = wrapper.scrollLeft + (container.offsetWidth / 2);
      let offsetPalceholder = 0;
      const centeredItem = [...el.children].findIndex(item => {
        offsetPalceholder += item.offsetWidth;
        return (offsetPalceholder + item.offsetWidth) >= centeredDistance;
      }) + 1;
      this.scrollToActiveTab(centeredItem + position);
    },
    triggerScroll(activeTab, softScroll) {
      this.$nextTick(() => {
        this.init();
        this.scrollToActiveTab(activeTab, softScroll);
      });
    },
  },

};
</script>
<style lang="scss" scoped>
    .tylko-tabs-container {
        position: relative;
        overflow: hidden;
        margin-top: -10px;

        .shadow {
            position: absolute;
            width: 10px;
            height: 36px;
            top: 2px;
            bottom: 0;
            z-index: 1;
            transition: opacity 0.3s ease;

            &.left {
                opacity: 0;
                left: 0;
                background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
            }

            &.right {
                opacity: 1;
                right: 0;
                background: linear-gradient(to left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
            }
        }
    }

    .tylko-tabs-wrapper {
        display: block;
        text-align: left;
        padding-bottom: 10px;
        transform: translateY(10px);
        flex-wrap: nowrap;
        overflow-x: scroll;
        /*justify-content: center;*/
        overflow-y: hidden;
        white-space: nowrap;
        box-sizing: border-box;
        margin: auto;
        > * {
            display: inline-block;
        }
        &::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
        }

        -webkit-overflow-scrolling: touch;
    }
</style>
