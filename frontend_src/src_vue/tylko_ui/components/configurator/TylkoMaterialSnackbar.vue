<template>
  <transition name="material-snackbar">
    <div
      v-if="shouldRender && isVisible"
      class="tylko-snackbar tylko-snackbar--material bg-white px-16 py-8 flex"
      :class="{'tylko-snackbar--change-position': changePosition}"
    >
      <h3 class="normal-14 text-offblack-600 tylko-snackbar__text">
        {{ $t(copyList[activeItem]) }}
      </h3>
    </div>
  </transition>
</template>

<script>

export default {
  props: {
    copyList: {
      required: true,
      type: Object,
    },
    activeItem: {
      required: true,
      type: [Number, String],
    },
    shouldRender: {
      default: true,
      type: Boolean,
    },
    timeout: {
      default: 2000,
      type: Number,
    },
    changePosition: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      isVisible: true,
      timer: null,
    };
  },
  watch: {
    activeItem(val, oldVal) {
      if (val !== oldVal) {
        clearTimeout(this.timer);
        this.isVisible = true;
        this.timer = setTimeout(() => {
          this.isVisible = false;
        }, this.timeout);
      }
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  },
};
</script>
