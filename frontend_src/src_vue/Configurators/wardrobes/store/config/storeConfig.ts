import commonUI from '../../../../@configurator-common/storeModules/commonUI';
import commonConfiguration from '../../../../@configurator-common/storeModules/commonConfiguration';
import commonPrices from '../../../../@configurator-common/storeModules/commonPrices';
import commonFurniture from '../../../../@configurator-common/storeModules/commonFurniture';
import commonGeomLocalStorage from '../../../../@configurator-common/storeModules/commonGeomLocalStorage';
import commonAbTests from '../../../../@configurator-common/storeModules/commonAbTests';

import CWatWarActions from '../modules/CWatWarModule/actions';
import { CWatWarMutations } from '../modules/CWatWarModule/mutations';
import CWatWarState from '../modules/CWatWarModule/state';
import CWatWarGetters from '../modules/CWatWarModule/getters';

import CVertActions from '../modules/CVertModule/actions';
import { CVertMutations } from '../modules/CVertModule/mutations';
import CVertState from '../modules/CVertModule/state';
import CVertStateGetters from '../modules/CVertModule/getters';

export const commonModules = {
  modules: {
    commonUI,
    commonConfiguration,
    commonPrices,
    commonFurniture,
    commonGeomLocalStorage,
    commonAbTests,
  },
};
export const CWatWarStore = {
  ...commonModules,
  actions: CWatWarActions,
  mutations: CWatWarMutations,
  state: CWatWarState,
  getters: CWatWarGetters,
};
export const CVertStore = {
  ...commonModules,
  actions: CVertActions,
  mutations: CVertMutations,
  state: CVertState,
  getters: CVertStateGetters,
};
