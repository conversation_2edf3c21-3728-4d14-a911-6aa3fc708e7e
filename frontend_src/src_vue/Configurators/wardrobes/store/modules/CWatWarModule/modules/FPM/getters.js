export default {
    GET_THUMBNAILS_GEOMETRY(state) {
        return state.thumbnailsGeometry;
    },
    GET_THUMBNAIL_TITLE(state, _, rootState) {
        if (!state.thumbnailsGeometry) return [];
        const isExternal = rootState.configuration.drawerStyle === 'external';
        const externalComponentsList = ['rail_l_2drawers', 'rail_4drawers', 'slabs_4drawers', 'slabs_6drawers'];
        return state.thumbnailsGeometry.map(({ series_name }) => `${series_name}${isExternal && externalComponentsList.includes(series_name) ? '_external' : ''}`);
    },
    GET_COMPONENTS(state) {
        return state.components;
    },
    GET_COMPONENTS_ARR(state) {
        return state.geom.components;
    },
    GET_GEOMETRY(state) {
        return state.geom;
    },
    GET_COMPONENT_WIDTH(state, _, rootState) {
        return state.geom.components[rootState.activeComponentIndex].width;
    },
    GET_WATTY_INFO(state) {
        return state.wattyInfo;
    },
    GET_ACTIVE_THUMBNAIL(state, { GET_COMPONENT_SERIES_ID }) {
        return state.thumbnailsGeometry.findIndex(item => item.series_id === GET_COMPONENT_SERIES_ID);
    },
    GET_COMPONENT_SERIES_ID(state, _, rootState) {
        return state.components[rootState.activeComponent].series_id;
    },
    GET_RAW_GEOMETRY(state) {
        return state.rawGeom;
    },
};
