import { dimensions, FPM, offscreenRenderer, renderer } from '../../../core';

export default {
  UPDATE_SHOW_DIMENSIONS({ dispatch, commit }, payload) {
    dispatch('FPM/UPDATE_GEOMETRY', { onSliderRelease: true }, { root: true });
    commit('updateShowDimensions', payload);
    if (payload) {
      dispatch('UPDATE_DIMENSIONS');
    } else {
      dispatch('REMOVE_DIMENSIONS');
    }
  },
  UPDATE_DIMENSIONS({ rootState }) {
    if (renderer.isReady) dimensions.drawDimensions();
  },
  REMOVE_DIMENSIONS() {
    if (renderer.isReady) dimensions.removeDimensions();
  },
  UPDATE_SHOW_DIMENSIONS_MOBILE({ commit, dispatch, rootState }, payload) {
    if (payload) {
      dispatch('renderer/UPDATE_OFFSCREEN_RENDERER', null, { root: true });
      commit('updateShowDimensionsMobile', offscreenRenderer.getScreenshotForMobileDim());
    } else {
      commit('resetBase64image', null);
    }
  },
};
