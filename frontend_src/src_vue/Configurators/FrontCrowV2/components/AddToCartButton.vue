<template>
  <AddToCartButtonUI
    buttonTranslationKey="crow_add_to_cart"
    :cartLoader="cartLoader"
    :addToCart="addToCart"
  />
</template>

<script>
import AddToCartButtonUI from '@configurator-common/components/ui/AddToCartButtonUI';
import { addToCart } from '../mixins/storeComponentCommunication';

export default {
  components: { AddToCartButtonUI },
  mixins: [addToCart],
  computed: {
    cartLoader() {
      return false;
    },
  },
};
</script>
