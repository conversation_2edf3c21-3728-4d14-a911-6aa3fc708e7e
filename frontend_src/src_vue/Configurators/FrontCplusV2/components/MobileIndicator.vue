<template>
    <div class="mobile-indicator-wrapper">
        <div
            class="relative"
            :style="coordsForIndicator"
        >
            <button class="pulsar">
                <img
                    svg-inline
                    class="pulsar__img"
                    src="@tylko_ui/icons-cplus/ic_mobile_pulsar.svg"
                    alt="adjust"
                >
            </button>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'MobileIndicator',
        computed: {
            coordsForIndicator() {
                let coords = { x: 0, y: 0 };
                if (this.$store.getters['ui/GET_COORDINATES_FOR_BUTTONS_MOBILE'].length > 0) {
                    [coords] = this.$store.getters['ui/GET_COORDINATES_FOR_BUTTONS_MOBILE'];
                }
                return `transform: translate(${coords.x}px,${coords.y}px);`;
            },
        },
    }
</script>
