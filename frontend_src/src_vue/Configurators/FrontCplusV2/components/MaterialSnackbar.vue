<template>
  <transition name="material-snackbar">
    <div
      v-if="isActive"
      class="tylko-snackbar tylko-snackbar--material bg-white px-16 py-8 flex"
    >
      <h2 class="normal-14 text-offblack-600 tylko-snackbar__text">
        {{ copy }}
      </h2>
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      isVisible: false,
      timer: null,
    };
  },
  computed: {
    materialConfig() {
      return this.$store.getters['commonMaterials/GET_MATERIAL_CONFIG'];
    },
    activeTab() {
      const tabs = this.$store.getters['ui/GET_TABS'];
      const activeTab = this.$store.getters['ui/GET_ACTIVE_TAB'];
      return tabs[activeTab] ? tabs[activeTab].code : false;
    },
    material() {
      return this.$store.getters['configuration/GET_MATERIAL'];
    },
    shelfType() {
      return this.$store.getters.GET_SHELF_TYPE;
    },
    copy() {
      const copy = this.materialConfig.find(el => el.value === this.material).translation;
      return this.$t(copy);
    },
    isActive() {
      return this.activeTab === 'color' && this.isVisible;
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
    this.snackbarWatcher();
  },
  created() {
    this.snackbarWatcher = this.$store.watch(
      (state, getters) => getters['ui/GET_MATERIAL_SNACKBAR'],
      () => {
        clearTimeout(this.timer);
        this.isVisible = true;
        this.timer = setTimeout(() => {
          this.isVisible = false;
        }, 2000);
      },
    );
  },
};
</script>
