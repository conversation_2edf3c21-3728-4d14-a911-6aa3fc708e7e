/* eslint-disable  import/no-mutable-exports */
import LocalEdge from '@src_webgl/rendererJetty/localEdge';
import { initPricing } from '@src_webgl/pricingV3/pricing';
import {
  deprecatedDimensionsAPI,
  deprecatedOffscreenRendererAPI,
  deprecatedRendererAPI,
} from '@src_webgl/rendererT13/mockedAPI';

import {
  DynamicShadowRendererAPI,
  ScreenShotRendererAPI,
  RendererInteractionAPI,
  RendererUtilsAPI,
} from '@libs/space-renderer.min';
import { buildSpaceRenderFormat } from '@libs/space-describer.min';
import FurnitureProjectManager from '@libs/fpm-original';

let FPM = null;
let pricing = null;
let localEdge = null;
let category = 'unknow';
let isIOSMobile = false;

const renderer = {
  ...deprecatedRendererAPI,
  openDoorAndDrawersForSection: sectionId => {
    // not implemented yet
  },
  openAllDoorsAndDrawers: () => {
    RendererInteractionAPI.openDoors();
  },
  closeAllDoorsAndDrawers: () => {
    RendererInteractionAPI.closeDoors();
  },
  toggleDoorsVisibility: val => {
    if (val) RendererInteractionAPI.openDoors();
    else RendererInteractionAPI.closeDoors();
  },
  toggleItemsVisibility: val => {
    if (val) RendererInteractionAPI.showItemsOnShelves();
    else RendererInteractionAPI.hideItemsOnShelves();
  },
  anyItemsOnShelves: () => RendererUtilsAPI.anyItemsOnShelves(),
  onWindowResize: (width, height) => {
    DynamicShadowRendererAPI.changeSize(width, height);
  },
  setTabSpecificView: tab => {
    RendererInteractionAPI.setSpecificView(tab);
  },
  updateComponentHoverBox: sectionId => {
    RendererInteractionAPI.updateComponentHoverBox(sectionId);
  },
  setInactiveState: sectionId => {
    RendererInteractionAPI.setInactiveState(sectionId);
  },
  setComponentView: (sectionId, isMobile) => {
    if (isMobile) RendererInteractionAPI.setComponentViewAndFocus(sectionId);
    else RendererInteractionAPI.setComponentView();
  },
  setShelfView: (geom, isMobile, tripleSnap = false) => {
    if (!isMobile) return;
    RendererInteractionAPI.setShelfView(tripleSnap);
  },
  getCoordinatesForButtons: () => ({
    mobile: RendererInteractionAPI.get2DCoordinatesForHovers('center', 0),
    desktop: RendererInteractionAPI.get2DCoordinatesForHovers('bottom', 0.2),
  }),
  isReady: false,
  getAnalyzedSession: () => RendererUtilsAPI.getAnalyzedSession(),
  destroy: () => {
    RendererUtilsAPI.disposeApp();
  },
};
const offscreenRenderer = {
  ...deprecatedOffscreenRendererAPI,
  getScreenshotForCart: () => {
    ScreenShotRendererAPI.changeSize(600, 600);
    return ScreenShotRendererAPI.drawLevel('row');
  },
  getScreenshotForSave: () => {
    ScreenShotRendererAPI.changeSize(600, 600);
    return ScreenShotRendererAPI.drawLevel('row');
  },
  getScreenshotForMobileDim: () => {
    const geom = FPM.getGeometry();
    const rawDimensions = FPM != null ? FPM.calculateDimensions().all : [];

    const spaceRenderFormat = buildSpaceRenderFormat([{ ...geom, dimensions: rawDimensions }], {
      name: 'tylko-space',
      category,
      system: 'col',
      shelfOffset: { x: 0, y: 0, z: 0 },
      hideItems: isIOSMobile,
    });

    ScreenShotRendererAPI.updateLevel(spaceRenderFormat);
    return ScreenShotRendererAPI.drawDimensionsView();
  },
};

// all | left | right | center
let dimensionsMode = 'all';
const getDimensions = activeComponent => {
  if (FPM == null) return [];
  if (activeComponent == null) return FPM.calculateDimensions().all;

  switch (dimensionsMode) {
  case 'all':
    return FPM.calculateDimensions().all;
  case 'left':
    return FPM.calculateDimensions('local_edge_left', Number(activeComponent)).all;
  case 'right':
    return FPM.calculateDimensions('local_edge_right', Number(activeComponent)).all;
  case 'center':
    return FPM.calculateDimensions('local_edge_center', Number(activeComponent)).all;
  default:
    return [];
  }
};
const recalculateAndDrawDimensions = rawDimensions => {
  const geom = FPM.getGeometry();
  const spaceRenderFormat = buildSpaceRenderFormat([{ ...geom, dimensions: rawDimensions }], {
    name: 'tylko-space',
    category,
    system: 'col',
    shelfOffset: { x: 0, y: 0, z: 0 },
    hideItems: isIOSMobile,
  });
  RendererInteractionAPI.showDimensions();
  DynamicShadowRendererAPI.updateAndDrawLevel(spaceRenderFormat, false);
};
const dimensions = {
  ...deprecatedDimensionsAPI,
  drawAllDimensions: () => {
    dimensionsMode = 'all';
    return new Promise((resolve, reject) => {
      const allDimensions = getDimensions();
      if (FPM) recalculateAndDrawDimensions(allDimensions);
      resolve();
    });
  },
  drawLeftDimensions: activeComponent => {
    dimensionsMode = 'left';
    return new Promise((resolve, reject) => {
      const leftDimensions = getDimensions(activeComponent);
      if (FPM) recalculateAndDrawDimensions(leftDimensions);
      resolve();
    });
  },
  drawRightDimensions: activeComponent => {
    dimensionsMode = 'right';
    return new Promise((resolve, reject) => {
      const rightDimensions = getDimensions(activeComponent);
      if (FPM) recalculateAndDrawDimensions(rightDimensions);
      resolve();
    });
  },
  drawCenterDimensions: activeComponent => {
    dimensionsMode = 'center';
    return new Promise((resolve, reject) => {
      const centerDimensions = getDimensions(activeComponent);
      if (FPM) recalculateAndDrawDimensions(centerDimensions);
      resolve();
    });
  },
  removeDimensions: () => {
    RendererInteractionAPI.hideDimensions();
  },
};

function initFurnitureProjectManager(serialization) {
  FPM = new FurnitureProjectManager().withDNA(serialization);
}

async function initJettyPricing(galleryFormat) {
  pricing = await initPricing(galleryFormat, window.location.origin);
}

function updateShelfGeometrySync(geom, cameraUpdate, activeComponent = null) {
  const spaceRenderFormat = buildSpaceRenderFormat([{ ...geom, dimensions: [] }], {
    name: 'tylko-space',
    category,
    system: 'col',
    shelfOffset: { x: 0, y: 0, z: 0 },
    hideItems: isIOSMobile,
  });

  const changeCameraFocus = activeComponent == null && cameraUpdate;
  DynamicShadowRendererAPI.updateAndDrawLevel(spaceRenderFormat, changeCameraFocus);
}

function updateShelfGeometry(geom, cameraUpdate, activeComponent = null) {
  return new Promise((resolve, reject) => {
    updateShelfGeometrySync(geom, cameraUpdate, activeComponent);
    resolve();
  });
}

function onContextLostHandler() {
  PubSub.publish('webGlContextLost');
}

function onContextRestoreHandler() {
  PubSub.publish('webGlContextRestore');
}

function setListeners(args) {
  const timeoutTime = 100;

  const newHoverAction = ({ key, value }) => {
    args.dispatcher('UPDATE_HOVER_COMPONENT', value);
  };
  const noneHoverAction = ({ key, value }) => {
    args.dispatcher('UPDATE_HOVER_COMPONENT', null);
  };
  const newClickAction = ({ key, value }) => {
    args.dispatcher('ACTIVE_COMPONENT', value);
  };
  const noneClickAction = ({ key, value }) => {
    args.dispatcher('DEACTIVATE_COMPONENT');
  };
  const cameraAnimationOn = () => {
    args.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', true);
  };
  const cameraAnimationOff = () => {
    setTimeout(() => {
      args.dispatcher('ui/UPDATE_COORDINATES_FOR_BUTTONS', renderer.getCoordinatesForButtons());
      args.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', false);
    }, timeoutTime);
  };
  const cameraDragingOn = () => {
    args.dispatcher('interactions/SET_IS_ORBIT_MOVING', true);
  };
  const cameraDragingOff = () => {
    setTimeout(() => {
      args.dispatcher('ui/UPDATE_COORDINATES_FOR_BUTTONS', renderer.getCoordinatesForButtons());
      args.dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
    }, timeoutTime);
  };

  RendererInteractionAPI.subscribe('hovered-section', newHoverAction);
  RendererInteractionAPI.subscribe('hovered-outside', noneHoverAction);
  RendererInteractionAPI.subscribe('clicked-section', newClickAction);
  RendererInteractionAPI.subscribe('clicked-outside', noneClickAction);

  RendererInteractionAPI.subscribe('camera-animation-start', cameraAnimationOn);
  RendererInteractionAPI.subscribe('camera-animation-end', cameraAnimationOff);
  RendererInteractionAPI.subscribe('orbit-movement-start', cameraDragingOn);
  RendererInteractionAPI.subscribe('orbit-movement-end', cameraDragingOff);

  RendererInteractionAPI.subscribe('webgl-context-lost', onContextLostHandler);
  RendererInteractionAPI.subscribe('webgl-context-restored', onContextRestoreHandler);
}

async function initTheRenderer(args) {
  category = args.category;
  isIOSMobile = args.store.ui.isMobile && /iPhone/i.test(window.navigator.userAgent);

  const isDesk = args.category === 'desk' || args.category === 'dressing_table';

  const spaceRenderFormat = buildSpaceRenderFormat([{ ...args.geom, dimensions: [] }], {
    name: 'tylko-space',
    category,
    system: 'col',
    shelfOffset: { x: 0, y: 0, z: 0 },
    hideItems: isIOSMobile,
  });

  RendererUtilsAPI.setAssetsSource('/r_static/src_webgl/t13');
  RendererUtilsAPI.setCanvasWrapperId('conf');
  RendererUtilsAPI.setEnvironmentSettingsPreset('shelf');
  RendererUtilsAPI.setCameraSettingsPreset(isDesk ? 'desk' : 'cplus');
  RendererUtilsAPI.setInitialInteractionLayer('shelf_closed_dimensions_hidden');

  DynamicShadowRendererAPI.initialize({
    container: args.container,
    width: args.width,
    height: args.height,
    // TO DELETE
    dispatcher: args.dispatcher,
  });

  ScreenShotRendererAPI.initialize({
    width: 600,
    height: 600,
  });

  return DynamicShadowRendererAPI.loadAssets().then(() => {
    DynamicShadowRendererAPI.setupLevel(spaceRenderFormat);
    DynamicShadowRendererAPI.run();

    setListeners(args);

    localEdge = new LocalEdge(RendererUtilsAPI.getSceneInstance(), RendererUtilsAPI.getCameraInstance(), args.container);
    renderer.isReady = true;
    document.getElementById('conf').dispatchEvent(new CustomEvent('loadCanvas'));
  });
}

async function initOffScreenRenderer(args) {}


export {
  initFurnitureProjectManager,
  initTheRenderer,
  initOffScreenRenderer,
  initJettyPricing,
  updateShelfGeometry,
  FPM,
  renderer,
  dimensions,
  localEdge,
  pricing,
  offscreenRenderer,
};
