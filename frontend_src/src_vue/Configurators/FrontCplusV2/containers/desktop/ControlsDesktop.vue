<template>
  <div class="bg-white controls-desktop md:p-16 md:mb-32 xl2:p-24">
    <controlsDesktopPrice />
    <div class="cplus-divider" />
    <div>
      <div
        v-if="!isDesksProduct"
        class="mt-8"
      >
        <div class="row middle-xs">
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_style_name") }}
            </p>
          </div>
          <div class="col-xs-9">
            <ToggleFurnitureStyle />
          </div>
        </div>
      </div>
      <transition name="fade-slow">
        <div
          v-if="configurationParams && isDistortionAvailable"
          class="row middle-xs mt-16"
        >
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800 text-nowrap">
              {{ $t("cplus_density") }}
              <span class="bold-10 banner-new">
                {{ $t("common_new") }}
              </span>
            </p>
          </div>
          <div class="col-xs-9 flex items-center">
            <div class="px-32 slider-mock-wrapper w-full">
              <TylkoSliderControls
                v-model="distortion"
                :rangeMin="distortionRange.min"
                :rangeMax="distortionRange.max"
                unit="%"
                @onSliderRelease="onSliderRelease"
                @onSliderChange="onSliderChange"
                @handleAdjustSlider="handleAdjustSlider"
              />
            </div>
            <div class="ml-8">
              <TylkoConfiguratorTooltip :isPositionFixed="isPositionFixed">
                <div
                  class="text-offblack-600 normal-14"
                  v-html="$t('cplus_density_info', { button: densityButton })"
                />
              </TylkoConfiguratorTooltip>
            </div>
          </div>
        </div>
      </transition>
      <div class="mt-16">
        <div class="row middle-xs">
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_width") }}
            </p>
          </div>
          <div class="col-xs-9">
            <div class="px-32 slider-mock-wrapper">
              <TylkoSliderControls
                v-if="configurationParams"
                v-model="width"
                :rangeMin="widthRange.min"
                :rangeMax="widthRange.max"
                @onSliderRelease="onSliderRelease"
                @onSliderChange="onSliderChange"
                @handleAdjustSlider="handleAdjustSlider"
              />
            </div>
          </div>
        </div>

        <div
          v-if="!isDesksProduct"
          class="mt-16 row middle-xs"
        >
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_height") }}
            </p>
          </div>
          <div class="col-xs-9">
            <div class="px-32 slider-mock-wrapper">
              <TylkoSlider
                v-if="configurationParams"
                v-model="height"
                :rangeMin="heightRange.min"
                :rangeMax="heightRange.max"
                :step="heightRange.step"
                :additionalValue="additionalHeight"
                @onSliderRelease="onSliderRelease"
                @onSliderChange="onSliderChange"
              />
            </div>
          </div>
        </div>
        <div
          v-if="isDesksProduct"
          class="mt-16 row middle-xs"
        >
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_height") }}
            </p>
          </div>
          <div class="col-xs-9">
            <p class="normal-12 text-offblack-8">
              {{ $t(isDressingTable ? "cplus_dressing_table_height_info": "cplus_desk_height_info") }}
            </p>
          </div>
        </div>
        <div class="mt-16 row middle-xs">
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_depth") }}
            </p>
          </div>
          <div class="col-xs-8 inline-wrapper">
            <div style="display: inline-block">
              <toggleGroup
                v-if="configurationParams"
                v-model="depth"
                noUppercase
                :options="depthOptions"
              />
            </div>
          </div>
        </div>
        <transition name="fade-slow">
          <div
            v-if="configurationParams && isColumnCountAvailable"
            class="mt-16 row middle-xs"
          >
            <div class="col-xs-3">
              <p class="normal-14 text-offblack-800">
                {{ isDesksProduct ? $t("cplus_storage_columns") : $t("cplus_columns") }}
              </p>
            </div>
            <div class="col-xs-8">
              <TylkoStepper
                v-model="columnCount"
                :options="columnCountOptions"
              />
            </div>
          </div>
        </transition>
      </div>

      <div
        v-if="isDesksProduct"
        class="mt-16 row middle-xs"
      >
        <div class="col-xs-3">
          <p class="normal-14 text-offblack-800">
            {{ $t("cplus_legroom_placement") }}
          </p>
        </div>
        <div class="col-xs-9">
          <div :style="legroomSliderWidth">
            <div class="px-32 slider-mock-wrapper">
              <TylkoSlider
                v-if="configurationParams"
                v-model="legroomArea"
                :rangeMin="legroomAreaRange.min"
                :rangeMax="legroomAreaRange.max"
                :step="legroomAreaRange.step"
                :customTooltipRange="1"
                :tooltipVolume="5"
                forceTooltipFixed
                :externalLabel="getDeskLayoutIcon"
                @onSliderRelease="onSliderRelease"
                @onSliderChange="onSliderChange"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="mt-16 mb-16">
        <div
          v-if="!isDesksProduct && configurationParams && areFeetAvailable"
          class="row middle-xs mb-16"
        >
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_feet") }}
            </p>
          </div>
          <div class="col-xs-8 inline-wrapper">
            <div style="display: inline-block">
              <toggleGroup
                v-model="feet"
                noUppercase
                :options="feetOptions"
              />
            </div>
            <div class="ml-8">
              <TylkoConfiguratorTooltip :isPositionFixed="isPositionFixed">
                <p class="text-grey-900 normal-14">
                  <template v-if="feetType === 'longLegs'">
                    {{ $t("cplus_legs_tooltip") }}
                    <br><br>
                    {{ $t("cplus_legs_tooltip_2") }}
                  </template>
                  <template v-else>
                    {{ $t("cplus_plinth_tooltip") }}
                    <br><br>
                    {{ $t("cplus_plinth_tooltip_2") }}
                  </template>
                </p>
              </TylkoConfiguratorTooltip>
            </div>
          </div>
        </div>
        <div
          v-if="configurationParams && areBackPanelsAvailable"
          class="mt-16 row middle-xs"
        >
          <div class="col-xs-3">
            <p
              class="normal-14 text-offblack-800"
              v-html="$t('common_configurator_backpanels')"
            />
          </div>
          <div
            class="flex col-xs-9 depth-wrapper toggle-group-fixed-height-wrapper middle-xs"
          >
            <template v-if="backPanelsOptions.every((o) => !o.disabled)">
              <toggleGroup
                v-model="backPanels"
                noUppercase
                :options="backPanelsOptions"
              />
            </template>
            <template v-else>
              <p class="text-offblack-600 normal-12">
                {{
                  $t(
                    isDesksProduct
                      ? "cplus_desks_back_panels"
                      : "common_configurator_backpanels_disclaimer"
                  )
                }}
              </p>
            </template>
          </div>
        </div>
      </div>
      <div class="mt-16">
        <ControlsMaterialsClassic
          v-if="(shelfType === 0 || shelfType === 2) && !isDesksProduct"
        />
        <div
          v-if="isDesksProduct || shelfType === 1"
          class="mt-8 row"
        >
          <div class="col-xs-3">
            <p class="normal-14 text-offblack-800">
              {{ $t("cplus_color") }}
              <span
                v-if="!isDesksProduct"
                class="bold-10 banner-new"
              >
                {{ $t("common_new") }}
              </span>
            </p>
          </div>
          <TylkoMaterials
            :materialsConfig="materialConfig"
            :activeMaterial="material"
            :handleMaterialUpdate="updateMaterial"
            colorTooltips
            :colorTranslations="colorTrans"
            :shelfType="shelfType"
            class="col-xs-9 start-xs"
          />
        </div>
        <div
          v-if="shelfType === 1 && isMaterialPink"
          class="row"
        >
          <div class="col-xs-3" />
          <div class="col-xs-9 start-xs">
            <LimitedEditionReisingerPinkHint />
          </div>
        </div>
      </div>
      <div class="row mt-8">
        <div class="col-xs-3" />
        <div class="col-xs-8 inline-wrapper">
          <OrderSamplesButton />
        </div>
      </div>
    </div>
    <div class="mt-8 mb-12 cplus-divider" />
    <div
      v-if="region !== '_other'"
      class="shopping-buttons-wrapper"
    >
      <AddToCartButton class="mb-8 orange-button" />
      <SaveForLaterButton
        :extraClasses="['white-button']"
        i18n="cplus_save_for_later"
      />
      <button
        v-if="showPresetButton"
        class="cta-button save-for-later white-button"
        @click="saveAsPreset"
      >
        SAVE AS PRESET
      </button>
    </div>
    <div
      v-else
      class="shopping-buttons-wrapper"
    >
      <p class="mb-8 normal-12 text-offblack-800">
        {{ $t("cplus_save_other_region_header") }}
      </p>
      <SaveForLaterButton
        :extraClasses="['white-button']"
        i18n="cplus_save_for_later"
      />
    </div>
    <ConfiguratorDeliveryDetails />
    <PaymentDetailsButton
      labelTranslationKey="cplus_payment_details_button"
      @onCTAClick="openCTAModal"
    />
    <Toolbar />
  </div>
</template>

<script>
import PaymentDetailsButton from '@configurator-common/components/ui/PaymentDetailsButton';
import LimitedEditionReisingerPinkHint from '@configurator-common/components/ui/ReisingerPink/LimitedEditionReisingerPinkHint';
import {
  ToggleGroup,
  TylkoConfiguratorTooltip,
  TylkoSlider,
  TylkoSliderControls,
  TylkoStepper,
  TylkoMaterials,
} from '@componentsConfigurator';
import ToggleFurnitureStyle from '@src_vue/Configurators/FrontCplusV2/components/ToggleFurnitureStyle';
import OrderSamplesButton from '@configurator-common/components/ui/OrderSamplesButton';
import ConfiguratorDeliveryDetails from '@configurator-common/components/ui/ConfiguratorDeliveryDetails';
import {
  addToCart,
  updateConfigurator,
  updateMaterial,
  saveAsPreset,
} from '../../mixins/storeComponentCommunication';
import ControlsDesktopPrice from './ControlsDekstopPrice';
import ControlsMaterialsClassic from './ControlsMaterialsClassic';
import Toolbar from './Toolbar';
import AddToCartButton from '../../components/AddToCartButton';
import SaveForLaterButton from '../../components/SaveForLaterButton';
import { chooseDeskLayoutSVG } from '../deskLayout';

export default {
  components: {
    ConfiguratorDeliveryDetails,
    ToggleFurnitureStyle,
    TylkoStepper,
    TylkoConfiguratorTooltip,
    TylkoSliderControls,
    ToggleGroup,
    ControlsDesktopPrice,
    AddToCartButton,
    Toolbar,
    TylkoSlider,
    PaymentDetailsButton,
    SaveForLaterButton,
    ControlsMaterialsClassic,
    TylkoMaterials,
    LimitedEditionReisingerPinkHint,
    OrderSamplesButton,
  },
  mixins: [updateConfigurator, updateMaterial, addToCart, saveAsPreset],
  data() {
    return {
      originName: window.location.origin,
      hideScrollableSectionButton: false,
    };
  },
  computed: {
    shelfType: {
      get() {
        return this.$store.getters.GET_SHELF_TYPE;
      },
    },
    materialConfig() {
      return this.$store.getters['commonMaterials/GET_MATERIAL_CONFIG'];
    },
    material() {
      return this.$store.getters['configuration/GET_MATERIAL'];
    },
    isMaterialPink() {
      return this.material === 15;
    },
    colorTrans() {
      return [...this.materialConfig.map(el => this.$t(el.translation))];
    },
    isPositionFixed() {
      return (
        this.shelfType === 0 || this.shelfType === 1 || this.shelfType === 2
      );
    },
    width: {
      get() {
        return this.$store.getters['configuration/GET_WIDTH'];
      },
      set({ value, release }) {
        this.isCanvasFocused = false;
        this.updateConfigurator('width', value, release);
      },
    },
    height: {
      get() {
        return this.$store.getters['configuration/GET_HEIGHT'];
      },
      set({ value, release }) {
        this.isCanvasFocused = false;
        this.updateConfigurator('height', value, release);
      },
    },
    distortion: {
      get() {
        return this.$store.getters['configuration/GET_DISTORTION'];
      },
      set({ value, release }) {
        this.isCanvasFocused = false;
        this.updateConfigurator('distortion', value, release);
      },
    },
    showPresetButton() {
      const url = new URL(window.location.href);
      return url.search.includes('haslo=okon');
    },
    additionalHeight() {
      // TODO Rework additionalValue into simple override, so this subtraction is no longer needed.
      const { height: actualHeight } = this.$store.getters['FPM/GET_GEOMETRY'];
      const configurationHeight = this.$store.getters['configuration/GET_HEIGHT'];
      return actualHeight / 10 - configurationHeight;
    },
    depth: {
      get() {
        return this.$store.getters['configuration/GET_DEPTH'];
      },
      set(newValue) {
        this.updateConfigurator('depth', newValue);
      },
    },
    backPanels: {
      get() {
        return this.$store.getters['configuration/GET_BACK_PANELS'];
      },
      set(value) {
        this.updateConfigurator('backPanels', value);
      },
    },
    feet: {
      get() {
        return this.$store.getters['configuration/GET_FEET'];
      },
      set(newValue) {
        this.updateConfigurator('feet', newValue);
      },
    },
    columnCount: {
      get() {
        return this.$store.getters['configuration/GET_COLUMN_COUNT'];
      },
      set(newValue) {
        this.updateConfigurator('columnCount', newValue);
        this.$store.dispatch('ui/UPDATE_COORDINATES_FOR_BUTTONS');
      },
    },
    isCanvasFocused: {
      get() {
        return this.$store.getters['ui/GET_IS_CANVAS_FOCUSED'];
      },
      set(newVal) {
        this.$store.dispatch('ui/UPDATE_IS_CANVAS_FOCUSED', newVal);
      },
    },
    legroomArea: {
      get() {
        return this.$store.getters['configuration/GET_LEGROOM_AREA'];
      },
      set({ value, release }) {
        this.isCanvasFocused = false;
        this.updateConfigurator('legroomArea', value, release);
      },
    },
    collectionType() {
      return this.$store.getters['FPM/GET_COLLECTION_TYPE'];
    },
    isDesk() {
      return this.collectionType === 'Desk';
    },
    isDressingTable() {
      return this.$store.getters['commonFurniture/GET_IS_DRESSING_TABLE'];
    },
    isDesksProduct() {
      return this.isDesk || this.isDressingTable;
    },
    configurationParams() {
      return this.$store.getters['ui/GET_CONFIGURATOR_PARAMS'];
    },
    widthRange() {
      return this.configurationParams.width.ranges[0];
    },
    heightRange() {
      return this.configurationParams.height.ranges[0];
    },
    distortionRange() {
      return this.configurationParams.distortion.ranges[0];
    },
    legroomAreaRange() {
      return this.configurationParams.legroomArea.ranges[0];
    },
    depthOptions() {
      return this.configurationParams.depth.options;
    },
    areFeetAvailable() {
      return this.configurationParams.feet.visible;
    },
    feetOptions() {
      const feetKey = this.feetType === 'longLegs' ? 'legs' : 'plinth';
      return this.configurationParams.feet.options.map((option, index) => ({
        ...option,
        label: this.$t(`cplus_${feetKey}_option_${index + 1}`),
      }));
    },
    feetType() {
      return this.configurationParams.feet.options[1].value;
    },
    isColumnCountAvailable() {
      return this.configurationParams.columnCount.visible;
    },
    isDistortionAvailable() {
      if (!this.configurationParams.distortion) return;
      return this.configurationParams.distortion.visible;
    },
    columnCountOptions() {
      return this.configurationParams.columnCount.options;
    },
    backPanelsOptions() {
      const labelDict = {
        false: 'cplus_backpanels_option1',
        true: 'cplus_backpanels_option2',
      };
      return this.configurationParams.backPanels.options.map(option => ({
        ...option,
        label: this.$t(labelDict[option.value]),
      }));
    },
    areBackPanelsAvailable() {
      return this.configurationParams.backPanels.visible;
    },
    cartLoader() {
      return this.$store.getters['ui/GET_CART_LOADER'];
    },
    region() {
      return window.cstm_i18n.region_name;
    },
    legroomSliderWidth() {
      let colWidth = 100;
      if (this.configurationParams && this.legroomAreaRange.step === 17) {
        colWidth = 84.5;
      } else if (
        this.configurationParams
        && this.legroomAreaRange.step === 20
      ) {
        colWidth = 69;
      } else if (
        this.configurationParams
        && this.legroomAreaRange.step === 25
      ) {
        colWidth = 53.5;
      } else if (
        this.configurationParams
        && this.legroomAreaRange.step === 34
      ) {
        colWidth = 38;
      }
      return `width: ${colWidth}%;`;
    },
    densityButton() {
      return `<a class="bold text-orange" target="_blank" href='${this.$t(
        'cplus_density_tips_url',
      )}'>${this.$t('cplus_density_info_link')}</a>`;
    },
  },
  methods: {
    onSliderRelease(isCanvasFocused = true) {
      this.isCanvasFocused = isCanvasFocused;
      this.$store.dispatch('renderer/UPDATE_RENDERER', true);
    },
    onSliderChange(val) {
      // force to updated camera every 30cm on +/- buttons
      if (!val && !(this.width % 300)) this.onSliderRelease(false);
      this.$store.dispatch('interactions/SET_IS_SLIDER_MOVING', val);
    },
    openCTAModal() {
      PubSub.publish('ecommerceServiceOpenPaymentInformationModal', {
        model: 'jetty',
        geom: this.$store.getters['FPM/GET_GEOMETRY'],
        price: this.$store.getters['commonPrices/GET_PRICE'],
        priceRaw: this.$store.getters['commonPrices/GET_RAW_PRICE'],
      });
    },
    handleAdjustSlider(payload) {
      PubSub.publish('atupaleLayer_configurator', {
        method: 'adjustSlider',
        payload,
      });
    },
    getDeskLayoutIcon(value) {
      if (this.configurationParams) {
        return chooseDeskLayoutSVG(value, this.legroomAreaRange);
      }
      return '_';
    },
  },
};
</script>
<style lang="scss" scoped>
#columnConfiguratorV2 {
  .fade-slow-leave-active {
    transition: opacity 0.2s ease-out;
  }
  .fade-slow-enter-active {
    transition: opacity 0.2s ease-in;
  }
  .fade-slow-enter,
  .fade-slow-leave-to {
    opacity: 0;
  }
}
</style>
